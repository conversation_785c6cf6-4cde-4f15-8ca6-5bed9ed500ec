#!/usr/bin/env python3
"""
MongoDB数据库初始化脚本
创建数据库、集合和索引
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.mongodb_models import MongoDBManager
from src.database.config import db_settings
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def init_mongodb():
    """初始化MongoDB数据库"""
    try:
        logger.info("开始初始化MongoDB数据库...")
        
        # 创建数据库管理器
        db_manager = MongoDBManager(
            connection_string=db_settings.get_mongodb_url(),
            database_name=db_settings.MONGODB_DATABASE
        )
        
        logger.info(f"连接到数据库: {db_settings.MONGODB_DATABASE}")
        
        # 验证连接
        db_info = db_manager.client.server_info()
        logger.info(f"MongoDB服务器版本: {db_info['version']}")
        
        # 检查集合是否存在
        collections = db_manager.db.list_collection_names()
        logger.info(f"现有集合: {collections}")
        
        # 创建示例数据（可选）
        create_sample_data = input("是否创建示例数据？(y/N): ").lower().strip() == 'y'
        
        if create_sample_data:
            create_sample_data_func(db_manager)
        
        # 显示数据库统计信息
        show_database_stats(db_manager)
        
        logger.info("MongoDB数据库初始化完成!")
        
    except Exception as e:
        logger.error(f"初始化数据库失败: {e}")
        raise
    finally:
        if 'db_manager' in locals():
            db_manager.close()

def create_sample_data_func(db_manager: MongoDBManager):
    """创建示例数据"""
    from datetime import datetime
    from src.database.mongodb_models import (
        OCRFile, FileMetadata, ProcessingHistoryEntry,
        BoundingBox, OCRRegion, RawOCRResult, PreprocessingParams,
        ProcessingStats, OCRResult
    )
    
    logger.info("创建示例数据...")
    
    try:
        # 创建示例文件记录
        sample_file = OCRFile(
            file_id=db_manager.generate_id(),
            original_filename="sample_document.jpg",
            file_path="/uploads/sample_document.jpg",
            file_size=1024000,
            file_type="jpg",
            mime_type="image/jpeg",
            file_hash=db_manager.generate_hash("sample_content"),
            upload_time=datetime.now(),
            status="completed",
            metadata=FileMetadata(
                width=1920,
                height=1080,
                pages=1,
                format="JPEG",
                color_mode="RGB"
            ),
            processing_history=[
                ProcessingHistoryEntry(
                    timestamp=datetime.now(),
                    status="completed",
                    message="OCR处理完成",
                    processing_time=2500
                )
            ],
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        db_manager.create_ocr_file(sample_file)
        logger.info("示例文件记录创建成功")
        
        # 创建示例OCR结果
        sample_result = OCRResult(
            result_id=db_manager.generate_id(),
            file_id=sample_file.file_id,
            annotation_id=None,
            processing_type="basic",
            page_number=1,
            regions=[
                OCRRegion(
                    region_id=db_manager.generate_id(),
                    region_type="single_column",
                    bbox=BoundingBox(x=100, y=100, width=800, height=200),
                    text="这是一个示例文本区域",
                    confidence=0.95,
                    order=1,
                    raw_ocr_results=[
                        RawOCRResult(
                            text="这是一个示例文本区域",
                            confidence=0.95,
                            bbox=[100, 100, 800, 200]
                        )
                    ],
                    preprocessing_params=PreprocessingParams(
                        clahe_clip_limit=2.0,
                        adaptive_threshold_block_size=11,
                        adaptive_threshold_c=2
                    )
                )
            ],
            processing_stats=ProcessingStats(
                total_regions=1,
                avg_confidence=0.95,
                processing_time=2500,
                ocr_engine_version="PaddleOCR-2.7"
            ),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        db_manager.create_ocr_result(sample_result)
        logger.info("示例OCR结果创建成功")
        
    except Exception as e:
        logger.error(f"创建示例数据失败: {e}")
        raise

def show_database_stats(db_manager: MongoDBManager):
    """显示数据库统计信息"""
    logger.info("=== 数据库统计信息 ===")
    
    try:
        # 获取所有集合的文档数量
        collections = [
            ("ocr_files", db_manager.ocr_files),
            ("ocr_results", db_manager.ocr_results),
            ("ocr_annotations", db_manager.ocr_annotations),
            ("ocr_training_samples", db_manager.ocr_training_samples),
            ("ocr_preprocessing_effectiveness", db_manager.ocr_preprocessing_effectiveness),
            ("ocr_error_patterns", db_manager.ocr_error_patterns),
            ("ocr_learning_reports", db_manager.ocr_learning_reports)
        ]
        
        for name, collection in collections:
            count = collection.count_documents({})
            logger.info(f"{name}: {count} 个文档")
        
        # 获取数据库大小信息
        stats = db_manager.db.command("dbStats")
        logger.info(f"数据库大小: {stats.get('dataSize', 0) / 1024 / 1024:.2f} MB")
        logger.info(f"索引大小: {stats.get('indexSize', 0) / 1024 / 1024:.2f} MB")
        
    except Exception as e:
        logger.error(f"获取数据库统计信息失败: {e}")

def drop_database():
    """删除数据库（谨慎使用）"""
    confirm = input(f"确认删除数据库 '{db_settings.MONGODB_DATABASE}'？这将删除所有数据！(yes/NO): ")
    
    if confirm.lower() == 'yes':
        try:
            db_manager = MongoDBManager(
                connection_string=db_settings.get_mongodb_url(),
                database_name=db_settings.MONGODB_DATABASE
            )
            
            db_manager.client.drop_database(db_settings.MONGODB_DATABASE)
            logger.info(f"数据库 '{db_settings.MONGODB_DATABASE}' 已删除")
            
        except Exception as e:
            logger.error(f"删除数据库失败: {e}")
            raise
        finally:
            if 'db_manager' in locals():
                db_manager.close()
    else:
        logger.info("取消删除操作")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="MongoDB数据库初始化脚本")
    parser.add_argument("--drop", action="store_true", help="删除数据库")
    parser.add_argument("--stats", action="store_true", help="仅显示统计信息")
    
    args = parser.parse_args()
    
    if args.drop:
        drop_database()
    elif args.stats:
        try:
            db_manager = MongoDBManager(
                connection_string=db_settings.get_mongodb_url(),
                database_name=db_settings.MONGODB_DATABASE
            )
            show_database_stats(db_manager)
        finally:
            if 'db_manager' in locals():
                db_manager.close()
    else:
        init_mongodb() 
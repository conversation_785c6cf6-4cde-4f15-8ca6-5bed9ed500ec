#!/usr/bin/env python3
"""
MongoDB集合创建脚本
使用项目配置系统创建所有必需的集合和索引
支持通过.env文件配置MongoDB连接参数
"""

import sys
from pathlib import Path
from pymongo import MongoClient, ASCENDING, DESCENDING
from pymongo.errors import CollectionInvalid, OperationFailure
import logging

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入项目配置
from src.backend.config import settings

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_mongodb_config():
    """从项目配置获取MongoDB配置"""
    mongodb_uri = settings.MONGODB_URL
    mongo_db_name = settings.MONGODB_DATABASE
    
    logger.info(f"使用 MongoDB URI: {mongodb_uri}")
    logger.info(f"使用数据库名称: {mongo_db_name}")
    
    return mongodb_uri, mongo_db_name

def create_collections_and_indexes(db):
    """创建所有集合和索引"""
    
    collections_config = {
        'ocr_files': {
            'description': '存储上传的原始文件信息和处理状态',
            'indexes': [
                # 普通索引
                {'keys': [('file_id', ASCENDING)], 'name': 'file_id_idx'},
                {'keys': [('file_hash', ASCENDING)], 'name': 'file_hash_idx'},
                # 复合索引
                {'keys': [('status', ASCENDING), ('upload_time', DESCENDING)], 'name': 'status_upload_time'},
                {'keys': [('file_type', ASCENDING), ('created_at', DESCENDING)], 'name': 'file_type_created_at'},
            ]
        },
        
        'ocr_results': {
            'description': '存储OCR识别的详细结果',
            'indexes': [
                # 普通索引
                {'keys': [('result_id', ASCENDING)], 'name': 'result_id_idx'},
                # 复合索引
                {'keys': [('file_id', ASCENDING), ('page_number', ASCENDING)], 'name': 'file_id_page_number'},
                {'keys': [('processing_type', ASCENDING), ('created_at', DESCENDING)], 'name': 'processing_type_created_at'},
                {'keys': [('regions.region_type', ASCENDING)], 'name': 'regions_region_type'},
            ]
        },
        
        'ocr_annotations': {
            'description': '存储人工标注的区域信息',
            'indexes': [
                # 普通索引
                {'keys': [('annotation_id', ASCENDING)], 'name': 'annotation_id_idx'},
                # 复合索引
                {'keys': [('file_id', ASCENDING), ('page_number', ASCENDING)], 'name': 'file_id_page_number'},
                {'keys': [('template_info.is_template', ASCENDING), ('template_info.template_name', ASCENDING)], 'name': 'template_info'},
                {'keys': [('created_at', DESCENDING)], 'name': 'created_at_idx'},
            ]
        },
        
        'ocr_training_samples': {
            'description': '存储学习系统的训练样本',
            'indexes': [
                # 普通索引
                {'keys': [('sample_id', ASCENDING)], 'name': 'sample_id_idx'},
                # 复合索引
                {'keys': [('region_type', ASCENDING), ('created_at', DESCENDING)], 'name': 'region_type_created_at'},
                {'keys': [('file_id', ASCENDING), ('region_id', ASCENDING)], 'name': 'file_id_region_id'},
                {'keys': [('feedback_source', ASCENDING), ('accuracy_metrics.character_accuracy', DESCENDING)], 'name': 'feedback_source_accuracy'},
            ]
        },
        
        'ocr_preprocessing_effectiveness': {
            'description': '存储不同预处理参数的效果评估',
            'indexes': [
                # 普通索引
                {'keys': [('effectiveness_id', ASCENDING)], 'name': 'effectiveness_id_idx'},
                # 复合索引
                {'keys': [('region_type', ASCENDING), ('performance_metrics.combined_score', DESCENDING)], 'name': 'region_type_combined_score'},
                {'keys': [('evaluation_period.start_date', ASCENDING), ('evaluation_period.end_date', ASCENDING)], 'name': 'evaluation_period'},
            ]
        },
        
        'ocr_error_patterns': {
            'description': '存储识别的错误模式和统计信息',
            'indexes': [
                # 普通索引
                {'keys': [('pattern_id', ASCENDING)], 'name': 'pattern_id_idx'},
                # 复合索引
                {'keys': [('region_type', ASCENDING), ('error_type', ASCENDING)], 'name': 'region_type_error_type'},
                {'keys': [('statistics.frequency', DESCENDING), ('statistics.last_occurrence', DESCENDING)], 'name': 'frequency_last_occurrence'},
            ]
        },
        
        'ocr_learning_reports': {
            'description': '存储学习系统生成的报告',
            'indexes': [
                # 普通索引
                {'keys': [('report_id', ASCENDING)], 'name': 'report_id_idx'},
                # 复合索引
                {'keys': [('report_type', ASCENDING), ('generated_at', DESCENDING)], 'name': 'report_type_generated_at'},
                {'keys': [('time_range.start_date', ASCENDING), ('time_range.end_date', ASCENDING)], 'name': 'time_range'},
            ]
        }
    }
    
    created_collections = []
    created_indexes = []
    
    for collection_name, config in collections_config.items():
        try:
            # 创建集合（如果不存在）
            if collection_name not in db.list_collection_names():
                db.create_collection(collection_name)
                logger.info(f"✅ 创建集合: {collection_name}")
                created_collections.append(collection_name)
            else:
                logger.info(f"📋 集合已存在: {collection_name}")
            
            # 获取集合对象
            collection = db[collection_name]
            
            # 创建索引
            for index_config in config['indexes']:
                try:
                    index_name = collection.create_index(
                        index_config['keys'],
                        name=index_config['name']
                    )
                    logger.info(f"  ✅ 创建索引: {collection_name}.{index_config['name']}")
                    created_indexes.append(f"{collection_name}.{index_config['name']}")
                except OperationFailure as e:
                    if "already exists" in str(e):
                        logger.info(f"  📋 索引已存在: {collection_name}.{index_config['name']}")
                    else:
                        logger.error(f"  ❌ 创建索引失败: {collection_name}.{index_config['name']} - {e}")
            
            logger.info(f"  📝 {config['description']}")
            
        except CollectionInvalid as e:
            logger.error(f"❌ 创建集合失败: {collection_name} - {e}")
        except Exception as e:
            logger.error(f"❌ 处理集合时出错: {collection_name} - {e}")
    
    return created_collections, created_indexes

def validate_collections(db):
    """验证集合和索引是否正确创建"""
    logger.info("\n🔍 验证集合和索引...")
    
    expected_collections = [
        'ocr_files', 'ocr_results', 'ocr_annotations', 
        'ocr_training_samples', 'ocr_preprocessing_effectiveness', 
        'ocr_error_patterns', 'ocr_learning_reports'
    ]
    
    existing_collections = db.list_collection_names()
    
    for collection_name in expected_collections:
        if collection_name in existing_collections:
            collection = db[collection_name]
            indexes = list(collection.list_indexes())
            logger.info(f"✅ {collection_name}: {len(indexes)} 个索引")
            for index in indexes:
                logger.info(f"  - {index['name']}: {index.get('key', {})}")
        else:
            logger.error(f"❌ 缺少集合: {collection_name}")

def show_database_stats(db):
    """显示数据库统计信息"""
    logger.info("\n📊 数据库统计信息:")
    
    stats = db.command("dbStats")
    logger.info(f"数据库名称: {stats['db']}")
    logger.info(f"集合数量: {stats['collections']}")
    logger.info(f"索引数量: {stats['indexes']}")
    logger.info(f"数据大小: {stats.get('dataSize', 0)} 字节")
    logger.info(f"存储大小: {stats.get('storageSize', 0)} 字节")

def main():
    """主函数"""
    try:
        # 获取配置
        mongodb_uri, mongo_db_name = get_mongodb_config()
        logger.info(f"连接到 MongoDB: {mongo_db_name}")
        
        # 连接数据库
        client = MongoClient(mongodb_uri)
        db = client[mongo_db_name]
        
        # 测试连接
        client.admin.command('ping')
        logger.info("✅ MongoDB 连接成功")
        
        # 创建集合和索引
        logger.info("\n🚀 开始创建集合和索引...")
        created_collections, created_indexes = create_collections_and_indexes(db)
        
        # 验证创建结果
        validate_collections(db)
        
        # 显示统计信息
        show_database_stats(db)
        
        # 总结
        logger.info(f"\n🎉 完成!")
        logger.info(f"新创建的集合: {len(created_collections)} 个")
        logger.info(f"新创建的索引: {len(created_indexes)} 个")
        
        if created_collections:
            logger.info(f"新集合: {', '.join(created_collections)}")
        if created_indexes:
            logger.info(f"新索引: {', '.join(created_indexes)}")
        
    except Exception as e:
        logger.error(f"❌ 脚本执行失败: {e}")
        sys.exit(1)
    finally:
        if 'client' in locals():
            client.close()
            logger.info("🔌 MongoDB 连接已关闭")

if __name__ == "__main__":
    main() 
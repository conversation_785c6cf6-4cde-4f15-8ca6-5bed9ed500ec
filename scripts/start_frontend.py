#!/usr/bin/env python3
"""
前端项目启动脚本
自动检查依赖并启动开发服务器
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_colored(text: str, color: str = 'white') -> None:
    """打印彩色文本"""
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'white': '\033[97m',
        'end': '\033[0m'
    }
    print(f"{colors.get(color, colors['white'])}{text}{colors['end']}")

def run_command(command: str, cwd: str = None) -> tuple[int, str, str]:
    """运行shell命令并返回结果"""
    try:
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=cwd,
            text=True
        )
        stdout, stderr = process.communicate()
        return process.returncode, stdout, stderr
    except Exception as e:
        return 1, '', str(e)

def check_node_installed() -> bool:
    """检查是否安装了Node.js"""
    code, _, _ = run_command('node --version')
    return code == 0

def check_npm_installed() -> bool:
    """检查是否安装了npm"""
    code, _, _ = run_command('npm --version')
    return code == 0

def install_dependencies(frontend_dir: str) -> bool:
    """安装前端依赖"""
    print_colored('正在安装前端依赖...', 'blue')
    code, stdout, stderr = run_command('npm install', cwd=frontend_dir)
    if code != 0:
        print_colored('依赖安装失败！', 'red')
        print_colored(f'错误信息：\n{stderr}', 'red')
        return False
    print_colored('依赖安装成功！', 'green')
    return True

def start_dev_server(frontend_dir: str) -> None:
    """启动开发服务器"""
    print_colored('正在启动开发服务器...', 'blue')
    print_colored('开发服务器将在 http://localhost:3000 启动', 'yellow')
    print_colored('按 Ctrl+C 停止服务器\n', 'yellow')
    
    try:
        process = subprocess.Popen(
            'npm run dev',
            shell=True,
            cwd=frontend_dir
        )
        process.wait()
    except KeyboardInterrupt:
        print_colored('\n正在停止服务器...', 'yellow')
        process.terminate()
        process.wait()
        print_colored('服务器已停止', 'green')
    except Exception as e:
        print_colored(f'启动服务器时发生错误：{str(e)}', 'red')
        sys.exit(1)

def main():
    # 获取前端目录路径
    script_dir = Path(__file__).resolve().parent
    frontend_dir = script_dir.parent / 'frontend'
    
    if not frontend_dir.exists():
        print_colored(f'错误：找不到前端目录 {frontend_dir}', 'red')
        sys.exit(1)

    # 检查必要工具
    if not check_node_installed():
        print_colored('错误：未安装Node.js，请先安装Node.js', 'red')
        sys.exit(1)

    if not check_npm_installed():
        print_colored('错误：未安装npm，请先安装npm', 'red')
        sys.exit(1)

    # 安装依赖
    if not install_dependencies(str(frontend_dir)):
        sys.exit(1)

    # 启动服务器
    start_dev_server(str(frontend_dir))

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print_colored('\n操作已取消', 'yellow')
        sys.exit(0)
    except Exception as e:
        print_colored(f'发生错误：{str(e)}', 'red')
        sys.exit(1) 
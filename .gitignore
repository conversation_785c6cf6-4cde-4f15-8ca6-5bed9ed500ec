# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Dependency directories
node_modules/

# Environment variables
.env

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store

# Task files
tasks.json
tasks/ 

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
venv/
.env

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDEs and editors
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Docker
.docker/
docker-compose.override.yml

# Project specific
uploads/
processed/
models/
.taskmaster/ 

# Test files and temporary files
test_*.py
*_test.py
update_*.py
quick_test.py
show_*.py

# OCR and image processing temporary files
initial_ocr_results_*.json
*_annotation_results.json
ocr_test_results.json
layout_analysis_*.jpg
image_with_annotations.*
test_image.*

# Cache and temporary directories
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version 
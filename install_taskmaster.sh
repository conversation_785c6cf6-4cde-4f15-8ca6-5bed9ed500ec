#!/bin/bash

echo "开始安装 Task Master..."

# 确保 ~/.cursor 目录存在
mkdir -p ~/.cursor

# 创建 MCP 配置文件
cat > ~/.cursor/mcp.json << 'EOF'
{
  "mcpServers": {
    "taskmaster-ai": {
      "command": "npx",
      "args": ["-y", "--package=task-master-ai", "task-master-ai"],
      "env": {
        "ANTHROPIC_API_KEY": "",
        "OPENAI_API_KEY": "",
        "GOOGLE_API_KEY": "",
        "PERPLEXITY_API_KEY": "",
        "MISTRAL_API_KEY": "",
        "OPENROUTER_API_KEY": "",
        "XAI_API_KEY": "",
        "AZURE_OPENAI_API_KEY": ""
      }
    }
  }
}
EOF

# 创建项目结构
mkdir -p .taskmaster/docs
mkdir -p .taskmaster/templates

# 创建示例 PRD 模板
cat > .taskmaster/templates/example_prd.txt << 'EOF'
# 项目需求文档 (PRD)

## 项目概述
[在此描述项目的主要目标和功能]

## 功能需求
1. [功能1]
2. [功能2]
3. [功能3]

## 技术要求
- [技术要求1]
- [技术要求2]
- [技术要求3]

## 交付时间线
- [里程碑1]
- [里程碑2]
- [里程碑3]
EOF

# 安装依赖
npm install task-master-ai

echo "Task Master 安装完成！"
echo "请在 ~/.cursor/mcp.json 中配置您的 API keys"
echo "然后在 Cursor 中启用 Task Master (Cmd+Shift+J -> MCP -> 启用 task-master-ai)" 
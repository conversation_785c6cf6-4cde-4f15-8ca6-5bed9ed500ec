# OCR 相关
paddlepaddle>=2.3.0
paddleocr>=2.6.0
opencv-python>=********
Pillow>=8.3.2
PyMuPDF>=1.22.0  # fitz 包
pillow-heif>=0.13.0  # HEIC/HEIF 格式支持

# 文本处理
python-docx>=1.0.0
Markdown>=3.4.0
python-frontmatter>=1.0.0

# Web 框架
fastapi>=0.68.0
uvicorn>=0.15.0
pydantic>=1.9.0
pydantic-settings>=2.0.0
python-multipart>=0.0.5

# 数据处理
numpy>=1.21.0
pandas>=2.0.0

# 数据库
pymongo>=4.0.1
motor>=2.5.1
redis>=4.3.4
aioredis==2.0.1
minio>=7.1.0

# 消息队列
aio-pika>=8.2.0

# 工具
python-dotenv>=0.19.0
requests>=2.26.0
tqdm>=4.66.0
python-magic>=0.4.27  # 文件类型检测
aiofiles>=0.8.0
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
email-validator>=1.1.3
python-dateutil>=2.8.2
aiohttp>=3.8.1
psutil>=5.8.0
python-magic>=0.4.27  # 文件类型检测

# 测试
pytest>=7.0.0
pytest-cov>=3.0.0
pytest-asyncio>=0.18.0
httpx>=0.18.2

# 系统监控和性能分析
prometheus-client>=0.12.0
structlog>=21.1.0

# 机器学习和深度学习
torch>=2.0.0
torchvision>=0.15.0
scikit-learn>=1.3.0
optuna>=3.4.0  # 超参数优化
transformers>=4.30.0  # HuggingFace模型
tokenizers>=0.13.0

# JWT认证
PyJWT>=2.8.0

# ONNX Runtime相关
onnx>=1.10.1
onnxruntime>=1.8.1
# onnxruntime-gpu>=1.16.0  # GPU版本（如果需要GPU支持，取消注释）
coloredlogs>=15.0.1  # 美化日志输出
protobuf>=4.24.0  # ONNX模型序列化

# 配置和工具
PyYAML>=6.0.0  # 用于配置文件 

# 新增依赖项
pillow>=9.0.0
pyheif>=0.7.0
pymupdf>=1.23.0

# 开发工具
black>=21.7b0
flake8>=3.9.2
mypy>=0.910
isort>=5.9.3

hiredis==2.2.3
redis==4.6.0

# 文件处理相关依赖
aiofiles>=0.8.0
python-multipart>=0.0.5
aiohttp>=3.8.1
watchdog>=2.1.9

# 文档转换相关依赖
python-docx>=0.8.11
pdfkit>=1.0.0
markdown>=3.3.7
mammoth>=1.5.1
beautifulsoup4>=4.9.3
pandas>=1.3.0
openpyxl>=3.0.7
wkhtmltopdf>=0.2

# PaddleLabel 相关依赖
paddlex 
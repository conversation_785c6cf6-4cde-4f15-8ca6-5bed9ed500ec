global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "/etc/prometheus/rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - "alertmanager:9093"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'gateway'
    static_configs:
      - targets: ['gateway:8000']
    metrics_path: '/metrics'

  - job_name: 'upload'
    static_configs:
      - targets: ['upload:8001']
    metrics_path: '/metrics'

  - job_name: 'file-management'
    static_configs:
      - targets: ['file-management:8002']
    metrics_path: '/metrics'

  - job_name: 'ocr'
    static_configs:
      - targets: ['ocr:8003']
    metrics_path: '/metrics'

  - job_name: 'quality-check'
    static_configs:
      - targets: ['quality-check:8004']
    metrics_path: '/metrics'

  - job_name: 'batch-process'
    static_configs:
      - targets: ['batch-process:8005']
    metrics_path: '/metrics'

  - job_name: 'node_exporter'
    static_configs:
      - targets: ['node_exporter:9100'] 
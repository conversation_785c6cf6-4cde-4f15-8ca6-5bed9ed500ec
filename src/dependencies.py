from typing import AsyncGenerator
from fastapi import Depends

from src.services.performance_monitoring_service import PerformanceMonitoringService
from src.backend.config import get_settings

# Singleton instance
_performance_monitoring_service = None

async def get_performance_monitoring_service() -> AsyncGenerator[PerformanceMonitoringService, None]:
    """Get or create the performance monitoring service singleton"""
    global _performance_monitoring_service
    
    if _performance_monitoring_service is None:
        config = get_settings()
        _performance_monitoring_service = PerformanceMonitoringService(
            mongodb_url=config.get_mongodb_url(),
            redis_host=config.REDIS_HOST,
            redis_port=config.REDIS_PORT,
            collection_interval=60,  # 默认60秒
            retention_days=7,  # 默认保留7天
            thresholds={
                'cpu': config.CPU_THRESHOLD,
                'memory': config.MEMORY_THRESHOLD,
                'disk': config.DISK_THRESHOLD
            }
        )
        
    try:
        yield _performance_monitoring_service
    finally:
        # No cleanup needed for now, but could be added here if required
        pass 
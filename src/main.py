"""
主应用程序
"""

import asyncio
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from routers import (
    ocr_router,
    db_optimization_router,
    model_optimization_router,
    load_testing_router,
    performance_monitoring_router
)
from tasks.performance_monitoring_task import PerformanceMonitoringTask
from src.backend.logger import get_logger

logger = get_logger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="PaddleOCR Service",
    description="PaddleOCR intelligent document processing service",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

# 注册路由
app.include_router(ocr_router.router)
app.include_router(db_optimization_router.router)
app.include_router(model_optimization_router.router)
app.include_router(load_testing_router.router)
app.include_router(performance_monitoring_router.router)

# 性能监控任务
monitoring_task = PerformanceMonitoringTask()

@app.on_event("startup")
async def startup_event():
    """应用启动时执行"""
    try:
        # 启动性能监控任务
        asyncio.create_task(monitoring_task.run())
        logger.info("Performance monitoring task started")
        
    except Exception as e:
        logger.error(f"Failed to start performance monitoring task: {str(e)}")
        
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时执行"""
    try:
        # 停止性能监控任务
        monitoring_task.stop()
        logger.info("Performance monitoring task stopped")
        
    except Exception as e:
        logger.error(f"Failed to stop performance monitoring task: {str(e)}")
        
@app.get("/")
async def root():
    """根路由"""
    return {
        "message": "PaddleOCR Service is running",
        "version": "1.0.0"
    } 
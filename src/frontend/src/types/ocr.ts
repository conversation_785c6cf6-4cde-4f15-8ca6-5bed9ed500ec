// 区域类型定义
export type RegionType = 'single_column' | 'left_column' | 'right_column' | 'table' | 'image' | 'header' | 'footer' | 'special';

export const REGION_TYPES: Record<RegionType, { name: string; color: string }> = {
  single_column: { name: '单栏文本', color: '#10B981' },
  left_column: { name: '左栏文本', color: '#3B82F6' },
  right_column: { name: '右栏文本', color: '#EF4444' },
  table: { name: '表格', color: '#F59E0B' },
  image: { name: '图像', color: '#8B5CF6' },
  header: { name: '页眉', color: '#06B6D4' },
  footer: { name: '页脚', color: '#84CC16' },
  special: { name: '特殊区域', color: '#F97316' }
};

export interface OCRBox {
  x: number;
  y: number;
  width: number;
  height: number;
  text?: string;
  type?: RegionType;
  confidence?: number;
} 
'use client';

import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Upload } from "lucide-react";
import { useRef } from "react";

interface Document {
  id: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  uploadDate: string;
}

interface UploadButtonProps {
  onUploadSuccess: (documents: Document[]) => void;
}

export function UploadButton({ onUploadSuccess }: UploadButtonProps) {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const convertHeicToJpeg = async (file: File): Promise<File> => {
    if (file.type === "image/heic" || file.name.toLowerCase().endsWith(".heic")) {
      try {
        const heic2any = (await import("heic2any")).default;
        const jpegBlob = await heic2any({
          blob: file,
          toType: "image/jpeg",
          quality: 0.9,
        });
        return new File(
          [jpegBlob instanceof Blob ? jpegBlob : jpegBlob[0]], 
          file.name.replace(/\.heic$/i, ".jpg"),
          { type: "image/jpeg" }
        );
      } catch (error) {
        console.error("HEIC conversion error:", error);
        throw new Error("HEIC文件转换失败");
      }
    }
    return file;
  };

  const handleUpload = async (files: FileList) => {
    try {
      const uploadedDocs: Document[] = [];
      const totalFiles = files.length;
      let processedFiles = 0;

      for (const file of Array.from(files)) {
        try {
          const processedFile = await convertHeicToJpeg(file);
          const formData = new FormData();
          formData.append("file", processedFile);

          const response = await fetch("/api/documents/upload", {
            method: "POST",
            body: formData,
          });

          if (!response.ok) {
            throw new Error(`文件 ${file.name} 上传失败`);
          }

          const data = await response.json();
          uploadedDocs.push({
            id: data.id,
            fileName: processedFile.name,
            fileType: processedFile.type,
            fileSize: processedFile.size,
            uploadDate: new Date().toISOString(),
          });

          processedFiles++;
          
          if (processedFiles === totalFiles) {
            toast({
              title: "上传完成",
              description: `成功上传 ${uploadedDocs.length} 个文件`,
            });
          }
        } catch (error) {
          console.error(`Error uploading ${file.name}:`, error);
          toast({
            title: "上传失败",
            description: error instanceof Error ? error.message : `文件 ${file.name} 上传失败`,
            variant: "destructive",
          });
        }
      }

      if (uploadedDocs.length > 0) {
        onUploadSuccess(uploadedDocs);
      }
    } catch (error) {
      toast({
        title: "上传失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      });
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      handleUpload(files);
    }
    // 重置input以允许上传相同文件
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        className="hidden"
        accept=".pdf,.png,.jpg,.jpeg,.heic"
        multiple
      />
      <Button
        onClick={() => fileInputRef.current?.click()}
        className="mb-4"
      >
        <Upload className="mr-2 h-4 w-4" />
        上传文件
      </Button>
    </>
  );
} 
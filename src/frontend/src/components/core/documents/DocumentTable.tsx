import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatFileSize, formatDate } from "@/lib/utils";
import Image from "next/image";
import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog";
import { Trash2, Eye, Pencil } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { DocumentViewer } from "./DocumentViewer";
import { AnnotationEditor } from "./AnnotationEditor";
import { Document } from "@/lib/types";
import { SERVICES } from "@/config";

interface DocumentTableProps {
  documents: Document[];
  onDocumentDelete?: (id: string) => void;
}

export function DocumentTable({ documents, onDocumentDelete }: DocumentTableProps) {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const { toast } = useToast();

  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`${SERVICES.API.BASE_URL}/documents/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("删除文件失败");
      }

      toast({
        title: "删除成功",
        description: "文件已成功删除",
      });

      if (onDocumentDelete) {
        onDocumentDelete(id);
      }
    } catch (error) {
      toast({
        title: "删除失败",
        description: error instanceof Error ? error.message : "删除文件时出错",
        variant: "destructive",
      });
    }
  };

  const handleView = (document: Document) => {
    setSelectedDocument(document);
    setIsViewerOpen(true);
  };

  const handleEdit = (document: Document) => {
    setSelectedDocument(document);
    setIsEditorOpen(true);
  };

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>文件名</TableHead>
              <TableHead>类型</TableHead>
              <TableHead>大小</TableHead>
              <TableHead>上传时间</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {documents.map((doc) => (
              <TableRow key={doc._id}>
                <TableCell>{doc.original_filename}</TableCell>
                <TableCell>{doc.file_type}</TableCell>
                <TableCell>{formatFileSize(doc.file_size)}</TableCell>
                <TableCell>{formatDate(doc.upload_time)}</TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleView(doc)}
                      disabled={doc.status !== 'processed'}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      查看文档
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(doc)}
                      disabled={doc.status !== 'processed'}
                    >
                      <Pencil className="h-4 w-4 mr-1" />
                      编辑标注
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDelete(doc._id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* 预览图片弹窗 */}
      <Dialog open={!!selectedImage} onOpenChange={() => setSelectedImage(null)}>
        <DialogContent className="max-w-3xl">
          {selectedImage && (
            <div className="relative h-[600px] w-full">
              <Image
                src={selectedImage}
                alt="标注大图"
                fill
                className="object-contain"
              />
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 查看文档弹窗 */}
      <Dialog open={isViewerOpen} onOpenChange={setIsViewerOpen}>
        <DialogContent className="max-w-6xl">
          <DialogHeader>
            <DialogTitle>查看文档</DialogTitle>
          </DialogHeader>
          {selectedDocument && (
            <DocumentViewer
              document={selectedDocument}
              onClose={() => setIsViewerOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* 编辑标注弹窗 */}
      <Dialog open={isEditorOpen} onOpenChange={setIsEditorOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>编辑标注</DialogTitle>
          </DialogHeader>
          {selectedDocument && (
            <AnnotationEditor
              document={selectedDocument}
              onClose={() => setIsEditorOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  );
} 
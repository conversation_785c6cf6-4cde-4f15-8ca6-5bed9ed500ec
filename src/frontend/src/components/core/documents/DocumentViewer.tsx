"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ZoomIn, ZoomOut, FileText, Image as ImageIcon, RefreshC<PERSON>, Brain, Table } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Image from "next/image";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Document } from '@/lib/types';
import { formatFileSize, formatDate } from '@/lib/utils';

interface DocumentViewerProps {
  document: Document;
  onClose?: () => void;
}

interface OCRModelResult {
  model_name: string;
  model_type: string;
  regions: Array<{
    text: string;
    confidence: number;
    box: number[][];
    region_type: string;
    structure_type?: string;
    table_html?: string;
  }>;
  markdown_text: string;
  total_regions: number;
  error?: string;
}

interface OCRResults {
  image_size: {
    width: number;
    height: number;
  };
  models: {
    pp_ocrv5?: OCRModelResult;
    pp_structurev3?: OCRModelResult;
  };
}

interface DocumentDetails extends Document {
  annotations?: Array<{
    id: string;
    text: string;
    bbox: [number, number, number, number];
    confidence: number;
  }>;
  image_path?: string;
  markdown_content?: string;
  ocr_results?: OCRResults;
}

export function DocumentViewer({ document, onClose }: DocumentViewerProps) {
  const [zoom, setZoom] = useState(1);
  const [documentDetails, setDocumentDetails] = useState<DocumentDetails | null>(null);
  const [ocrResults, setOcrResults] = useState<OCRResults | null>(null);
  const [loading, setLoading] = useState(true);
  const [ocrLoading, setOcrLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeModel, setActiveModel] = useState<'pp_ocrv5' | 'pp_structurev3'>('pp_ocrv5');


  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.1, 3));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.1, 0.3));
  };

  const handleZoomReset = () => {
    setZoom(1);
  };

  // 获取文档详情
  const fetchDocumentDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/documents/${document._id}`);
      if (!response.ok) {
        throw new Error(`获取文档详情失败: ${response.status}`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.message || '获取文档详情失败');
      }

      // 生成markdown内容
      const markdownContent = generateMarkdownContent(result.data);

      setDocumentDetails({
        ...result.data,
        markdown_content: markdownContent
      });

      // 如果有OCR结果，设置OCR结果
      if (result.data.ocr_results) {
        setOcrResults(result.data.ocr_results);
      }
    } catch (err) {
      console.error('获取文档详情失败:', err);
      setError(err instanceof Error ? err.message : '获取文档详情失败');
      // 如果获取详情失败，使用基本文档信息
      setDocumentDetails({
        ...document,
        markdown_content: generateMarkdownContent(document)
      });
    } finally {
      setLoading(false);
    }
  };

  // 获取OCR结果
  const fetchOCRResults = async () => {
    try {
      setOcrLoading(true);
      const response = await fetch(`/api/documents/${document._id}/ocr`);
      if (!response.ok) {
        throw new Error(`获取OCR结果失败: ${response.status}`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.message || '获取OCR结果失败');
      }

      setOcrResults(result.data.ocr_results);
    } catch (err) {
      console.error('获取OCR结果失败:', err);
    } finally {
      setOcrLoading(false);
    }
  };

  // 生成markdown内容
  const generateMarkdownContent = (doc: Document) => {
    return `# ${doc.original_filename || doc.filename}

## 文档信息

- **文件ID**: ${doc._id}
- **原始文件名**: ${doc.original_filename || doc.filename}
- **文件大小**: ${formatFileSize(doc.file_size)}
- **上传时间**: ${formatDate(doc.upload_time)}
- **处理状态**: ${doc.status}
- **文件路径**: ${doc.file_path}

## OCR识别结果

${doc.ocr_text || '暂无OCR文本内容'}

## 处理详情

- **文件哈希**: ${doc.file_hash}
- **处理状态**: ${doc.processing_status}
- **是否删除**: ${doc.deleted ? '是' : '否'}

---

*文档查看器 - OCR智能文档处理系统*
`;
  };

  // 生成模型特定的markdown内容
  const generateModelMarkdownContent = (modelResult: OCRModelResult) => {
    if (modelResult.error) {
      return `# ${modelResult.model_name} 处理结果

## 错误信息
${modelResult.error}

---
*模型类型: ${modelResult.model_type}*
`;
    }

    return `# ${modelResult.model_name} 处理结果

## 模型信息
- **模型名称**: ${modelResult.model_name}
- **模型类型**: ${modelResult.model_type}
- **识别区域数**: ${modelResult.total_regions}

## 识别内容

${modelResult.markdown_text || '暂无识别内容'}

---
*模型类型: ${modelResult.model_type}*
`;
  };

  useEffect(() => {
    fetchDocumentDetails();
    // 如果没有OCR结果，尝试获取
    if (!ocrResults) {
      fetchOCRResults();
    }
  }, [document._id]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[600px]">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-4" />
          <p className="text-gray-600">加载文档详情中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-[600px]">
        <div className="text-center">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchDocumentDetails} variant="outline">
            重新加载
          </Button>
        </div>
      </div>
    );
  }

  if (!documentDetails) {
    return (
      <div className="flex items-center justify-center h-[600px]">
        <p className="text-gray-500">文档详情不可用</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full max-h-[80vh]">
      {/* 工具栏 */}
      <div className="flex justify-between items-center mb-4 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-semibold truncate max-w-md">
            {documentDetails.original_filename || documentDetails.filename}
          </h3>
          <Badge variant="secondary">
            {documentDetails.status === 'completed' ? '已完成' : documentDetails.status}
          </Badge>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={handleZoomOut} title="缩小">
            <ZoomOut className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={handleZoomReset} title="重置缩放">
            <span className="text-xs font-mono">{Math.round(zoom * 100)}%</span>
          </Button>
          <Button variant="outline" size="sm" onClick={handleZoomIn} title="放大">
            <ZoomIn className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* 图文对照主要内容区域 */}
      <div className="flex-1 overflow-hidden">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 h-full">
          {/* 左侧：图片区域 */}
          <Card className="h-full">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ImageIcon className="h-5 w-5" />
                <span>文档图片</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="h-full">
              <ScrollArea className="h-full">
                <div
                  className="relative bg-gray-50 rounded-lg p-4 min-h-[400px] flex items-center justify-center"
                  style={{ transform: `scale(${zoom})`, transformOrigin: 'top left' }}
                >
                  {documentDetails.image_path ? (
                    <Image
                      src={`http://localhost:8000${documentDetails.image_path}`}
                      alt={documentDetails.original_filename || documentDetails.filename}
                      width={500}
                      height={700}
                      className="max-w-full h-auto rounded shadow-lg"
                      onError={() => {
                        console.error('图片加载失败:', documentDetails.image_path);
                      }}
                    />
                  ) : (
                    <div className="text-center text-gray-500">
                      <ImageIcon className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                      <p>暂无标注图片</p>
                      <p className="text-sm mt-2">
                        文档可能还未进行标注处理
                      </p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* 右侧：双模型结果显示区域 */}
          <Card className="h-full">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>OCR识别结果</span>
                {ocrLoading && <RefreshCw className="h-4 w-4 animate-spin" />}
              </CardTitle>
            </CardHeader>
            <CardContent className="h-full">
              {ocrResults && ocrResults.models ? (
                <Tabs value={activeModel} onValueChange={(value) => setActiveModel(value as 'pp_ocrv5' | 'pp_structurev3')} className="h-full">
                  <TabsList className="grid w-full grid-cols-2 mb-4">
                    <TabsTrigger value="pp_ocrv5" className="flex items-center space-x-2">
                      <Brain className="h-4 w-4" />
                      <span>PP-OCRv5</span>
                      {ocrResults.models.pp_ocrv5 && (
                        <Badge variant="secondary" className="ml-2">
                          {ocrResults.models.pp_ocrv5.total_regions}
                        </Badge>
                      )}
                    </TabsTrigger>
                    <TabsTrigger value="pp_structurev3" className="flex items-center space-x-2">
                      <Table className="h-4 w-4" />
                      <span>PP-StructureV3</span>
                      {ocrResults.models.pp_structurev3 && (
                        <Badge variant="secondary" className="ml-2">
                          {ocrResults.models.pp_structurev3.total_regions}
                        </Badge>
                      )}
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="pp_ocrv5" className="h-full">
                    <ScrollArea className="h-full">
                      <div className="prose prose-sm max-w-none">
                        {ocrResults.models.pp_ocrv5 ? (
                          <ReactMarkdown
                            remarkPlugins={[remarkGfm]}
                            components={{
                              h1: ({children}) => <h1 className="text-xl font-bold mb-4 text-gray-900">{children}</h1>,
                              h2: ({children}) => <h2 className="text-lg font-semibold mb-3 text-gray-800">{children}</h2>,
                              h3: ({children}) => <h3 className="text-base font-medium mb-2 text-gray-700">{children}</h3>,
                              p: ({children}) => <p className="mb-3 text-gray-600 leading-relaxed">{children}</p>,
                              ul: ({children}) => <ul className="list-disc list-inside mb-3 text-gray-600">{children}</ul>,
                              li: ({children}) => <li className="mb-1">{children}</li>,
                              code: ({children}) => <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">{children}</code>,
                              pre: ({children}) => <pre className="bg-gray-100 p-3 rounded overflow-x-auto text-sm">{children}</pre>,
                              hr: () => <hr className="my-6 border-gray-200" />,
                              strong: ({children}) => <strong className="font-semibold text-gray-900">{children}</strong>,
                            }}
                          >
                            {generateModelMarkdownContent(ocrResults.models.pp_ocrv5)}
                          </ReactMarkdown>
                        ) : (
                          <div className="text-center text-gray-500 py-8">
                            <Brain className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                            <p>PP-OCRv5 结果不可用</p>
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                  </TabsContent>

                  <TabsContent value="pp_structurev3" className="h-full">
                    <ScrollArea className="h-full">
                      <div className="prose prose-sm max-w-none">
                        {ocrResults.models.pp_structurev3 ? (
                          <ReactMarkdown
                            remarkPlugins={[remarkGfm]}
                            components={{
                              h1: ({children}) => <h1 className="text-xl font-bold mb-4 text-gray-900">{children}</h1>,
                              h2: ({children}) => <h2 className="text-lg font-semibold mb-3 text-gray-800">{children}</h2>,
                              h3: ({children}) => <h3 className="text-base font-medium mb-2 text-gray-700">{children}</h3>,
                              p: ({children}) => <p className="mb-3 text-gray-600 leading-relaxed">{children}</p>,
                              ul: ({children}) => <ul className="list-disc list-inside mb-3 text-gray-600">{children}</ul>,
                              li: ({children}) => <li className="mb-1">{children}</li>,
                              code: ({children}) => <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">{children}</code>,
                              pre: ({children}) => <pre className="bg-gray-100 p-3 rounded overflow-x-auto text-sm">{children}</pre>,
                              hr: () => <hr className="my-6 border-gray-200" />,
                              strong: ({children}) => <strong className="font-semibold text-gray-900">{children}</strong>,
                            }}
                          >
                            {generateModelMarkdownContent(ocrResults.models.pp_structurev3)}
                          </ReactMarkdown>
                        ) : (
                          <div className="text-center text-gray-500 py-8">
                            <Table className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                            <p>PP-StructureV3 结果不可用</p>
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                  </TabsContent>
                </Tabs>
              ) : (
                <ScrollArea className="h-full">
                  <div className="prose prose-sm max-w-none">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        h1: ({children}) => <h1 className="text-xl font-bold mb-4 text-gray-900">{children}</h1>,
                        h2: ({children}) => <h2 className="text-lg font-semibold mb-3 text-gray-800">{children}</h2>,
                        h3: ({children}) => <h3 className="text-base font-medium mb-2 text-gray-700">{children}</h3>,
                        p: ({children}) => <p className="mb-3 text-gray-600 leading-relaxed">{children}</p>,
                        ul: ({children}) => <ul className="list-disc list-inside mb-3 text-gray-600">{children}</ul>,
                        li: ({children}) => <li className="mb-1">{children}</li>,
                        code: ({children}) => <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">{children}</code>,
                        pre: ({children}) => <pre className="bg-gray-100 p-3 rounded overflow-x-auto text-sm">{children}</pre>,
                        hr: () => <hr className="my-6 border-gray-200" />,
                        strong: ({children}) => <strong className="font-semibold text-gray-900">{children}</strong>,
                      }}
                    >
                      {documentDetails?.markdown_content || '暂无内容'}
                    </ReactMarkdown>
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>

        </div>
      </div>
    </div>
  );
}
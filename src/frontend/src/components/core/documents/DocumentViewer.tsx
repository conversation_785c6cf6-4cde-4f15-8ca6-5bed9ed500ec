"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ZoomIn, ZoomOut, FileText, Image as ImageIcon, Download, RefreshCw } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Document } from '@/lib/types';
import { formatFileSize, formatDate } from '@/lib/utils';

interface DocumentViewerProps {
  document: Document;
  onClose?: () => void;
}

interface DocumentDetails extends Document {
  annotations?: Array<{
    id: string;
    text: string;
    bbox: [number, number, number, number];
    confidence: number;
  }>;
  image_path?: string;
  markdown_content?: string;
}

export function DocumentViewer({ document, onClose }: DocumentViewerProps) {
  const [zoom, setZoom] = useState(1);
  const [documentDetails, setDocumentDetails] = useState<DocumentDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.1, 3));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.1, 0.3));
  };

  const handleZoomReset = () => {
    setZoom(1);
  };

  // 获取文档详情
  const fetchDocumentDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/documents/${document._id}`);
      if (!response.ok) {
        throw new Error(`获取文档详情失败: ${response.status}`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.message || '获取文档详情失败');
      }

      // 生成markdown内容
      const markdownContent = generateMarkdownContent(result.data);

      setDocumentDetails({
        ...result.data,
        markdown_content: markdownContent
      });
    } catch (err) {
      console.error('获取文档详情失败:', err);
      setError(err instanceof Error ? err.message : '获取文档详情失败');
      // 如果获取详情失败，使用基本文档信息
      setDocumentDetails({
        ...document,
        markdown_content: generateMarkdownContent(document)
      });
    } finally {
      setLoading(false);
    }
  };

  // 生成markdown内容
  const generateMarkdownContent = (doc: Document) => {
    return `# ${doc.original_filename || doc.filename}

## 文档信息

- **文件ID**: ${doc._id}
- **原始文件名**: ${doc.original_filename || doc.filename}
- **文件大小**: ${formatFileSize(doc.file_size)}
- **上传时间**: ${formatDate(doc.upload_time)}
- **处理状态**: ${doc.status}
- **文件路径**: ${doc.file_path}

## OCR识别结果

${doc.ocr_text || '暂无OCR文本内容'}

## 处理详情

- **文件哈希**: ${doc.file_hash}
- **处理状态**: ${doc.processing_status}
- **是否删除**: ${doc.deleted ? '是' : '否'}

---

*文档查看器 - OCR智能文档处理系统*
`;
  };

  useEffect(() => {
    fetchDocumentDetails();
  }, [document._id]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[600px]">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-4" />
          <p className="text-gray-600">加载文档详情中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-[600px]">
        <div className="text-center">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchDocumentDetails} variant="outline">
            重新加载
          </Button>
        </div>
      </div>
    );
  }

  if (!documentDetails) {
    return (
      <div className="flex items-center justify-center h-[600px]">
        <p className="text-gray-500">文档详情不可用</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full max-h-[80vh]">
      {/* 工具栏 */}
      <div className="flex justify-between items-center mb-4 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-semibold truncate max-w-md">
            {documentDetails.original_filename || documentDetails.filename}
          </h3>
          <Badge variant="secondary">
            {documentDetails.status === 'completed' ? '已完成' : documentDetails.status}
          </Badge>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={handleZoomOut} title="缩小">
            <ZoomOut className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={handleZoomReset} title="重置缩放">
            <span className="text-xs font-mono">{Math.round(zoom * 100)}%</span>
          </Button>
          <Button variant="outline" size="sm" onClick={handleZoomIn} title="放大">
            <ZoomIn className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview" className="flex items-center space-x-2">
              <FileText className="h-4 w-4" />
              <span>概览</span>
            </TabsTrigger>
            <TabsTrigger value="split" className="flex items-center space-x-2">
              <ImageIcon className="h-4 w-4" />
              <span>图文对照</span>
            </TabsTrigger>
            <TabsTrigger value="markdown" className="flex items-center space-x-2">
              <FileText className="h-4 w-4" />
              <span>Markdown</span>
            </TabsTrigger>
          </TabsList>

          {/* 概览标签页 */}
          <TabsContent value="overview" className="h-full mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 h-full">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="h-5 w-5" />
                    <span>文档信息</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <span className="text-gray-600">文件大小:</span>
                    <span>{formatFileSize(documentDetails.file_size)}</span>

                    <span className="text-gray-600">上传时间:</span>
                    <span>{formatDate(documentDetails.upload_time)}</span>

                    <span className="text-gray-600">处理状态:</span>
                    <Badge variant={documentDetails.status === 'completed' ? 'default' : 'secondary'}>
                      {documentDetails.status === 'completed' ? '已完成' : documentDetails.status}
                    </Badge>

                    <span className="text-gray-600">文件哈希:</span>
                    <span className="font-mono text-xs truncate">{documentDetails.file_hash}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>OCR识别结果</CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-48">
                    <div className="text-sm whitespace-pre-wrap">
                      {documentDetails.ocr_text || '暂无OCR文本内容'}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 图文对照标签页 */}
          <TabsContent value="split" className="h-full mt-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 h-full">
              {/* 左侧：图片区域 */}
              <Card className="h-full">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <ImageIcon className="h-5 w-5" />
                    <span>文档图片</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="h-full">
                  <ScrollArea className="h-full">
                    <div
                      className="relative bg-gray-50 rounded-lg p-4 min-h-[400px] flex items-center justify-center"
                      style={{ transform: `scale(${zoom})`, transformOrigin: 'top left' }}
                    >
                      {documentDetails.image_path ? (
                        <Image
                          src={`http://localhost:8000${documentDetails.image_path}`}
                          alt={documentDetails.original_filename || documentDetails.filename}
                          width={500}
                          height={700}
                          className="max-w-full h-auto rounded shadow-lg"
                          onError={() => {
                            console.error('图片加载失败:', documentDetails.image_path);
                          }}
                        />
                      ) : (
                        <div className="text-center text-gray-500">
                          <ImageIcon className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                          <p>暂无标注图片</p>
                          <p className="text-sm mt-2">
                            文档可能还未进行标注处理
                          </p>
                        </div>
                      )}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>

              {/* 右侧：Markdown渲染区域 */}
              <Card className="h-full">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="h-5 w-5" />
                    <span>文档内容</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="h-full">
                  <ScrollArea className="h-full">
                    <div className="prose prose-sm max-w-none">
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        components={{
                          h1: ({children}) => <h1 className="text-xl font-bold mb-4 text-gray-900">{children}</h1>,
                          h2: ({children}) => <h2 className="text-lg font-semibold mb-3 text-gray-800">{children}</h2>,
                          h3: ({children}) => <h3 className="text-base font-medium mb-2 text-gray-700">{children}</h3>,
                          p: ({children}) => <p className="mb-3 text-gray-600 leading-relaxed">{children}</p>,
                          ul: ({children}) => <ul className="list-disc list-inside mb-3 text-gray-600">{children}</ul>,
                          li: ({children}) => <li className="mb-1">{children}</li>,
                          code: ({children}) => <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">{children}</code>,
                          pre: ({children}) => <pre className="bg-gray-100 p-3 rounded overflow-x-auto text-sm">{children}</pre>,
                          hr: () => <hr className="my-6 border-gray-200" />,
                          strong: ({children}) => <strong className="font-semibold text-gray-900">{children}</strong>,
                        }}
                      >
                        {documentDetails.markdown_content || '暂无内容'}
                      </ReactMarkdown>
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 纯Markdown标签页 */}
          <TabsContent value="markdown" className="h-full mt-4">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-5 w-5" />
                    <span>Markdown渲染</span>
                  </div>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    导出
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="h-full">
                <ScrollArea className="h-full">
                  <div className="prose prose-lg max-w-none">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        h1: ({children}) => <h1 className="text-2xl font-bold mb-6 text-gray-900 border-b pb-2">{children}</h1>,
                        h2: ({children}) => <h2 className="text-xl font-semibold mb-4 text-gray-800">{children}</h2>,
                        h3: ({children}) => <h3 className="text-lg font-medium mb-3 text-gray-700">{children}</h3>,
                        p: ({children}) => <p className="mb-4 text-gray-600 leading-relaxed">{children}</p>,
                        ul: ({children}) => <ul className="list-disc list-inside mb-4 text-gray-600 space-y-1">{children}</ul>,
                        li: ({children}) => <li className="mb-1">{children}</li>,
                        code: ({children}) => <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">{children}</code>,
                        pre: ({children}) => <pre className="bg-gray-100 p-4 rounded overflow-x-auto text-sm mb-4">{children}</pre>,
                        hr: () => <hr className="my-8 border-gray-200" />,
                        strong: ({children}) => <strong className="font-semibold text-gray-900">{children}</strong>,
                        blockquote: ({children}) => <blockquote className="border-l-4 border-blue-200 pl-4 italic text-gray-600 mb-4">{children}</blockquote>,
                      }}
                    >
                      {documentDetails.markdown_content || '暂无内容'}
                    </ReactMarkdown>
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ZoomIn, ZoomOut } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import Image from "next/image";
import { Document } from '@/lib/types';

interface DocumentViewerProps {
  document: Document;
  onClose?: () => void;
}

export function DocumentViewer({ document, onClose }: DocumentViewerProps) {
  const [zoom, setZoom] = useState(1);

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.1, 2));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.1, 0.5));
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex justify-end space-x-2 mb-4">
        <Button variant="outline" size="sm" onClick={handleZoomOut}>
          <ZoomOut className="h-4 w-4" />
        </Button>
        <Button variant="outline" size="sm" onClick={handleZoomIn}>
          <ZoomIn className="h-4 w-4" />
        </Button>
      </div>
      
      <ScrollArea className="flex-1 w-full rounded-md border">
        <div className="relative w-full min-h-[600px] bg-white" style={{ transform: `scale(${zoom})`, transformOrigin: 'top left' }}>
          {document.parseResult ? (
            <div className="p-4">
              <pre className="whitespace-pre-wrap font-mono text-sm">
                {JSON.stringify(document.parseResult, null, 2)}
              </pre>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500">暂无解析结果</p>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
} 
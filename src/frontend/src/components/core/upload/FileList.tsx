import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { formatDistanceToNow } from "date-fns"
import { zhCN } from "date-fns/locale"
import { Eye, RefreshCw } from "lucide-react"

interface FileRecord {
  _id: string
  file_id: string
  original_filename: string
  file_path: string
  file_size: number
  file_type: string
  status: string
  upload_time: string
  metadata?: {
    width?: number
    height?: number
    pages?: number
    format?: string
    color_mode?: string
  }
}

export function FileList() {
  const [files, setFiles] = useState<FileRecord[]>([])
  const [loading, setLoading] = useState(true)
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    fetchFiles()
  }, [])

  const fetchFiles = async () => {
    try {
      const response = await fetch("/api/documents")
      if (!response.ok) {
        throw new Error("获取文件列表失败")
      }
      const data = await response.json()
      setFiles(data.documents)
    } catch (error) {
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "获取文件列表失败",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleView = (fileId: string) => {
    router.push(`/annotate/${fileId}`)
  }

  const handleRegenerate = async (fileId: string) => {
    try {
      const response = await fetch(`/api/documents/${fileId}/regenerate`, {
        method: "POST",
      })
      
      if (!response.ok) {
        throw new Error("重新生成失败")
      }

      toast({
        title: "成功",
        description: "已开始重新生成识别结果",
      })

      // 刷新文件列表
      fetchFiles()
    } catch (error) {
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "重新生成失败",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return <div className="text-center py-4">加载中...</div>
  }

  if (files.length === 0) {
    return <div className="text-center py-4">暂无文件</div>
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>文件名</TableHead>
          <TableHead>类型</TableHead>
          <TableHead>大小</TableHead>
          <TableHead>状态</TableHead>
          <TableHead>上传时间</TableHead>
          <TableHead>操作</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {files.map((file) => (
          <TableRow key={file._id}>
            <TableCell>{file.original_filename}</TableCell>
            <TableCell>{file.file_type}</TableCell>
            <TableCell>{formatFileSize(file.file_size)}</TableCell>
            <TableCell>
              <span className={`inline-block px-2 py-1 rounded text-sm ${
                file.status === "completed" ? "bg-green-100 text-green-800" :
                file.status === "processing" ? "bg-blue-100 text-blue-800" :
                file.status === "pending" ? "bg-yellow-100 text-yellow-800" :
                "bg-red-100 text-red-800"
              }`}>
                {file.status === "completed" ? "已完成" :
                 file.status === "processing" ? "处理中" :
                 file.status === "pending" ? "等待处理" :
                 "错误"}
              </span>
            </TableCell>
            <TableCell>
              {formatDistanceToNow(new Date(file.upload_time), {
                addSuffix: true,
                locale: zhCN,
              })}
            </TableCell>
            <TableCell>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  className="h-8 px-3 text-xs"
                  onClick={() => handleView(file.file_id)}
                >
                  <Eye className="h-4 w-4 mr-1" />
                  查看
                </Button>
                <Button
                  variant="outline"
                  className="h-8 px-3 text-xs"
                  onClick={() => handleRegenerate(file.file_id)}
                  disabled={file.status === "processing"}
                >
                  <RefreshCw className="h-4 w-4 mr-1" />
                  重新生成
                </Button>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
} 
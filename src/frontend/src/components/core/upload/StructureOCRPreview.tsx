import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Tit<PERSON> } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { X, Download, Copy } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import ReactMarkdown from "react-markdown"

interface Region {
  text: string
  confidence: number
  box: number[][]
  region_type: string
  structure_type?: string
  table_html?: string
}

interface StructureOCRData {
  file_id: string
  filename: string
  processing_method: string
  total_regions: number
  image_size: {
    width: number
    height: number
  }
  regions: Region[]
  markdown_text: string
  success: boolean
}

interface StructureOCRPreviewProps {
  data: StructureOCRData
  onClose: () => void
}

export function StructureOCRPreview({ data, onClose }: StructureOCRPreviewProps) {
  const [annotatedImageUrl, setAnnotatedImageUrl] = useState<string>("")
  const { toast } = useToast()

  useEffect(() => {
    // 生成标注图片
    generateAnnotatedImage()
  }, [data])

  const generateAnnotatedImage = async () => {
    try {
      // 创建canvas来绘制标注
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (!ctx) return

      // 设置canvas尺寸
      canvas.width = data.image_size.width
      canvas.height = data.image_size.height

      // 绘制白色背景
      ctx.fillStyle = 'white'
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // 绘制区域框
      data.regions.forEach((region, index) => {
        if (region.box && region.box.length >= 4) {
          const box = region.box
          
          // 绘制边框
          ctx.strokeStyle = getRegionColor(region.region_type)
          ctx.lineWidth = 2
          ctx.beginPath()
          ctx.moveTo(box[0][0], box[0][1])
          for (let i = 1; i < box.length; i++) {
            ctx.lineTo(box[i][0], box[i][1])
          }
          ctx.closePath()
          ctx.stroke()

          // 绘制标签
          ctx.fillStyle = getRegionColor(region.region_type)
          ctx.font = '12px Arial'
          const label = `${region.region_type} (${(region.confidence * 100).toFixed(1)}%)`
          const textWidth = ctx.measureText(label).width
          
          // 绘制标签背景
          ctx.fillRect(box[0][0], box[0][1] - 20, textWidth + 8, 16)
          
          // 绘制标签文字
          ctx.fillStyle = 'white'
          ctx.fillText(label, box[0][0] + 4, box[0][1] - 8)
        }
      })

      // 转换为图片URL
      const imageUrl = canvas.toDataURL('image/png')
      setAnnotatedImageUrl(imageUrl)
    } catch (error) {
      console.error('生成标注图片失败:', error)
    }
  }

  const getRegionColor = (regionType: string): string => {
    const colors: { [key: string]: string } = {
      'title': '#ff6b6b',
      'heading': '#4ecdc4',
      'text': '#45b7d1',
      'table': '#96ceb4',
      'figure': '#ffeaa7',
      'annotation': '#dda0dd',
      'default': '#74b9ff'
    }
    return colors[regionType] || colors.default
  }

  const handleCopyMarkdown = async () => {
    try {
      await navigator.clipboard.writeText(data.markdown_text)
      toast({
        title: "复制成功",
        description: "Markdown内容已复制到剪贴板",
      })
    } catch (error) {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板",
        variant: "destructive",
      })
    }
  }

  const handleDownloadMarkdown = () => {
    const blob = new Blob([data.markdown_text], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${data.filename.replace(/\.[^/.]+$/, "")}.md`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl w-[90vw] h-[90vh] p-0">
        <DialogHeader className="p-6 pb-4 border-b">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-xl font-semibold">
                OCR识别结果预览
              </DialogTitle>
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="outline">{data.processing_method}</Badge>
                <Badge variant="secondary">{data.total_regions} 个区域</Badge>
                <span className="text-sm text-gray-500">{data.filename}</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyMarkdown}
              >
                <Copy className="h-4 w-4 mr-2" />
                复制
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownloadMarkdown}
              >
                <Download className="h-4 w-4 mr-2" />
                下载
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>
        
        <div className="flex h-full">
          {/* 左侧：标注图片 */}
          <div className="w-1/2 p-6 border-r">
            <div className="h-full flex flex-col">
              <h3 className="text-lg font-medium mb-4">标注图片</h3>
              <div className="flex-1 overflow-hidden rounded-lg border bg-gray-50">
                {annotatedImageUrl ? (
                  <img
                    src={annotatedImageUrl}
                    alt="标注图片"
                    className="w-full h-full object-contain"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-500">
                    生成标注图片中...
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* 右侧：识别内容 */}
          <div className="w-1/2 p-6">
            <div className="h-full flex flex-col">
              <h3 className="text-lg font-medium mb-4">识别内容</h3>
              <ScrollArea className="flex-1 border rounded-lg p-4 bg-white">
                <div className="prose prose-sm max-w-none">
                  <ReactMarkdown>{data.markdown_text}</ReactMarkdown>
                </div>
              </ScrollArea>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

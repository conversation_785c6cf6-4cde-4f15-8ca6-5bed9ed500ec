import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { Upload } from "lucide-react"
import { useRouter } from "next/navigation"

interface UploadButtonProps {
  onUploadSuccess?: () => void
}

export function UploadButton({ onUploadSuccess }: UploadButtonProps) {
  const [uploading, setUploading] = useState(false)
  const { toast } = useToast()
  const router = useRouter()
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setUploading(true)
    const formData = new FormData()
    formData.append("file", file)

    try {
      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        throw new Error("上传失败")
      }

      const data = await response.json()
      
      toast({
        title: "上传成功",
        description: "文件已开始处理，正在进行OCR识别...",
      })

      // 调用回调函数刷新文档列表
      if (onUploadSuccess) {
        onUploadSuccess()
      }
    } catch (error) {
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "上传失败",
        variant: "destructive",
      })
    } finally {
      setUploading(false)
      // 清空文件输入，允许重复上传相同文件
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }
    }
  }

  return (
    <>
      <input
        type="file"
        accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg,.heic,.heif"
        onChange={handleUpload}
        className="hidden"
        ref={fileInputRef}
      />
      <Button
        variant="default"
        onClick={() => fileInputRef.current?.click()}
        disabled={uploading}
      >
        {uploading ? (
          <>
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent mr-2" />
            上传中...
          </>
        ) : (
          <>
            <Upload className="h-4 w-4 mr-2" />
            上传文件
          </>
        )}
      </Button>
    </>
  )
} 
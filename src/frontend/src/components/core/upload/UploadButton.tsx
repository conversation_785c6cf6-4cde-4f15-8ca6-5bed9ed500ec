import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { Upload, Eye } from "lucide-react"
import { useRouter } from "next/navigation"
import { StructureOCRPreview } from "./StructureOCRPreview"

interface UploadButtonProps {
  onUploadSuccess?: () => void
  enableStructurePreview?: boolean
}

export function UploadButton({ onUploadSuccess, enableStructurePreview = false }: UploadButtonProps) {
  const [uploading, setUploading] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [previewData, setPreviewData] = useState<any>(null)
  const { toast } = useToast()
  const router = useRouter()
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setUploading(true)
    const formData = new FormData()
    formData.append("file", file)

    try {
      let response
      let data

      if (enableStructurePreview) {
        // 使用结构化OCR处理
        response = await fetch("/api/ocr/structure-process", {
          method: "POST",
          body: formData,
        })

        if (!response.ok) {
          throw new Error("结构化OCR处理失败")
        }

        data = await response.json()

        // 显示预览
        setPreviewData(data)
        setShowPreview(true)

        toast({
          title: "处理完成",
          description: `识别到 ${data.total_regions} 个区域，点击预览查看结果`,
        })
      } else {
        // 使用传统上传
        response = await fetch("/api/upload", {
          method: "POST",
          body: formData,
        })

        if (!response.ok) {
          throw new Error("上传失败")
        }

        data = await response.json()

        toast({
          title: "上传成功",
          description: "文件已开始处理，正在进行OCR识别...",
        })
      }

      // 调用回调函数刷新文档列表
      if (onUploadSuccess) {
        onUploadSuccess()
      }
    } catch (error) {
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "处理失败",
        variant: "destructive",
      })
    } finally {
      setUploading(false)
      // 清空文件输入，允许重复上传相同文件
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }
    }
  }

  return (
    <>
      <input
        type="file"
        accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg,.heic,.heif"
        onChange={handleUpload}
        className="hidden"
        ref={fileInputRef}
      />
      <div className="flex gap-2">
        <Button
          variant="default"
          onClick={() => fileInputRef.current?.click()}
          disabled={uploading}
        >
          {uploading ? (
            <>
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent mr-2" />
              {enableStructurePreview ? "处理中..." : "上传中..."}
            </>
          ) : (
            <>
              <Upload className="h-4 w-4 mr-2" />
              {enableStructurePreview ? "结构化识别" : "上传文件"}
            </>
          )}
        </Button>

        {previewData && (
          <Button
            variant="outline"
            onClick={() => setShowPreview(true)}
          >
            <Eye className="h-4 w-4 mr-2" />
            预览结果
          </Button>
        )}
      </div>

      {showPreview && previewData && (
        <StructureOCRPreview
          data={previewData}
          onClose={() => setShowPreview(false)}
        />
      )}
    </>
  )
} 
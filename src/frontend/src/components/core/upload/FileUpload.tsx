'use client'

import React, { useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { UploadCloud } from 'lucide-react'
import { Loading } from '@/components/ui/loading'

export interface FileUploadProps {
  onFileUpload: (file: File) => Promise<void>
  isUploading: boolean
  acceptedFileTypes: string
}

export function FileUpload({ onFileUpload, isUploading, acceptedFileTypes }: FileUploadProps) {
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      await onFileUpload(acceptedFiles[0])
    }
  }, [onFileUpload])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png']
    },
    maxFiles: 1
  })

  return (
    <div
      {...getRootProps()}
      className={`
        border-2 border-dashed rounded-lg p-8 text-center cursor-pointer
        transition-colors duration-200 ease-in-out
        ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}
        ${isUploading ? 'pointer-events-none opacity-50' : ''}
      `}
    >
      <input {...getInputProps()} />
      <div className="flex flex-col items-center justify-center space-y-4">
        {isUploading ? (
          <>
            <Loading />
            <p className="text-sm text-gray-500">正在上传...</p>
          </>
        ) : (
          <>
            <UploadCloud className="h-12 w-12 text-gray-400" />
            <div className="space-y-1 text-center">
              <p className="text-sm text-gray-600">
                {isDragActive ? (
                  "松开以上传文件"
                ) : (
                  <>
                    将文件拖放到此处，或
                    <button type="button" className="text-blue-500 hover:text-blue-600 mx-1">
                      点击选择文件
                    </button>
                  </>
                )}
              </p>
              <p className="text-xs text-gray-500">
                支持的文件类型：{acceptedFileTypes}
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  )
} 
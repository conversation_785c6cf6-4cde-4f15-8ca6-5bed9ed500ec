import React from 'react';
import { AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface ErrorProps {
  message: string;
  title?: string;
  description?: string;
  className?: string;
}

export function Error({ message, title = '错误', description, className }: ErrorProps) {
  return (
    <div className={cn("bg-red-50 border-l-4 border-red-400 p-4", className)}>
      <div className="flex">
        <div className="flex-shrink-0">
          <AlertCircle className="h-5 w-5 text-red-400" />
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red-800">
            {title}
          </h3>
          <div className="mt-2 text-sm text-red-700">
            <p>
              {description || message}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export interface ErrorPageProps {
  message: string;
  title?: string;
  onRetry?: () => void;
  variant?: 'error' | 'warning';
  className?: string;
}

export function ErrorPage({ message, title, onRetry, variant = 'error', className }: ErrorPageProps) {
  const Icon = variant === 'error' ? AlertCircle : AlertCircle;
  return (
    <div className={cn("flex flex-col items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8", className)}>
      <div className="max-w-md w-full space-y-8">
        <div className="flex flex-col items-center">
          <Icon className={cn("h-12 w-12", variant === 'error' ? 'text-red-500' : 'text-yellow-500')} />
          <h2 className={cn("mt-6 text-center text-3xl font-extrabold", variant === 'error' ? 'text-red-900' : 'text-yellow-900')}>
            {title || (variant === 'error' ? '发生错误' : '警告')}
          </h2>
          <p className={cn("mt-2 text-center text-sm", variant === 'error' ? 'text-red-600' : 'text-yellow-600')}>
            {message}
          </p>
        </div>
        {onRetry && (
          <div className="mt-6">
            <button
              onClick={onRetry}
              className={cn(
                "group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2",
                variant === 'error' 
                  ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                  : 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'
              )}
            >
              重试
            </button>
          </div>
        )}
      </div>
    </div>
  );
} 
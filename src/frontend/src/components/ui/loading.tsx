import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingProps {
  className?: string;
  size?: number;
}

export const Loading = ({ className, size = 24 }: LoadingProps) => {
  return (
    <div className={cn('flex items-center justify-center', className)}>
      <Loader2 className="animate-spin" size={size} />
    </div>
  );
};

interface LoadingOverlayProps extends LoadingProps {
  message?: string;
}

export const LoadingOverlay = ({ className, size = 32, message }: LoadingOverlayProps) => {
  return (
    <div className={cn('fixed inset-0 bg-black/50 flex items-center justify-center z-50', className)}>
      <div className="bg-white rounded-lg p-6 flex flex-col items-center space-y-4">
        <Loading size={size} />
        {message && <p className="text-sm text-gray-600">{message}</p>}
      </div>
    </div>
  );
}; 
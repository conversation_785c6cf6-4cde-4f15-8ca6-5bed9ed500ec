import React, { useState, useEffect, useRef } from 'react';
import { Button, message, Space, Tooltip } from 'antd';
import { SaveOutlined, DeleteOutlined } from '@ant-design/icons';
import { Stage, Layer, Rect, Transformer, Text, Image as KonvaImage } from 'react-konva';
import type { KonvaEventObject } from 'konva/lib/Node';
import type { Transformer as TransformerType } from 'konva/lib/shapes/Transformer';
import type { Stage as StageType } from 'konva/lib/Stage';
import { OCRLabelProperties } from './OCRLabelProperties';
import { OCRBox, REGION_TYPES } from '@/types/ocr';

interface OCRLabelEditorProps {
  documentId: string;
  imageUrl: string;
  onSave: (labels: OCRBox[]) => Promise<void>;
  onCancel: () => void;
  initialLabels?: OCRBox[];
}

export const OCRLabelEditor: React.FC<OCRLabelEditorProps> = ({
  documentId,
  imageUrl,
  onSave,
  onCancel,
  initialLabels = []
}) => {
  const [image, setImage] = useState<HTMLImageElement | null>(null);
  const [labels, setLabels] = useState<OCRBox[]>(initialLabels);
  const [selectedId, setSelectedId] = useState<string | null>(null);
  const [scale, setScale] = useState(1);
  
  const stageRef = useRef<StageType>(null);
  const transformerRef = useRef<TransformerType>(null);

  // 加载图片
  useEffect(() => {
    const img = new Image();
    img.src = imageUrl;
    img.onload = () => {
      setImage(img);
      // 自动调整缩放以适应屏幕
      const scale = Math.min(
        window.innerWidth * 0.7 / img.width,
        window.innerHeight / img.height
      ) * 0.8;
      setScale(scale);
    };
  }, [imageUrl]);

  // 选中标注框时更新transformer
  useEffect(() => {
    if (!transformerRef.current) return;
    
    const node = stageRef.current?.findOne(`#${selectedId}`);
    if (node) {
      transformerRef.current.nodes([node]);
    } else {
      transformerRef.current.nodes([]);
    }
  }, [selectedId]);

  // 处理标注框拖动
  const handleDragEnd = (e: KonvaEventObject<DragEvent>, index: number) => {
    const box = e.target;
    const newLabels = [...labels];
    newLabels[index] = {
      ...newLabels[index],
      x: box.x() / scale,
      y: box.y() / scale,
      width: box.width() / scale,
      height: box.height() / scale
    };
    setLabels(newLabels);
  };

  // 处理标注框变形
  const handleTransformEnd = (e: KonvaEventObject<Event>, index: number) => {
    const box = e.target;
    const scaleX = box.scaleX();
    const scaleY = box.scaleY();
    
    box.scaleX(1);
    box.scaleY(1);
    
    const newLabels = [...labels];
    newLabels[index] = {
      ...newLabels[index],
      x: box.x() / scale,
      y: box.y() / scale,
      width: box.width() * scaleX / scale,
      height: box.height() * scaleY / scale
    };
    setLabels(newLabels);
  };

  // 删除选中的标注框
  const handleDelete = () => {
    if (!selectedId) return;
    setLabels(labels.filter((_, i) => i.toString() !== selectedId));
    setSelectedId(null);
  };

  // 更新标注框属性
  const handleUpdateLabel = (updates: Partial<OCRBox>) => {
    if (!selectedId) return;
    const index = parseInt(selectedId);
    const newLabels = [...labels];
    newLabels[index] = {
      ...newLabels[index],
      ...updates
    };
    setLabels(newLabels);
  };

  // 保存标注
  const handleSave = async () => {
    try {
      await onSave(labels);
      message.success('保存成功');
    } catch (error) {
      message.error('保存失败');
      console.error('保存标注失败:', error);
    }
  };

  if (!image) return <div>加载中...</div>;

  const selectedLabel = selectedId ? labels[parseInt(selectedId)] : null;

  return (
    <div className="flex gap-4 p-4 h-screen">
      <div className="flex-1 relative">
        <Stage
          ref={stageRef}
          width={window.innerWidth * 0.7}
          height={window.innerHeight - 100}
          scale={{ x: scale, y: scale }}
        >
          <Layer>
            {/* 背景图片 */}
            <KonvaImage
              image={image}
              width={image.width}
              height={image.height}
            />
            
            {/* 标注框 */}
            {labels.map((label, i) => (
              <React.Fragment key={i}>
                <Rect
                  id={i.toString()}
                  x={label.x * scale}
                  y={label.y * scale}
                  width={label.width * scale}
                  height={label.height * scale}
                  stroke={label.type ? REGION_TYPES[label.type]?.color || '#00ff00' : '#00ff00'}
                  strokeWidth={2}
                  draggable
                  onClick={() => setSelectedId(i.toString())}
                  onDragEnd={(e) => handleDragEnd(e, i)}
                  onTransformEnd={(e) => handleTransformEnd(e, i)}
                />
                <Text
                  x={label.x * scale}
                  y={(label.y - 20) * scale}
                  text={label.text || ''}
                  fontSize={14 * scale}
                  fill={label.type ? REGION_TYPES[label.type]?.color || '#00ff00' : '#00ff00'}
                />
              </React.Fragment>
            ))}
            
            {/* 变形控制器 */}
            <Transformer
              ref={transformerRef}
              boundBoxFunc={(oldBox: any, newBox: any) => {
                // 限制最小尺寸
                const minSize = 5;
                const box = {
                  ...newBox,
                  width: Math.max(minSize, newBox.width),
                  height: Math.max(minSize, newBox.height)
                };
                return box;
              }}
            />
          </Layer>
        </Stage>

        {/* 工具栏 */}
        <div className="absolute top-4 right-4 space-x-2">
          <Tooltip title="删除选中的标注框">
            <Button
              icon={<DeleteOutlined />}
              onClick={handleDelete}
              disabled={!selectedId}
            />
          </Tooltip>
          <Tooltip title="保存标注">
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSave}
            />
          </Tooltip>
        </div>
      </div>

      {/* 属性编辑面板 */}
      <div className="w-80">
        <OCRLabelProperties
          selectedLabel={selectedLabel}
          onUpdate={handleUpdateLabel}
        />
      </div>
    </div>
  );
}; 
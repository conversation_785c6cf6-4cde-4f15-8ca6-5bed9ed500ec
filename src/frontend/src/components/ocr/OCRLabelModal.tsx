import React, { useState } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Document } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Loading } from '@/components/ui/loading';

export interface OCRLabelModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  document: Document;
  onSuccess: () => void;
}

export function OCRLabelModal({ open, onOpenChange, document, onSuccess }: OCRLabelModalProps) {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleSave = async () => {
    try {
      setLoading(true);
      // TODO: 实现标注保存逻辑
      toast({
        title: '保存成功',
        description: '标注已保存'
      });
      onSuccess();
      onOpenChange(false);
    } catch (error) {
      toast({
        title: '保存失败',
        description: error instanceof Error ? error.message : '保存标注时出错',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>OCR 标注</DialogTitle>
        </DialogHeader>
        
        {loading ? (
          <div className="flex items-center justify-center h-96">
            <Loading />
          </div>
        ) : (
          <div className="space-y-4">
            <div className="border rounded-lg p-4">
              <h3 className="font-medium mb-2">文档信息</h3>
              <p className="text-sm text-gray-600">文件名：{document.original_filename}</p>
              <p className="text-sm text-gray-600">类型：{document.file_type}</p>
            </div>
            
            {/* TODO: 添加标注界面 */}
            <div className="h-96 border rounded-lg flex items-center justify-center">
              <p className="text-gray-500">标注界面开发中...</p>
            </div>
            
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                取消
              </Button>
              <Button onClick={handleSave} disabled={loading}>
                保存
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
} 
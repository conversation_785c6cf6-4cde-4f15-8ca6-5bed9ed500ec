import React, { useEffect, useRef } from 'react';
import { Spin } from 'antd';
import { PaddleLabelInstance, Annotation } from '@/lib/paddlelabel';

interface PaddleLabelProps {
  imageUrl: string;
  annotations: Annotation[];
  onSave: (labels: Annotation[]) => void;
  loading?: boolean;
  mode?: 'detection' | 'classification' | 'ocr';
}

const PaddleLabel: React.FC<PaddleLabelProps> = ({
  imageUrl,
  annotations,
  onSave,
  loading = false,
  mode = 'detection'
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const paddleLabelRef = useRef<PaddleLabelInstance | null>(null);

  useEffect(() => {
    if (containerRef.current) {
      // 初始化PaddleLabel
      const container = containerRef.current;
      const paddleLabel = new PaddleLabelInstance({
        container,
        imageUrl,
        annotations,
        onSave,
        mode
      });

      paddleLabelRef.current = paddleLabel;

      return () => {
        // 清理PaddleLabel实例
        if (paddleLabelRef.current) {
          paddleLabelRef.current.destroy();
          paddleLabelRef.current = null;
        }
      };
    }
  }, [imageUrl, mode]);

  // 当annotations改变时更新标注
  useEffect(() => {
    if (paddleLabelRef.current) {
      paddleLabelRef.current.updateAnnotations(annotations);
    }
  }, [annotations]);

  return (
    <Spin spinning={loading}>
      <div 
        ref={containerRef} 
        style={{ 
          width: '100%', 
          height: '100%',
          minHeight: '600px',
          background: '#f0f2f5'
        }} 
      />
    </Spin>
  );
};

export default PaddleLabel; 
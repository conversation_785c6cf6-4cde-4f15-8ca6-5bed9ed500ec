import React from 'react';
import { Input, Select, InputNumber } from 'antd';
import { Card } from '@/components/ui/card';
import { OCRBox, REGION_TYPES } from '@/types/ocr';

interface OCRLabelPropertiesProps {
  selectedLabel: OCRBox | null;
  onUpdate: (updates: Partial<OCRBox>) => void;
}

export const OCRLabelProperties: React.FC<OCRLabelPropertiesProps> = ({
  selectedLabel,
  onUpdate
}) => {
  if (!selectedLabel) {
    return (
      <Card className="p-4">
        <div className="text-gray-500 text-center">
          请选择一个标注框以编辑属性
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-4 space-y-4">
      <div>
        <label className="block text-sm font-medium mb-1">文本内容</label>
        <Input.TextArea
          value={selectedLabel.text || ''}
          onChange={(e) => onUpdate({ text: e.target.value })}
          placeholder="请输入文本内容"
          autoSize={{ minRows: 2, maxRows: 6 }}
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">区域类型</label>
        <Select
          value={selectedLabel.type || 'single_column'}
          onChange={(value) => onUpdate({ type: value })}
          style={{ width: '100%' }}
        >
          {Object.entries(REGION_TYPES).map(([key, { name, color }]) => (
            <Select.Option key={key} value={key}>
              <div className="flex items-center">
                <div
                  className="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: color }}
                />
                {name}
              </div>
            </Select.Option>
          ))}
        </Select>
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">置信度</label>
        <InputNumber
          value={selectedLabel.confidence || 1}
          onChange={(value) => value !== null && onUpdate({ confidence: value })}
          min={0}
          max={1}
          step={0.01}
          style={{ width: '100%' }}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">X 坐标</label>
          <InputNumber
            value={Math.round(selectedLabel.x)}
            onChange={(value) => value !== null && onUpdate({ x: value })}
            style={{ width: '100%' }}
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Y 坐标</label>
          <InputNumber
            value={Math.round(selectedLabel.y)}
            onChange={(value) => value !== null && onUpdate({ y: value })}
            style={{ width: '100%' }}
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">宽度</label>
          <InputNumber
            value={Math.round(selectedLabel.width)}
            onChange={(value) => value !== null && onUpdate({ width: value })}
            min={1}
            style={{ width: '100%' }}
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">高度</label>
          <InputNumber
            value={Math.round(selectedLabel.height)}
            onChange={(value) => value !== null && onUpdate({ height: value })}
            min={1}
            style={{ width: '100%' }}
          />
        </div>
      </div>
    </Card>
  );
}; 
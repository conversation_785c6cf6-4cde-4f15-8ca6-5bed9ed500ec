import Link from 'next/link'
import { FileText, BookOpen } from 'lucide-react'

export function Navigation() {
  return (
    <nav className="border-b">
      <div className="flex h-16 items-center px-4">
        <div className="flex items-center space-x-4">
          <Link href="/documents" className="flex items-center space-x-2">
            <FileText className="h-6 w-6" />
            <span className="font-medium">Documents</span>
          </Link>
          <Link href="/learning" className="flex items-center space-x-2">
            <BookOpen className="h-6 w-6" />
            <span className="font-medium">Learning</span>
          </Link>
        </div>
      </div>
    </nav>
  )
} 
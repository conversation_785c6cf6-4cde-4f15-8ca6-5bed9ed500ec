/**
 * 统一配置文件
 * 管理所有前端配置
 */

// 环境变量获取工具
const getEnvVar = (key: string, defaultValue: string = ''): string => {
  if (typeof window === 'undefined') {
    return process.env[key] || defaultValue;
  }
  return (window as any).__ENV__?.[key] || defaultValue;
};

// 环境配置
export const ENV = {
  IS_DEV: process.env.NODE_ENV === 'development',
  IS_PROD: process.env.NODE_ENV === 'production'
};

// 应用配置
export const APP_CONFIG = {
  NAME: getEnvVar('NEXT_PUBLIC_APP_NAME', 'OCR智能文档处理系统'),
  VERSION: getEnvVar('NEXT_PUBLIC_APP_VERSION', '1.0.0'),
  DEBUG: getEnvVar('NEXT_PUBLIC_DEBUG', 'false') === 'true',
};

// 服务配置
export const SERVICES = {
  // PaddleLabel服务配置
  PADDLE_LABEL: {
    BASE_URL: process.env.NEXT_PUBLIC_PADDLE_LABEL_URL || '/paddlelabel',
    PORTS: {
      DEV: 8089,
      PROD: 8089
    }
  },
  
  // API服务配置
  API: {
    BASE_URL: process.env.NEXT_PUBLIC_API_URL || '/api',
    VERSION: '',  // 移除API版本号配置
    TIMEOUT: 30000,
    ENDPOINTS: {
      OCR: {
        DOCUMENTS: '/ocr/documents',
        LABELS: '/ocr/documents/:id/labels',
        PARSE: '/ocr/documents/:id/parse'
      }
    }
  }
};

// 文档状态
export const DocumentStatus = {
  ACTIVE: 'active',
  DELETED: 'deleted',
} as const;

// 默认值
export const DEFAULT_VALUES = {
  deleted: false,
  deletedAt: null,
  status: DocumentStatus.ACTIVE
} as const;

// 分页配置
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: parseInt(getEnvVar('NEXT_PUBLIC_DEFAULT_PAGE_SIZE', '10')),
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
} as const;

// 上传配置
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: parseInt(getEnvVar('NEXT_PUBLIC_MAX_FILE_SIZE', '10485760')), // 10MB
  ALLOWED_FILE_TYPES: ['image/jpeg', 'image/png', 'application/pdf'],
  MAX_FILES: parseInt(getEnvVar('NEXT_PUBLIC_MAX_FILES', '10')),
} as const;

// UI配置
export const UI_CONFIG = {
  THEME: getEnvVar('NEXT_PUBLIC_THEME', 'light'),
  ANIMATION_DURATION: parseInt(getEnvVar('NEXT_PUBLIC_ANIMATION_DURATION', '300')),
  TOAST_DURATION: parseInt(getEnvVar('NEXT_PUBLIC_TOAST_DURATION', '3000')),
} as const;

// 特性开关
export const FEATURE_FLAGS = {
  ENABLE_BATCH_PROCESSING: getEnvVar('NEXT_PUBLIC_ENABLE_BATCH_PROCESSING', 'true') === 'true',
  ENABLE_QUALITY_CHECK: getEnvVar('NEXT_PUBLIC_ENABLE_QUALITY_CHECK', 'true') === 'true',
  ENABLE_AUTO_CORRECTION: getEnvVar('NEXT_PUBLIC_ENABLE_AUTO_CORRECTION', 'true') === 'true',
} as const;

// MongoDB配置（仅用于前端API路由）
export const MONGODB = {
  URI: process.env.MONGODB_URI || 'mongodb://localhost:55142',
  DB_NAME: process.env.MONGODB_DB_NAME || 'agentdb_test',
  COLLECTIONS: {
    OCR_FILES: 'ocr_files',
    OCR_LABELS: 'ocr_labels'
  } as const
} as const;

// API路径构建函数
export const buildApiPath = (endpoint: string, params: Record<string, string> = {}) => {
  let path = SERVICES.API.BASE_URL + endpoint;
  Object.entries(params).forEach(([key, value]) => {
    path = path.replace(`:${key}`, value);
  });
  return path;
};

// 导出所有配置
export default {
  ENV,
  APP_CONFIG,
  SERVICES,
  DocumentStatus,
  DEFAULT_VALUES,
  PAGINATION_CONFIG,
  UPLOAD_CONFIG,
  UI_CONFIG,
  FEATURE_FLAGS,
  MONGODB,
  buildApiPath
}; 
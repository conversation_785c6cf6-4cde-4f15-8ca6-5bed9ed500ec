'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Upload, Download, RotateCcw, Save, Play, Eye, EyeOff, Trash2, <PERSON>tings, Brain, Search, Filter, Edit2, X } from 'lucide-react';
import LearningFeedback from '../../components/LearningFeedback';
import Navigation from '@/components/Navigation'
import AnnotationSystem from '@/components/AnnotationSystem'
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Loading, LoadingOverlay } from '@/components/ui/loading';
import { Error } from '@/components/ui/error';
import { cn } from '@/lib/utils';

// 区域类型定义
const REGION_TYPES = {
  single_column: { name: '单栏文本', color: '#10B981', key: '1' },
  left_column: { name: '左栏文本', color: '#3B82F6', key: '2' },
  right_column: { name: '右栏文本', color: '#EF4444', key: '3' },
  table: { name: '表格', color: '#F59E0B', key: '4' },
  image: { name: '图像', color: '#8B5CF6', key: '5' },
  header: { name: '页眉', color: '#06B6D4', key: '6' },
  footer: { name: '页脚', color: '#84CC16', key: '7' },
  special: { name: '特殊区域', color: '#F97316', key: '8' }
};

interface AnnotationRegion {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  regionType: string;
  order: number;
  label?: string;
}

interface OCRResult {
  region_id: string;
  region_type: string;
  order: number;
  bbox: [number, number, number, number];
  text: string;
  confidence: number;
  raw_ocr_results: any[];
}

interface Annotation {
  id: string;
  documentId: string;
  content: string;
  originalContent: string;
  confidence: number;
  status: 'pending' | 'corrected';  // 简化状态
  region: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

// 模拟数据，实际应该从API获取
const mockData = {
  imageUrl: '/sample.jpg',
  boxes: [
    {
      id: '1',
      text: '示例标题',
      confidence: 0.95,
      box: [10, 10, 90, 20] as [number, number, number, number],
      category: 'title'
    },
    {
      id: '2',
      text: '这是一段示例文本内容',
      confidence: 0.88,
      box: [10, 30, 90, 50] as [number, number, number, number],
      category: 'content'
    }
  ]
}

// 标注项组件
const AnnotationItem = ({
  content,
  originalContent,
  confidence,
  status,
  onEdit,
  onSave,
  onCancel,
  isEditing
}: {
  content: string;
  originalContent: string;
  confidence: number;
  status: Annotation['status'];
  onEdit: () => void;
  onSave: (newContent: string) => void;
  onCancel: () => void;
  isEditing: boolean;
}) => {
  const [editedContent, setEditedContent] = useState(content);

  useEffect(() => {
    setEditedContent(content);
  }, [content]);

  return (
    <Card className="p-4">
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className={cn(
              'px-2 py-1 text-xs font-medium rounded',
              status === 'corrected' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
            )}>
              {status === 'corrected' ? '已修正' : '待修正'}
            </span>
            <span className="text-sm text-gray-500">
              置信度: {(confidence * 100).toFixed(1)}%
            </span>
          </div>
          {!isEditing ? (
            <Button variant="ghost" onClick={onEdit}>
              <Edit2 className="h-4 w-4" />
            </Button>
          ) : (
            <div className="flex items-center space-x-2">
              <Button variant="ghost" onClick={() => onSave(editedContent)}>
                <Save className="h-4 w-4" />
              </Button>
              <Button variant="ghost" onClick={onCancel}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        <div className="space-y-2">
          <div>
            <label className="text-xs text-gray-500">原始文本</label>
            <p className="text-sm text-gray-700">{originalContent}</p>
          </div>
          <div>
            <label className="text-xs text-gray-500">修正文本</label>
            {isEditing ? (
              <Input
                value={editedContent}
                onChange={(e) => setEditedContent(e.target.value)}
                className="mt-1"
              />
            ) : (
              <p className="text-sm text-gray-900">{content}</p>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default function AnnotationPage() {
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [editingId, setEditingId] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadAnnotations();
  }, []);

  const loadAnnotations = async () => {
    try {
      setLoading(true);
      setError(null);
      // TODO: 从API获取标注数据
      const response = await fetch('/api/annotations');
      const data = await response.json();
      setAnnotations(data);
    } catch (err) {
      setError('加载标注数据失败');
      console.error('Failed to load annotations:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (id: string, newContent: string) => {
    try {
      setSaving(true);
      // TODO: 调用API保存修改
      await fetch(`/api/annotations/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content: newContent }),
      });
      
      setAnnotations(annotations.map(ann =>
        ann.id === id ? { ...ann, content: newContent } : ann
      ));
      setEditingId(null);
    } catch (err) {
      setError('保存修改失败');
      console.error('Failed to save annotation:', err);
    } finally {
      setSaving(false);
    }
  };

  const filteredAnnotations = annotations.filter(ann =>
    ann.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
    ann.originalContent.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return <Loading className="min-h-[400px]" />;
  }

  if (error) {
    return (
      <Error
        title="加载失败"
        message={error}
        variant="error"
        className="m-4"
      />
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">标注系统</h1>
      </div>

      <div className="flex items-center space-x-4 mb-6">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              className="pl-10"
              placeholder="搜索标注内容..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        <Button variant="outline">
          <Filter className="h-5 w-5 mr-2" />
          筛选
        </Button>
      </div>

      {filteredAnnotations.length === 0 ? (
        <div className="text-center py-12">
          <Edit2 className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">暂无标注</h3>
          <p className="mt-1 text-sm text-gray-500">
            上传文档后开始标注
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredAnnotations.map((ann) => (
            <AnnotationItem
              key={ann.id}
              content={ann.content}
              originalContent={ann.originalContent}
              confidence={ann.confidence}
              status={ann.status}
              isEditing={editingId === ann.id}
              onEdit={() => setEditingId(ann.id)}
              onSave={(newContent) => handleSave(ann.id, newContent)}
              onCancel={() => setEditingId(null)}
            />
          ))}
        </div>
      )}

      {saving && (
        <LoadingOverlay message="正在保存修改..." />
      )}
    </div>
  );
} 
import { NextRequest, NextResponse } from "next/server";
import { SERVICES } from "@/config";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = params;
  const { searchParams } = new URL(request.url);
  const model = searchParams.get('model');
  
  let apiUrl = `${SERVICES.API.BASE_URL}/documents/${id}/ocr`;
  if (model) {
    apiUrl += `?model=${model}`;
  }
  
  console.log('Fetching OCR results from:', apiUrl);
  
  try {
    const response = await fetch(apiUrl, {
      cache: 'no-store'
    });
    
    console.log('OCR API response status:', response.status);

    if (!response.ok) {
      const errorData = await response.text();
      console.error('OCR API error response:', errorData);
      throw new Error(`API returned ${response.status}: ${errorData}`);
    }

    const data = await response.json();
    console.log('OCR API response data:', data);
    
    const responseData = NextResponse.json({
      success: true,
      data
    });
    
    // 添加缓存控制头
    responseData.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    responseData.headers.set('Pragma', 'no-cache');
    responseData.headers.set('Expires', '0');
    
    return responseData;
  } catch (error) {
    console.error('Error fetching OCR results:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { MongoClient, ObjectId } from 'mongodb';
import { MONGODB } from '@/config';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const client = new MongoClient(MONGODB.URI);
  
  try {
    await client.connect();
    const db = client.db(MONGODB.DB_NAME);
    const collection = db.collection(MONGODB.COLLECTIONS.OCR_LABELS);
    
    const labels = await collection.findOne({ document_id: params.id });
    
    if (!labels) {
      return NextResponse.json({
        success: true,
        data: { labels: [] }
      });
    }
    
    return NextResponse.json({
      success: true,
      data: labels
    });
  } catch (error) {
    console.error('获取标注失败:', error);
    return NextResponse.json({
      success: false,
      error: '获取标注失败'
    }, { status: 500 });
  } finally {
    await client.close();
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const client = new MongoClient(MONGODB.URI);
  
  try {
    const body = await request.json();
    const { labels } = body;
    
    await client.connect();
    const db = client.db(MONGODB.DB_NAME);
    const collection = db.collection(MONGODB.COLLECTIONS.OCR_LABELS);
    
    // 更新或创建标注
    await collection.updateOne(
      { document_id: params.id },
      {
        $set: {
          document_id: params.id,
          labels,
          updated_at: new Date()
        }
      },
      { upsert: true }
    );
    
    return NextResponse.json({
      success: true,
      message: '保存标注成功'
    });
  } catch (error) {
    console.error('保存标注失败:', error);
    return NextResponse.json({
      success: false,
      error: '保存标注失败'
    }, { status: 500 });
  } finally {
    await client.close();
  }
} 
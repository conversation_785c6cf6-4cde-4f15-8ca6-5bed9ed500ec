import { NextRequest, NextResponse } from "next/server";
import { MongoClient, ObjectId } from "mongodb";
import { MONGODB } from "@/config";

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  const client = new MongoClient(MONGODB.URI);
  
  try {
    await client.connect();
    const db = client.db(MONGODB.DB_NAME);
    const collection = db.collection(MONGODB.COLLECTIONS.OCR_FILES);
    
    const document = await collection.findOne({ _id: new ObjectId(params.id) });
    
    if (!document) {
      return NextResponse.json({
        success: false,
        error: "文档不存在"
      }, { status: 404 });
    }
    
    return NextResponse.json({
      success: true,
      data: document
    });
  } catch (error) {
    console.error("获取文档详情失败:", error);
    if (error instanceof Error) {
      console.error("Error details:", {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    }
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "获取文档详情失败"
    }, { status: 500 });
  } finally {
    await client.close();
  }
} 
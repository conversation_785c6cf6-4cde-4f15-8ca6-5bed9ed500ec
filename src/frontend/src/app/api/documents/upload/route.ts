import { NextRequest, NextResponse } from "next/server";
import { SERVICES } from "@/config";

export async function POST(request: NextRequest) {
  const apiUrl = `${SERVICES.API.BASE_URL}/documents/upload`;
  console.log('Uploading document to:', apiUrl);
  
  try {
    const formData = await request.formData();
    const file = formData.get('file');
    console.log('Uploading file:', {
      name: file instanceof File ? file.name : null,
      type: file instanceof File ? file.type : null,
      size: file instanceof File ? file.size : null
    });
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      body: formData
    });
    console.log('Response status:', response.status);

    if (!response.ok) {
      const errorData = await response.text();
      console.error('Error response:', errorData);
      throw new Error(`API returned ${response.status}: ${errorData}`);
    }

    const data = await response.json();
    console.log('Upload response:', data);
    return NextResponse.json({
      success: true,
      data
    });
  } catch (error) {
    console.error("上传文件失败:", error);
    if (error instanceof Error) {
      console.error("Error details:", {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    }
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "上传文件失败"
    }, { status: 500 });
  }
} 
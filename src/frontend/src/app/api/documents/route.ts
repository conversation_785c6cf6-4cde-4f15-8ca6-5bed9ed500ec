import { NextResponse } from "next/server";
import { SERVICES } from "@/config";
import { ApiResponse, Document } from "@/lib/types";

export async function GET() {
  const apiUrl = `${SERVICES.API.BASE_URL}/documents`;
  console.log('Fetching documents from:', apiUrl);
  
  try {
    const response = await fetch(apiUrl, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (!response.ok) {
      const errorData = await response.text();
      console.error('Error response:', errorData);
      throw new Error(`API returned ${response.status}: ${errorData}`);
    }
    
    const data = await response.json();
    console.log('Documents data:', data);
    
    if (data.success) {
      return NextResponse.json<ApiResponse<Document[]>>({
        success: true,
        data: data.data
      });
    } else {
      console.error('API returned success: false:', data);
      return NextResponse.json<ApiResponse<Document[]>>({
        success: false,
        message: data.message || '获取文档列表失败'
      }, { status: data.code || 500 });
    }
  } catch (error) {
    console.error('Error fetching documents:', error);
    if (error instanceof Error) {
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    }
    
    return NextResponse.json<ApiResponse<Document[]>>({
      success: false,
      message: error instanceof Error ? error.message : '获取文档列表失败'
    }, { status: 500 });
  }
} 
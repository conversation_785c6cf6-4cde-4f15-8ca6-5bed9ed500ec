import { NextResponse } from 'next/server'

export async function POST(request: Request) {
  try {
    const data = await request.json()
    const { boxes } = data

    if (!boxes || !Array.isArray(boxes)) {
      return NextResponse.json(
        { error: '无效的标注数据' },
        { status: 400 }
      )
    }

    // 这里应该调用后端API保存标注
    // const response = await fetch('http://localhost:8000/annotations', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //   },
    //   body: JSON.stringify({ boxes })
    // })
    
    // 模拟保存成功
    return NextResponse.json({ message: '保存成功' })
  } catch (error) {
    console.error('保存错误:', error)
    return NextResponse.json(
      { error: '保存失败' },
      { status: 500 }
    )
  }
} 
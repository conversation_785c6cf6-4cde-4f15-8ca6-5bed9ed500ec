import { NextResponse } from 'next/server'

export async function POST(request: Request) {
  try {
    const formData = await request.formData()
    const files = formData.getAll('files')

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: '没有上传文件' },
        { status: 400 }
      )
    }

    // 这里应该调用后端API处理文件上传
    // const response = await fetch('http://localhost:8000/upload', {
    //   method: 'POST',
    //   body: formData
    // })
    
    // 模拟上传成功
    return NextResponse.json({ message: '上传成功' })
  } catch (error) {
    console.error('上传错误:', error)
    return NextResponse.json(
      { error: '上传失败' },
      { status: 500 }
    )
  }
} 
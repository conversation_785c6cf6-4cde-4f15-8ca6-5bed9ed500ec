import { NextResponse } from 'next/server'

export async function POST(request: Request) {
  try {
    const formData = await request.formData()
    const file = formData.get('file')

    if (!file) {
      return NextResponse.json(
        { error: '没有上传文件' },
        { status: 400 }
      )
    }

    console.log('收到文件上传请求:', {
      name: file instanceof File ? file.name : 'unknown',
      type: file instanceof File ? file.type : 'unknown',
      size: file instanceof File ? file.size : 'unknown'
    })

    // 调用后端API处理文件上传
    const backendFormData = new FormData()
    backendFormData.append('file', file)

    const response = await fetch('http://localhost:8000/api/documents/upload', {
      method: 'POST',
      body: backendFormData
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('后端上传失败:', response.status, errorText)
      return NextResponse.json(
        { error: `上传失败: ${errorText}` },
        { status: response.status }
      )
    }

    const result = await response.json()
    console.log('后端上传成功:', result)

    return NextResponse.json({
      success: true,
      data: result.data,
      fileId: result.data?._id || result.data?.id
    })
  } catch (error) {
    console.error('上传错误:', error)
    return NextResponse.json(
      { error: '上传失败' },
      { status: 500 }
    )
  }
}
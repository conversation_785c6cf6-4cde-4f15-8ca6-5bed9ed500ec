import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('收到结构化OCR请求')

    // 获取上传的文件
    const formData = await request.formData()
    const file = formData.get('file') as File

    console.log('解析到文件:', {
      name: file?.name,
      type: file?.type,
      size: file?.size
    })

    if (!file) {
      console.log('没有文件')
      return NextResponse.json(
        { error: '没有上传文件' },
        { status: 400 }
      )
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/bmp', 'image/tiff', 'image/heic', 'image/heif']
    if (!allowedTypes.includes(file.type)) {
      console.log('文件类型不支持:', file.type)
      return NextResponse.json(
        { error: '不支持的文件类型' },
        { status: 400 }
      )
    }

    // 转发到后端API
    const backendFormData = new FormData()
    backendFormData.append('file', file)

    console.log('转发到后端...')
    const backendResponse = await fetch('http://localhost:8000/api/ocr/structure-process', {
      method: 'POST',
      body: backendFormData,
    })

    console.log('后端响应状态:', backendResponse.status)

    if (!backendResponse.ok) {
      const errorData = await backendResponse.json().catch(() => ({}))
      console.log('后端错误:', errorData)
      return NextResponse.json(
        { error: errorData.detail || '后端处理失败' },
        { status: backendResponse.status }
      )
    }

    const result = await backendResponse.json()
    console.log('后端处理成功，返回结果')

    return NextResponse.json(result)
  } catch (error) {
    console.error('结构化OCR处理错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

'use client';

import React, { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  Eye,
  UploadCloud,
  Pencil,
  FileText,
  Calendar,
  HardDrive
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Loading } from '@/components/ui/loading';
import { Error } from '@/components/ui/error';
import { UploadButton } from '@/components/core/upload/UploadButton';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { DocumentViewer } from "@/components/core/documents/DocumentViewer";
import { Document, ApiResponse, ApiError, ApiErrorResponse, ApiSuccessResponse, BaseResponse, isApiSuccess } from '@/lib/types';
import { SERVICES } from '@/config';
import { useToast } from '@/components/ui/use-toast';
import { cn, formatFileSize, formatDate } from '@/lib/utils';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { OCRLabelModal } from '@/components/ocr/OCRLabelModal';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import Link from 'next/link';

export default function DocumentsPage() {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [labelModalOpen, setLabelModalOpen] = useState(false);
  const { toast } = useToast();

  const fetchDocuments = async () => {
    try {
      console.log('开始获取文档列表...');
      setLoading(true);
      setError(null);

      // 添加时间戳避免缓存
      const timestamp = new Date().getTime()
      const response = await fetch(`/api/documents?t=${timestamp}`, {
        cache: 'no-store'
      });
      console.log('文档列表响应状态:', response.status);

      let responseData: unknown;
      try {
        responseData = await response.json();
      } catch (parseError) {
        console.error('解析响应数据失败:', parseError);
        throw new ApiError('解析响应数据失败');
      }

      // 验证响应数据结构
      if (!responseData || typeof responseData !== 'object' || !('success' in responseData)) {
        throw new ApiError('无效的响应数据格式');
      }

      const typedResponse = responseData as ApiResponse<Document[]>;

      if (!response.ok || !isApiSuccess(typedResponse)) {
        console.error('获取文档列表失败:', typedResponse);
        const errorResponse = typedResponse as BaseResponse;
        throw new ApiError(errorResponse.message || '获取文档列表失败');
      }

      console.log('文档列表数据:', typedResponse);
      setDocuments(typedResponse.data);
    } catch (err: unknown) {
      console.error('获取文档列表出错:', err);
      if (err instanceof ApiError) {
        setError(err.message);
      } else if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('获取文档列表失败，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDocuments();
  }, []);

  const handleFileUpload = async (file: File) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${SERVICES.API.BASE_URL}/documents/upload`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new ApiError(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const result = await response.json() as ApiResponse<Document>;
      
      if (!isApiSuccess(result)) {
        throw new ApiError(result.message);
      }

      toast({
        title: '上传成功',
        description: '文件已成功上传'
      });
      
      setUploadModalOpen(false);
      fetchDocuments();
    } catch (error: unknown) {
      let errorMessage = '上传文件时出错';
      
      if (error && typeof error === 'object') {
        const errorObj = error as { message?: string };
        if (errorObj.message) {
          errorMessage = errorObj.message;
        }
      }
      
      toast({
        title: '上传失败',
        description: errorMessage,
        variant: 'destructive'
      });
    }
  };

  const handleViewDocument = (document: Document) => {
    setSelectedDocument(document);
    setViewModalOpen(true);
  };

  const handleLabelDocument = (document: Document) => {
    setSelectedDocument(document);
    setLabelModalOpen(true);
  };

  const getFileExtension = (filename: string) => {
    const ext = filename.split('.').pop()?.toLowerCase() || '';
    return ext.toUpperCase();
  };

  const getStatusDisplay = (status: string) => {
    const statusMap = {
      uploading: { text: '上传中', className: 'bg-yellow-100 text-yellow-800' },
      pending: { text: '待处理', className: 'bg-gray-100 text-gray-800' },
      processing: { text: '处理中', className: 'bg-blue-100 text-blue-800' },
      processed: { text: '已完成', className: 'bg-green-100 text-green-800' },
      completed: { text: '已完成', className: 'bg-green-100 text-green-800' },
      error: { text: '错误', className: 'bg-red-100 text-red-800' },
    };
    return statusMap[status as keyof typeof statusMap] || { text: status, className: 'bg-gray-100 text-gray-800' };
  };

  const filteredDocuments = documents.filter(doc =>
    doc.original_filename?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.filename?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">文档管理</h1>
          <UploadButton onUploadSuccess={fetchDocuments} />
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-4 w-1/4" />
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-20" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">文档管理</h1>
          <UploadButton onUploadSuccess={fetchDocuments} />
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="text-center py-8">
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
                <strong className="font-bold">加载失败：</strong>
                <span className="block sm:inline mt-1">{error}</span>
              </div>
              <Button onClick={fetchDocuments} variant="outline">
                重新加载
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">文档管理</h1>
        <UploadButton onUploadSuccess={fetchDocuments} />
      </div>

      {/* 搜索栏 */}
      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="搜索文档名称..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {documents.length === 0 ? (
        <Card>
          <CardContent className="p-12">
            <div className="text-center">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无文档</h3>
              <p className="text-gray-500 mb-6">开始上传您的第一个文档</p>
              <UploadButton onUploadSuccess={fetchDocuments} />
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[300px]">文件名</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead>大小</TableHead>
                  <TableHead>上传时间</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredDocuments.map((doc) => {
                  const statusInfo = getStatusDisplay(doc.status);
                  return (
                    <TableRow key={doc._id} className="hover:bg-gray-50">
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-3">
                          <FileText className="h-4 w-4 text-gray-400" />
                          <div>
                            <div className="font-medium text-gray-900">
                              {doc.original_filename || doc.filename}
                            </div>
                            {doc.ocr_text && (
                              <div className="text-xs text-gray-500 truncate max-w-[200px]">
                                {doc.ocr_text}
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {getFileExtension(doc.filename)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center text-sm text-gray-500">
                          <HardDrive className="h-3 w-3 mr-1" />
                          {formatFileSize(doc.file_size)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center text-sm text-gray-500">
                          <Calendar className="h-3 w-3 mr-1" />
                          {formatDate(doc.upload_time)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className={cn(
                          "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                          statusInfo.className
                        )}>
                          {statusInfo.text}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewDocument(doc)}
                            title="查看文档"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleLabelDocument(doc)}
                            title="标注文档"
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>

            {filteredDocuments.length === 0 && searchTerm && (
              <div className="text-center py-8">
                <p className="text-gray-500">没有找到匹配的文档</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 对话框 */}
      <Dialog open={viewModalOpen} onOpenChange={setViewModalOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>查看文档</DialogTitle>
          </DialogHeader>
          {selectedDocument && (
            <DocumentViewer document={selectedDocument} />
          )}
        </DialogContent>
      </Dialog>

      {selectedDocument && (
        <OCRLabelModal
          open={labelModalOpen}
          onOpenChange={setLabelModalOpen}
          document={selectedDocument}
          onSuccess={() => {
            setLabelModalOpen(false);
            fetchDocuments();
          }}
        />
      )}
    </div>
  );
}
'use client';

import React, { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  Eye,
  UploadCloud,
  Pencil
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Loading } from '@/components/ui/loading';
import { Error } from '@/components/ui/error';
import { FileUpload } from '@/components/core/upload/FileUpload';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { DocumentViewer } from "@/components/core/documents/DocumentViewer";
import { Document, ApiResponse, ApiError, ApiErrorResponse, ApiSuccessResponse, BaseResponse, isApiSuccess } from '@/lib/types';
import { SERVICES } from '@/config';
import { useToast } from '@/components/ui/use-toast';
import { cn, formatFileSize, formatDate } from '@/lib/utils';
import { Table } from 'antd';
import { OCRLabelModal } from '@/components/ocr/OCRLabelModal';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import Link from 'next/link';

export default function DocumentsPage() {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [labelModalOpen, setLabelModalOpen] = useState(false);
  const { toast } = useToast();

  const fetchDocuments = async () => {
    try {
      console.log('开始获取文档列表...');
      setLoading(true);
      setError(null);

      const response = await fetch('/api/documents');
      console.log('文档列表响应状态:', response.status);

      let responseData: unknown;
      try {
        responseData = await response.json();
      } catch (parseError) {
        console.error('解析响应数据失败:', parseError);
        throw new ApiError('解析响应数据失败');
      }

      // 验证响应数据结构
      if (!responseData || typeof responseData !== 'object' || !('success' in responseData)) {
        throw new ApiError('无效的响应数据格式');
      }

      const typedResponse = responseData as ApiResponse<Document[]>;

      if (!response.ok || !isApiSuccess(typedResponse)) {
        console.error('获取文档列表失败:', typedResponse);
        const errorResponse = typedResponse as BaseResponse;
        throw new ApiError(errorResponse.message || '获取文档列表失败');
      }

      console.log('文档列表数据:', typedResponse);
      setDocuments(typedResponse.data);
    } catch (err: unknown) {
      console.error('获取文档列表出错:', err);
      if (err instanceof ApiError) {
        setError(err.message);
      } else if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('获取文档列表失败，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDocuments();
  }, []);

  const handleFileUpload = async (file: File) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${SERVICES.API.BASE_URL}/documents/upload`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new ApiError(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const result = await response.json() as ApiResponse<Document>;
      
      if (!isApiSuccess(result)) {
        throw new ApiError(result.message);
      }

      toast({
        title: '上传成功',
        description: '文件已成功上传'
      });
      
      setUploadModalOpen(false);
      fetchDocuments();
    } catch (error: unknown) {
      let errorMessage = '上传文件时出错';
      
      if (error && typeof error === 'object') {
        const errorObj = error as { message?: string };
        if (errorObj.message) {
          errorMessage = errorObj.message;
        }
      }
      
      toast({
        title: '上传失败',
        description: errorMessage,
        variant: 'destructive'
      });
    }
  };

  const handleViewDocument = (document: Document) => {
    setSelectedDocument(document);
    setViewModalOpen(true);
  };

  const handleLabelDocument = (document: Document) => {
    setSelectedDocument(document);
    setLabelModalOpen(true);
  };

  const columns = [
    {
      title: '文件名',
      dataIndex: 'original_filename',
      key: 'original_filename',
      render: (text: string, record: Document) => (
        <div className="flex items-center">
          <span className="text-sm font-medium text-gray-900">{text}</span>
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'file_type',
      key: 'file_type',
      render: (text: string) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {text}
        </span>
      ),
    },
    {
      title: '大小',
      dataIndex: 'file_size',
      key: 'file_size',
      render: (size: number) => formatFileSize(size),
    },
    {
      title: '上传时间',
      dataIndex: 'upload_time',
      key: 'upload_time',
      render: (time: string) => formatDate(time),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          uploading: { text: '上传中', className: 'bg-blue-100 text-blue-800' },
          processing: { text: '处理中', className: 'bg-yellow-100 text-yellow-800' },
          processed: { text: '已处理', className: 'bg-green-100 text-green-800' },
          error: { text: '错误', className: 'bg-red-100 text-red-800' },
        };
        const { text, className } = statusMap[status as keyof typeof statusMap] || statusMap.error;
        return (
          <span className={cn("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium", className)}>
            {text}
          </span>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Document) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewDocument(record)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleLabelDocument(record)}
          >
            <Pencil className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  const filteredDocuments = documents.filter(doc =>
    doc.original_filename.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">文档列表</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="w-full">
              <CardContent className="p-4">
                <Skeleton className="h-4 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">文档列表</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">错误：</strong>
          <span className="block sm:inline">{error}</span>
        </div>
        <Button onClick={fetchDocuments} className="mt-4">重试</Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">文档列表</h1>
        <Link href="/upload">
          <Button>上传新文档</Button>
        </Link>
      </div>
      
      {documents.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">暂无文档</p>
          <Link href="/upload">
            <Button>上传第一个文档</Button>
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {documents.map((doc) => (
            <Link key={doc.id} href={`/annotate/${doc.id}`}>
              <Card className="w-full hover:shadow-lg transition-shadow">
                <CardContent className="p-4">
                  <h2 className="font-semibold mb-2">{doc.filename}</h2>
                  <p className="text-sm text-gray-500">
                    上传时间：{new Date(doc.created_at).toLocaleString()}
                  </p>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      )}
    </div>
  );
}
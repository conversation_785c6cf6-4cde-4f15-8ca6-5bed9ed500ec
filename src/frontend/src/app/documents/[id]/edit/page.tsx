'use client';

import { useEffect, useState } from 'react';
import { OCRLabelEditor } from '@/components/ocr/OCRLabelEditor';
import { SERVICES } from '@/config';
import { useRouter } from 'next/navigation';
import { message } from 'antd';

interface PageProps {
  params: {
    id: string;
  };
}

interface OCRBox {
  x: number;
  y: number;
  width: number;
  height: number;
  text?: string;
  confidence?: number;
}

export default function EditPage({ params }: PageProps) {
  const router = useRouter();
  const { id } = params;
  const [initialLabels, setInitialLabels] = useState<OCRBox[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadLabels();
  }, [id]);

  // 加载标注数据
  const loadLabels = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/documents/${id}/labels`);
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || '加载标注失败');
      }
      
      setInitialLabels(data.data?.labels || []);
    } catch (error) {
      message.error('加载标注失败');
      console.error('加载标注失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 保存标注
  const handleSave = async (labels: OCRBox[]) => {
    try {
      const response = await fetch(`/api/documents/${id}/labels`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ labels }),
      });

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.error || '保存标注失败');
      }

      message.success('保存成功');
      router.back();
    } catch (error) {
      message.error('保存标注失败');
      console.error('保存标注失败:', error);
    }
  };

  // 取消编辑
  const handleCancel = () => {
    router.back();
  };

  if (loading) {
    return <div className="flex items-center justify-center h-screen">加载中...</div>;
  }

  return (
    <OCRLabelEditor
      documentId={id}
      imageUrl={`${SERVICES.API.BASE_URL}/documents/${id}/image`}
      onSave={handleSave}
      onCancel={handleCancel}
      initialLabels={initialLabels}
    />
  );
} 
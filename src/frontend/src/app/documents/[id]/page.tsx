import { notFound } from "next/navigation";
import { readFile } from "fs/promises";
import { join } from "path";
import { DocumentViewer } from "@/components/core/documents/DocumentViewer";

interface DocumentPageProps {
  params: {
    id: string;
  };
}

async function getDocument(id: string) {
  try {
    const uploadDir = join(process.cwd(), "uploads");
    const files = await readFile(join(uploadDir, id));
    if (!files) {
      return null;
    }
    return files;
  } catch (error) {
    console.error("Error fetching document:", error);
    return null;
  }
}

export default async function DocumentPage({ params }: DocumentPageProps) {
  const document = await getDocument(params.id);

  if (!document) {
    notFound();
  }

  return (
    <div className="container mx-auto py-8">
      <DocumentViewer document={document} />
    </div>
  );
} 
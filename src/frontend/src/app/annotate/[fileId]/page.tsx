import { AnnotationEditor } from "@/components/core/annotation/AnnotationEditor"
import { Metadata } from "next"

type Props = {
  params: { fileId: string }
  searchParams: { [key: string]: string | string[] | undefined }
}

export const metadata: Metadata = {
  title: "Annotation Editor",
  description: "Edit annotations for your file"
}

export default async function Page({ params }: Props) {
  return (
    <main className="container mx-auto p-4">
      <AnnotationEditor fileId={params.fileId} />
    </main>
  )
} 
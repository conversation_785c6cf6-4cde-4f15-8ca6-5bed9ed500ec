'use client'

import { Navigation } from '@/components/layout/Navigation'
import { UploadButton } from '@/components/core/upload/UploadButton'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FileText, Image, Upload } from 'lucide-react'

export default function UploadPage() {
  return (
    <>
      <Navigation />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            上传文档
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            支持PDF、图片等格式的文件，系统将自动进行OCR识别
          </p>
        </div>

        <div className="mt-10 max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Upload className="h-5 w-5" />
                <span>选择文件上传</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center">
                <UploadButton />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <FileText className="h-4 w-4" />
                  <span>支持PDF、DOC、DOCX、TXT格式</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Image className="h-4 w-4" />
                  <span>支持PNG、JPG、JPEG、HEIC、HEIF格式</span>
                </div>
              </div>

              <div className="text-xs text-gray-500 text-center">
                <p>文件上传后将自动进行OCR识别，识别完成后可在文档列表中查看</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  )
}
'use client';

import React, { useState, useEffect } from 'react';
import { 
  Brain, 
  TrendingUp, 
  Target, 
  FileText, 
  Download, 
  RefreshCw,
  AlertCircle,
  CheckCircle,
  BarChart3,
  <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon,
  Search,
  Filter,
  Book
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface LearningReport {
  total_samples: number;
  accuracy_improvement: string;
  confidence_improvement: string;
  region_performance: Record<string, string>;
  common_errors: Array<{
    error_type: string;
    count: number;
    examples: string[];
  }>;
}

interface RegionAnalysis {
  region_type: string;
  total_samples: number;
  average_accuracy: number;
  best_preprocessing: Record<string, any>;
  improvement_trend: Array<{
    date: string;
    accuracy: number;
    confidence: number;
  }>;
}

const REGION_NAMES = {
  'single_column': '单栏文本',
  'left_column': '左栏文本',
  'right_column': '右栏文本',
  'table': '表格',
  'image': '图像',
  'header': '页眉',
  'footer': '页脚',
  'special': '特殊区域'
};

// 学习进度卡片组件
const ProgressCard = ({
  title,
  description,
  progress,
  total,
  icon: Icon
}: {
  title: string;
  description: string;
  progress: number;
  total: number;
  icon: any;
}) => (
  <Card className="p-6">
    <div className="flex items-start space-x-4">
      <div className="bg-blue-100 p-3 rounded-lg">
        <Icon className="h-6 w-6 text-blue-600" />
      </div>
      <div className="flex-1">
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        <p className="mt-1 text-sm text-gray-500">{description}</p>
        <div className="mt-4">
          <div className="flex items-center justify-between text-sm text-gray-500">
            <span>进度</span>
            <span>{Math.round((progress / total) * 100)}%</span>
          </div>
          <div className="mt-1 w-full h-2 bg-gray-200 rounded-full">
            <div
              className="h-2 bg-blue-600 rounded-full"
              style={{ width: `${(progress / total) * 100}%` }}
            />
          </div>
          <div className="mt-1 text-xs text-gray-500">
            {progress} / {total}
          </div>
        </div>
      </div>
    </div>
  </Card>
);

// 学习记录项组件
const LearningRecord = ({
  title,
  type,
  accuracy,
  time,
  status
}: {
  title: string;
  type: string;
  accuracy: number;
  time: string;
  status: string;
}) => (
  <Card className="p-4">
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        <span className={`
          inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
          ${status === '已完成' ? 'bg-green-100 text-green-800' : 
            status === '学习中' ? 'bg-yellow-100 text-yellow-800' : 
            'bg-gray-100 text-gray-800'}
        `}>
          {status}
        </span>
      </div>
      <div className="flex items-center space-x-4 text-sm text-gray-500">
        <span>类型: {type}</span>
        <span>准确率: {(accuracy * 100).toFixed(1)}%</span>
        <span>时间: {time}</span>
      </div>
    </div>
  </Card>
);

export default function LearningPage() {
  const [learningReport, setLearningReport] = useState<LearningReport | null>(null);
  const [regionAnalyses, setRegionAnalyses] = useState<Record<string, RegionAnalysis>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [searchQuery, setSearchQuery] = useState('');

  // 获取学习报告
  const fetchLearningReport = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/ocr/learning/report');
      if (!response.ok) {
        throw new Error('获取学习报告失败');
      }
      const data = await response.json();
      setLearningReport(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  // 获取区域分析
  const fetchRegionAnalysis = async (regionType: string) => {
    try {
      const response = await fetch(`/api/ocr/learning/region-analysis/${regionType}`);
      if (!response.ok) {
        throw new Error(`获取${REGION_NAMES[regionType as keyof typeof REGION_NAMES]}分析失败`);
      }
      const data = await response.json();
      setRegionAnalyses(prev => ({
        ...prev,
        [regionType]: data
      }));
    } catch (err) {
      console.error(`获取区域分析失败 (${regionType}):`, err);
    }
  };

  // 获取所有区域分析
  const fetchAllRegionAnalyses = async () => {
    const regionTypes = Object.keys(REGION_NAMES);
    await Promise.all(regionTypes.map(type => fetchRegionAnalysis(type)));
  };

  // 导出学习数据
  const exportLearningData = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/ocr/learning/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          output_path: `learning_export_${new Date().toISOString().split('T')[0]}.json`
        })
      });
      
      if (!response.ok) {
        throw new Error('导出学习数据失败');
      }
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `learning_export_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      setError(err instanceof Error ? err.message : '导出失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLearningReport();
    fetchAllRegionAnalyses();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-500 mx-auto" />
          <p className="mt-2 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (error) {
  return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto" />
          <p className="mt-2 text-red-600">{error}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => {
              fetchLearningReport();
              fetchAllRegionAnalyses();
            }}
          >
            重试
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">学习系统</h1>
        <p className="text-gray-600">
          查看OCR系统的学习进度和效果，分析识别准确率的提升情况。
            </p>
          </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <ProgressCard
          title="样本总数"
          description="已处理的文档样本数量"
          progress={learningReport?.total_samples || 0}
          total={1000}
          icon={FileText}
        />
        <ProgressCard
          title="准确率提升"
          description="OCR识别准确率的整体提升"
          progress={parseFloat(learningReport?.accuracy_improvement || '0')}
          total={100}
          icon={TrendingUp}
        />
        <ProgressCard
          title="置信度提升"
          description="OCR识别置信度的整体提升"
          progress={parseFloat(learningReport?.confidence_improvement || '0')}
          total={100}
          icon={Target}
        />
      </div>

      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-semibold text-gray-900">区域分析</h2>
      <div className="flex items-center space-x-4">
          <Input
            type="text"
              placeholder="搜索区域..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
              className="w-64"
          />
            <Button
              variant="outline"
              onClick={() => {
                fetchLearningReport();
                fetchAllRegionAnalyses();
              }}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新
            </Button>
            <Button onClick={exportLearningData}>
              <Download className="h-4 w-4 mr-2" />
              导出数据
        </Button>
      </div>
        </div>
      </div>
    </div>
  );
} 
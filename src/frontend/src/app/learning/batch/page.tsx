'use client';

import React, { useState, useEffect } from 'react';
import { 
  Upload, 
  Play, 
  Pause, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  Clock,
  FileText,
  Database,
  Settings,
  Download,
  Trash2
} from 'lucide-react';

interface BatchTask {
  task_id: string;
  status: string;
  start_time: string;
  end_time?: string;
  stats_summary?: {
    total_files: number;
    processed_files: number;
    total_samples: number;
  };
}

interface BatchLearningStats {
  total_files: number;
  processed_files: number;
  failed_files: number;
  total_samples: number;
  accuracy_improvements: number[];
  processing_times: number[];
  start_time: string;
  end_time?: string;
}

export default function BatchLearningPage() {
  const [tasks, setTasks] = useState<BatchTask[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 批量学习表单状态
  const [dataDirectory, setDataDirectory] = useState('');
  const [maxWorkers, setMaxWorkers] = useState(4);
  const [batchSize, setBatchSize] = useState(10);
  const [enableOptimization, setEnableOptimization] = useState(true);
  
  // CSV迁移表单状态
  const [csvFilePath, setCsvFilePath] = useState('');
  
  // 获取任务列表
  const fetchTasks = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/ocr/batch-learning/tasks');
      if (!response.ok) {
        throw new Error('获取任务列表失败');
      }
      const data = await response.json();
      setTasks(data.tasks);
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  // 启动批量学习
  const startBatchLearning = async () => {
    if (!dataDirectory.trim()) {
      setError('请输入数据目录路径');
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/ocr/batch-learning/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          data_directory: dataDirectory,
          max_workers: maxWorkers,
          batch_size: batchSize,
          enable_preprocessing_optimization: enableOptimization
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '启动批量学习失败');
      }

      const result = await response.json();
      alert(`批量学习任务已启动，任务ID: ${result.task_id}`);
      
      // 刷新任务列表
      await fetchTasks();
      
      // 清空表单
      setDataDirectory('');
      
    } catch (err) {
      setError(err instanceof Error ? err.message : '启动失败');
    } finally {
      setLoading(false);
    }
  };

  // 启动CSV迁移
  const startCsvMigration = async () => {
    if (!csvFilePath.trim()) {
      setError('请输入CSV文件路径');
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/ocr/batch-learning/migrate-csv', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          csv_file_path: csvFilePath
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'CSV迁移失败');
      }

      const result = await response.json();
      alert(`CSV迁移任务已启动，任务ID: ${result.task_id}`);
      
      // 刷新任务列表
      await fetchTasks();
      
      // 清空表单
      setCsvFilePath('');
      
    } catch (err) {
      setError(err instanceof Error ? err.message : '迁移失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除任务
  const deleteTask = async (taskId: string) => {
    if (!confirm('确定要删除这个任务吗？')) {
      return;
    }

    try {
      const response = await fetch(`/api/ocr/batch-learning/task/${taskId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('删除任务失败');
      }

      // 刷新任务列表
      await fetchTasks();
      
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除失败');
    }
  };

  // 获取任务状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'running':
        return <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'pending': '等待中',
      'running': '运行中',
      'completed': '已完成',
      'failed': '失败'
    };
    return statusMap[status] || status;
  };

  // 格式化时间
  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString('zh-CN');
  };

  useEffect(() => {
    fetchTasks();
    
    // 设置定时刷新
    const interval = setInterval(fetchTasks, 5000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Database className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold">批量学习</h1>
            <p className="text-gray-600">处理历史标注数据，批量优化OCR参数</p>
          </div>
        </div>
        
        <button 
          onClick={fetchTasks} 
          disabled={loading}
          className="flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          刷新
        </button>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <XCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* 操作面板 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 批量学习 */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center mb-4">
            <Play className="h-6 w-6 text-blue-600 mr-2" />
            <h2 className="text-xl font-semibold">启动批量学习</h2>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                数据目录路径
              </label>
              <input
                type="text"
                value={dataDirectory}
                onChange={(e) => setDataDirectory(e.target.value)}
                placeholder="例如: /path/to/historical/annotations"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  最大工作线程
                </label>
                <input
                  type="number"
                  value={maxWorkers}
                  onChange={(e) => setMaxWorkers(parseInt(e.target.value) || 4)}
                  min="1"
                  max="16"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  批次大小
                </label>
                <input
                  type="number"
                  value={batchSize}
                  onChange={(e) => setBatchSize(parseInt(e.target.value) || 10)}
                  min="1"
                  max="100"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="enableOptimization"
                checked={enableOptimization}
                onChange={(e) => setEnableOptimization(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="enableOptimization" className="ml-2 block text-sm text-gray-900">
                启用预处理参数优化
              </label>
            </div>
            
            <button
              onClick={startBatchLearning}
              disabled={loading || !dataDirectory.trim()}
              className="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Play className="h-4 w-4 mr-2" />
              启动批量学习
            </button>
          </div>
        </div>

        {/* CSV数据迁移 */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center mb-4">
            <Upload className="h-6 w-6 text-green-600 mr-2" />
            <h2 className="text-xl font-semibold">CSV数据迁移</h2>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                CSV文件路径
              </label>
              <input
                type="text"
                value={csvFilePath}
                onChange={(e) => setCsvFilePath(e.target.value)}
                placeholder="例如: /path/to/historical_data.csv"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              />
            </div>
            
            <div className="text-sm text-gray-600">
              <p className="mb-2">CSV文件应包含以下列：</p>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li>image_path - 图像文件路径</li>
                <li>region_type - 区域类型</li>
                <li>x1, y1, x2, y2 - 区域边界框坐标</li>
                <li>original_text - 原始OCR文本</li>
                <li>corrected_text - 修正后的文本</li>
                <li>confidence - 置信度</li>
              </ul>
            </div>
            
            <button
              onClick={startCsvMigration}
              disabled={loading || !csvFilePath.trim()}
              className="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Upload className="h-4 w-4 mr-2" />
              开始迁移
            </button>
          </div>
        </div>
      </div>

      {/* 任务列表 */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">任务列表</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            批量学习和数据迁移任务的执行状态
          </p>
        </div>
        
        {tasks.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">暂无任务</h3>
            <p className="mt-1 text-sm text-gray-500">启动批量学习或数据迁移来创建任务</p>
          </div>
        ) : (
          <div className="border-t border-gray-200">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      任务ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      开始时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      进度
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {tasks.map((task) => (
                    <tr key={task.task_id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(task.status)}
                          <span className="ml-2 text-sm text-gray-900">
                            {getStatusText(task.status)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-500">
                        {task.task_id.substring(0, 8)}...
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatTime(task.start_time)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {task.stats_summary ? (
                          <div>
                            <div className="text-sm">
                              {task.stats_summary.processed_files}/{task.stats_summary.total_files} 文件
                            </div>
                            <div className="text-xs text-gray-400">
                              {task.stats_summary.total_samples} 样本
                            </div>
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => deleteTask(task.task_id)}
                          className="text-red-600 hover:text-red-900"
                          title="删除任务"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 
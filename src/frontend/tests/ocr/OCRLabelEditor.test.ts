import { test, expect, type Page } from '@playwright/test';
import fs from 'fs';
import path from 'path';

test.describe('OCR 标注编辑器', () => {
  let documentId: string;

  test.beforeAll(async ({ request }) => {
    // 上传测试图片
    const imageBuffer = fs.readFileSync('/Users/<USER>/Downloads/IMG_1323.heic');
    const formData = new FormData();
    formData.append('file', new Blob([imageBuffer]), 'IMG_1323.heic');

    const response = await request.post('/api/documents', {
      multipart: {
        file: {
          name: 'file',
          mimeType: 'image/heic',
          buffer: imageBuffer,
        }
      }
    });
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    documentId = data.id;
  });

  test.beforeEach(async ({ page }: { page: Page }) => {
    // 导航到标注编辑页面
    await page.goto(`/documents/${documentId}/edit`);
    // 等待图片加载完成
    await page.waitForSelector('canvas', { timeout: 60000 });
  });

  test.afterAll(async ({ request }) => {
    // 清理测试数据
    if (documentId) {
      await request.delete(`/api/documents/${documentId}`);
    }
  });

  test('应该能够选择和编辑标注框属性', async ({ page }: { page: Page }) => {
    // 点击第一个标注框
    await page.click('canvas', { position: { x: 100, y: 100 } });

    // 验证属性面板显示
    await expect(page.locator('.text-gray-500.text-center')).not.toBeVisible();

    // 编辑文本内容
    const textArea = page.locator('textarea');
    await textArea.click();
    await textArea.fill('测试文本');
    await expect(textArea).toHaveValue('测试文本');

    // 选择区域类型
    const typeSelect = page.locator('.ant-select');
    await typeSelect.click();
    await page.click('.ant-select-item[title="表格"]');
    await expect(typeSelect).toHaveText('表格');

    // 调整置信度
    const confidenceInput = page.locator('input[type="number"]').first();
    await confidenceInput.click();
    await confidenceInput.fill('0.85');
    await expect(confidenceInput).toHaveValue('0.85');

    // 调整坐标
    const xInput = page.locator('input[type="number"]').nth(1);
    await xInput.click();
    await xInput.fill('150');
    await expect(xInput).toHaveValue('150');

    // 保存更改
    await page.click('button[title="保存标注"]');
    await expect(page.locator('.ant-message-success')).toBeVisible();
  });

  test('应该能够删除标注框', async ({ page }: { page: Page }) => {
    // 点击第一个标注框
    await page.click('canvas', { position: { x: 100, y: 100 } });

    // 点击删除按钮
    await page.click('button[title="删除选中的标注框"]');

    // 验证属性面板显示"请选择一个标注框以编辑属性"
    await expect(page.locator('.text-gray-500.text-center')).toBeVisible();
    await expect(page.locator('.text-gray-500.text-center')).toHaveText('请选择一个标注框以编辑属性');
  });

  test('标注框应该根据类型显示不同颜色', async ({ page }: { page: Page }) => {
    // 点击第一个标注框
    await page.click('canvas', { position: { x: 100, y: 100 } });

    // 选择不同的区域类型
    const typeSelect = page.locator('.ant-select');
    await typeSelect.click();
    await page.click('.ant-select-item[title="表格"]');

    // 验证标注框颜色变化
    // 注意：由于 canvas 的特性，我们需要使用 canvas 的截图来验证颜色
    const canvas = page.locator('canvas').first();
    const screenshot = await canvas.screenshot();
    // 这里可以添加图像处理逻辑来验证颜色
    expect(screenshot).toBeTruthy();
  });

  test('输入验证', async ({ page }: { page: Page }) => {
    // 点击第一个标注框
    await page.click('canvas', { position: { x: 100, y: 100 } });

    // 测试置信度范围验证
    const confidenceInput = page.locator('input[type="number"]').first();
    await confidenceInput.click();
    await confidenceInput.fill('2');
    await expect(confidenceInput).toHaveValue('1');
    await confidenceInput.fill('-1');
    await expect(confidenceInput).toHaveValue('0');

    // 测试宽度和高度的最小值验证
    const widthInput = page.locator('input[type="number"]').nth(3);
    await widthInput.click();
    await widthInput.fill('0');
    await expect(widthInput).toHaveValue('1');
  });
}); 
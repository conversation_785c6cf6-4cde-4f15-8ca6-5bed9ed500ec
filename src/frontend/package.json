{"name": "sake-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@tailwindcss/typography": "^0.5.16", "@types/axios": "^0.9.36", "@types/mongodb": "^4.0.6", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "antd": "^5.26.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "heic2any": "^0.0.4", "konva": "^9.3.20", "lucide-react": "^0.515.0", "mongodb": "^6.17.0", "next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-konva": "^19.0.6", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1", "task-master-ai": "^0.16.2", "uuid": "^11.1.0"}, "devDependencies": {"@playwright/test": "^1.53.0", "@tailwindcss/forms": "^0.5.10", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}
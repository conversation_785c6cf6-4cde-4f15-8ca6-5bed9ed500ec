#!/usr/bin/env python3
"""
文件质量检测和过滤服务
"""

import os
import cv2
import numpy as np
from PIL import Image
from typing import Dict, List, Tuple, Optional, Union
from pathlib import Path
import logging
from pillow_heif import register_heif_opener
import io

from src.backend.database.database import Database
from src.backend.minio_client import MinioClient

# 注册HEIF/HEIC格式支持
register_heif_opener()

class FileQualityChecker:
    """文件质量检查类"""
    
    def __init__(self):
        """初始化质量检查器"""
        self.db = Database.get_mongodb()
        self.minio = MinioClient()
        
        # 质量检查参数
        self.min_resolution = (800, 600)  # 最小分辨率
        self.min_dpi = 200  # 最小DPI
        self.max_file_size = 10 * 1024 * 1024  # 最大文件大小（10MB）
        self.min_brightness = 40  # 最小亮度
        self.max_brightness = 220  # 最大亮度
        self.min_contrast = 30  # 最小对比度
        
    async def check_quality(self, file_id: str) -> bool:
        """
        检查文件质量
        
        Args:
            file_id: 文件ID
            
        Returns:
            bool: 是否通过质量检查
        """
        try:
            # 获取文件信息
            file_info = await self.db["files"].find_one({"_id": file_id})
            if not file_info:
                raise ValueError(f"文件不存在: {file_id}")
                
            # 获取文件内容
            file_data = await self.minio.get_file(file_info["bucket"], file_info["object_name"])
            
            # 检查文件大小
            if len(file_data) > self.max_file_size:
                return False
                
            # 转换为OpenCV格式
            nparr = np.frombuffer(file_data, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            if img is None:
                return False
                
            # 检查分辨率
            height, width = img.shape[:2]
            if width < self.min_resolution[0] or height < self.min_resolution[1]:
                return False
                
            # 检查DPI
            dpi = self._get_image_dpi(file_data)
            if dpi and dpi < self.min_dpi:
                return False
                
            # 检查亮度和对比度
            brightness, contrast = self._check_brightness_contrast(img)
            if not (self.min_brightness <= brightness <= self.max_brightness):
                return False
            if contrast < self.min_contrast:
                return False
                
            return True
            
        except Exception as e:
            print(f"质量检查失败: {str(e)}")
            return False
    
    def _get_image_dpi(self, file_data: bytes) -> Optional[float]:
        """
        获取图像DPI
        
        Args:
            file_data: 文件数据
            
        Returns:
            float: DPI值
        """
        try:
            img = Image.open(io.BytesIO(file_data))
            if "dpi" in img.info:
                return min(img.info["dpi"])  # 返回x和y方向DPI的较小值
            return None
        except:
            return None
    
    def _check_brightness_contrast(self, img: np.ndarray) -> Tuple[float, float]:
        """
        检查图像亮度和对比度
        
        Args:
            img: OpenCV图像
            
        Returns:
            Tuple[float, float]: 亮度和对比度
        """
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 计算亮度（平均像素值）
        brightness = np.mean(gray)
        
        # 计算对比度（像素值的标准差）
        contrast = np.std(gray)
        
        return brightness, contrast
    
    def check_file_quality(self, file_path: Union[str, Path]) -> Tuple[bool, Dict]:
        """
        检查文件质量
        
        Args:
            file_path: 文件路径
            
        Returns:
            Tuple[bool, Dict]: (是否通过质量检查, 详细质量报告)
        """
        file_path = Path(file_path)
        
        # 检查文件大小
        size_check, size_report = self._check_file_size(file_path)
        if not size_check:
            return False, {'file_size': size_report}
        
        try:
            # 读取图像
            image = self._load_image(file_path)
            if image is None:
                return False, {'error': '无法读取图像文件'}
            
            # 进行各项质量检查
            quality_report = {
                'file_size': size_report,
                'resolution': self._check_resolution(image),
                'blur': self._check_blur(image),
                'brightness': self._check_brightness(image),
                'contrast': self._check_contrast(image),
                'noise': self._check_noise(image)
            }
            
            # 判断是否通过所有质量检查
            passed = all([
                quality_report['resolution']['passed'],
                quality_report['blur']['passed'],
                quality_report['brightness']['passed'],
                quality_report['contrast']['passed'],
                quality_report['noise']['passed'],
                quality_report['file_size']['passed']
            ])
            
            return passed, quality_report
            
        except Exception as e:
            self.logger.error(f"质量检查失败: {e}")
            return False, {'error': str(e)}
    
    def _load_image(self, file_path: Path) -> Optional[np.ndarray]:
        """加载图像文件"""
        try:
            if file_path.suffix.lower() in ['.heic', '.heif']:
                # 使用PIL读取HEIC格式
                pil_image = Image.open(file_path)
                if pil_image.mode != 'RGB':
                    pil_image = pil_image.convert('RGB')
                image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            else:
                # 使用OpenCV读取其他格式
                image = cv2.imread(str(file_path))
            
            return image
        except Exception as e:
            self.logger.error(f"图像加载失败: {e}")
            return None
    
    def _check_file_size(self, file_path: Path) -> Tuple[bool, Dict]:
        """检查文件大小"""
        try:
            size_bytes = os.path.getsize(file_path)
            size_kb = size_bytes / 1024
            size_mb = size_kb / 1024
            
            min_kb = self.quality_thresholds['file_size']['min_kb']
            max_mb = self.quality_thresholds['file_size']['max_mb']
            
            passed = size_kb >= min_kb and size_mb <= max_mb
            
            return passed, {
                'passed': passed,
                'size_kb': round(size_kb, 2),
                'size_mb': round(size_mb, 2),
                'min_kb': min_kb,
                'max_mb': max_mb
            }
        except Exception as e:
            self.logger.error(f"文件大小检查失败: {e}")
            return False, {
                'passed': False,
                'error': str(e)
            }
    
    def _check_resolution(self, image: np.ndarray) -> Dict:
        """检查图像分辨率"""
        height, width = image.shape[:2]
        min_width = self.quality_thresholds['resolution']['min_width']
        min_height = self.quality_thresholds['resolution']['min_height']
        
        passed = width >= min_width and height >= min_height
        
        return {
            'passed': passed,
            'width': width,
            'height': height,
            'min_width': min_width,
            'min_height': min_height
        }
    
    def _check_blur(self, image: np.ndarray) -> Dict:
        """检查图像模糊度"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 计算Laplacian方差
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            threshold = self.quality_thresholds['blur']['threshold']
            
            passed = laplacian_var >= threshold
            
            return {
                'passed': passed,
                'value': round(laplacian_var, 2),
                'threshold': threshold
            }
        except Exception as e:
            self.logger.error(f"模糊度检查失败: {e}")
            return {
                'passed': False,
                'error': str(e)
            }
    
    def _check_brightness(self, image: np.ndarray) -> Dict:
        """检查图像亮度"""
        try:
            # 转换为HSV颜色空间
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            brightness = hsv[..., 2].mean()
            
            min_brightness = self.quality_thresholds['brightness']['min']
            max_brightness = self.quality_thresholds['brightness']['max']
            
            passed = min_brightness <= brightness <= max_brightness
            
            return {
                'passed': passed,
                'value': round(brightness, 2),
                'min': min_brightness,
                'max': max_brightness
            }
        except Exception as e:
            self.logger.error(f"亮度检查失败: {e}")
            return {
                'passed': False,
                'error': str(e)
            }
    
    def _check_contrast(self, image: np.ndarray) -> Dict:
        """检查图像对比度"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 计算标准差作为对比度度量
            contrast = gray.std()
            min_contrast = self.quality_thresholds['contrast']['min']
            
            passed = contrast >= min_contrast
            
            return {
                'passed': passed,
                'value': round(contrast, 2),
                'min': min_contrast
            }
        except Exception as e:
            self.logger.error(f"对比度检查失败: {e}")
            return {
                'passed': False,
                'error': str(e)
            }
    
    def _check_noise(self, image: np.ndarray) -> Dict:
        """检查图像噪声水平"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 使用中值滤波作为参考
            denoised = cv2.medianBlur(gray, 3)
            
            # 计算原图与去噪图之间的差异作为噪声度量
            noise_level = np.abs(gray - denoised).mean()
            max_noise = self.quality_thresholds['noise']['max']
            
            passed = noise_level <= max_noise
            
            return {
                'passed': passed,
                'value': round(noise_level, 2),
                'max': max_noise
            }
        except Exception as e:
            self.logger.error(f"噪声检查失败: {e}")
            return {
                'passed': False,
                'error': str(e)
            }
    
    def set_quality_thresholds(self, thresholds: Dict) -> None:
        """
        设置质量检查阈值
        
        Args:
            thresholds: 新的阈值设置
        """
        self.quality_thresholds.update(thresholds)
    
    def get_quality_thresholds(self) -> Dict:
        """
        获取当前质量检查阈值
        
        Returns:
            Dict: 当前阈值设置
        """
        return self.quality_thresholds.copy()
    
    def get_improvement_suggestions(self, quality_report: Dict) -> List[str]:
        """
        根据质量报告生成改进建议
        
        Args:
            quality_report: 质量检查报告
            
        Returns:
            List[str]: 改进建议列表
        """
        suggestions = []
        
        if 'error' in quality_report:
            suggestions.append(f"文件处理错误: {quality_report['error']}")
            return suggestions
        
        # 检查文件大小
        if not quality_report['file_size']['passed']:
            size_kb = quality_report['file_size']['size_kb']
            min_kb = quality_report['file_size']['min_kb']
            max_mb = quality_report['file_size']['max_mb']
            if size_kb < min_kb:
                suggestions.append(f"文件太小 ({size_kb:.2f}KB), 建议使用更高质量的扫描设置")
            elif size_kb > max_mb * 1024:
                suggestions.append(f"文件太大 ({quality_report['file_size']['size_mb']:.2f}MB), 建议适当压缩")
        
        # 检查分辨率
        if not quality_report['resolution']['passed']:
            width = quality_report['resolution']['width']
            height = quality_report['resolution']['height']
            min_width = quality_report['resolution']['min_width']
            min_height = quality_report['resolution']['min_height']
            suggestions.append(
                f"图像分辨率太低 ({width}x{height}), "
                f"建议使用至少 {min_width}x{min_height} 的分辨率"
            )
        
        # 检查模糊度
        if not quality_report['blur']['passed']:
            suggestions.append(
                "图像较模糊, 建议：\n"
                "1. 确保相机对焦准确\n"
                "2. 保持拍摄时手机稳定\n"
                "3. 在光线充足的环境下拍摄"
            )
        
        # 检查亮度
        if not quality_report['brightness']['passed']:
            value = quality_report['brightness']['value']
            min_val = quality_report['brightness']['min']
            max_val = quality_report['brightness']['max']
            if value < min_val:
                suggestions.append("图像太暗, 建议在光线充足的环境下拍摄或调整曝光")
            elif value > max_val:
                suggestions.append("图像太亮, 建议避免过度曝光")
        
        # 检查对比度
        if not quality_report['contrast']['passed']:
            suggestions.append(
                "图像对比度太低, 建议：\n"
                "1. 使用深色文字在浅色背景上\n"
                "2. 避免使用褪色或浅色的墨水\n"
                "3. 调整扫描仪或相机的对比度设置"
            )
        
        # 检查噪声
        if not quality_report['noise']['passed']:
            suggestions.append(
                "图像噪声较大, 建议：\n"
                "1. 使用较低的ISO设置\n"
                "2. 在光线充足的环境下拍摄\n"
                "3. 使用更好的相机或扫描仪\n"
                "4. 清洁相机镜头或扫描仪表面"
            )
        
        return suggestions 
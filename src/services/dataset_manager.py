"""
数据集管理器
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import os
import json
import random
from bson import ObjectId

from src.backend.database.database import Database
from src.backend.services.data_export.data_export_service import DataExportService

class DatasetManager:
    """数据集管理器类"""
    
    def __init__(self):
        """初始化数据集管理器"""
        self.db = Database.get_mongodb()
        self.export_service = DataExportService()
        
    async def create_dataset_version(
        self,
        project_id: str,
        version: str,
        description: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        创建数据集版本
        
        Args:
            project_id: 项目ID
            version: 版本号
            description: 版本描述
            metadata: 版本元数据
            
        Returns:
            str: 版本ID
        """
        try:
            # 检查版本是否已存在
            existing = await self.db["dataset_versions"].find_one({
                "project_id": ObjectId(project_id),
                "version": version
            })
            if existing:
                raise ValueError(f"版本 {version} 已存在")
            
            # 创建版本记录
            version_doc = {
                "project_id": ObjectId(project_id),
                "version": version,
                "description": description,
                "metadata": metadata or {},
                "created_at": datetime.utcnow(),
                "status": "pending"
            }
            
            result = await self.db["dataset_versions"].insert_one(version_doc)
            return str(result.inserted_id)
            
        except Exception as e:
            raise Exception(f"创建数据集版本失败: {str(e)}")
    
    async def split_dataset(
        self,
        project_id: str,
        version: str,
        train_ratio: float = 0.7,
        val_ratio: float = 0.2,
        test_ratio: float = 0.1,
        random_seed: Optional[int] = None
    ) -> Dict[str, List[str]]:
        """
        划分数据集
        
        Args:
            project_id: 项目ID
            version: 数据集版本
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            test_ratio: 测试集比例
            random_seed: 随机种子
            
        Returns:
            Dict[str, List[str]]: 各集合的图像ID列表
        """
        try:
            # 验证比例
            total_ratio = train_ratio + val_ratio + test_ratio
            if not (0.99 <= total_ratio <= 1.01):  # 允许小的浮点误差
                raise ValueError("数据集比例之和必须为1")
            
            # 获取所有标注
            annotations = await self.db["annotations"].find({
                "project_id": ObjectId(project_id)
            }).to_list(None)
            
            # 设置随机种子
            if random_seed is not None:
                random.seed(random_seed)
            
            # 随机打乱
            random.shuffle(annotations)
            
            # 计算各集合的大小
            total = len(annotations)
            train_size = int(total * train_ratio)
            val_size = int(total * val_ratio)
            
            # 划分数据集
            train_set = annotations[:train_size]
            val_set = annotations[train_size:train_size + val_size]
            test_set = annotations[train_size + val_size:]
            
            # 提取图像ID
            result = {
                "train": [str(ann["image_id"]) for ann in train_set],
                "val": [str(ann["image_id"]) for ann in val_set],
                "test": [str(ann["image_id"]) for ann in test_set]
            }
            
            # 更新版本信息
            await self.db["dataset_versions"].update_one(
                {
                    "project_id": ObjectId(project_id),
                    "version": version
                },
                {
                    "$set": {
                        "split_info": {
                            "train_ratio": train_ratio,
                            "val_ratio": val_ratio,
                            "test_ratio": test_ratio,
                            "random_seed": random_seed,
                            "train_size": len(train_set),
                            "val_size": len(val_set),
                            "test_size": len(test_set)
                        }
                    }
                }
            )
            
            return result
            
        except Exception as e:
            raise Exception(f"划分数据集失败: {str(e)}")
    
    async def export_dataset_splits(
        self,
        project_id: str,
        version: str,
        output_dir: str,
        batch_size: int = 100
    ) -> Dict[str, Tuple[str, List[str]]]:
        """
        导出划分后的数据集
        
        Args:
            project_id: 项目ID
            version: 数据集版本
            output_dir: 输出目录
            batch_size: 批处理大小
            
        Returns:
            Dict[str, Tuple[str, List[str]]]: 各集合的标签文件路径和警告信息
        """
        try:
            # 获取数据集划分信息
            version_info = await self.db["dataset_versions"].find_one({
                "project_id": ObjectId(project_id),
                "version": version
            })
            if not version_info or "split_info" not in version_info:
                raise ValueError("数据集尚未划分")
            
            # 创建输出目录
            version_dir = os.path.join(output_dir, f"version_{version}")
            os.makedirs(version_dir, exist_ok=True)
            
            # 导出各个集合
            result = {}
            for split in ["train", "val", "test"]:
                split_dir = os.path.join(version_dir, split)
                os.makedirs(split_dir, exist_ok=True)
                
                # 导出标注数据
                label_file, warnings = await self.export_service.export_annotations(
                    project_id,
                    split_dir,
                    batch_size
                )
                
                result[split] = (label_file, warnings)
            
            # 导出分类标签
            await self.export_service.export_multi_level_categories(
                project_id,
                version_dir
            )
            
            # 更新版本状态
            await self.db["dataset_versions"].update_one(
                {
                    "project_id": ObjectId(project_id),
                    "version": version
                },
                {
                    "$set": {
                        "status": "exported",
                        "export_path": version_dir,
                        "exported_at": datetime.utcnow()
                    }
                }
            )
            
            return result
            
        except Exception as e:
            raise Exception(f"导出数据集划分失败: {str(e)}")
    
    async def get_version_info(
        self,
        project_id: str,
        version: str
    ) -> Optional[Dict[str, Any]]:
        """
        获取版本信息
        
        Args:
            project_id: 项目ID
            version: 数据集版本
            
        Returns:
            Optional[Dict[str, Any]]: 版本信息
        """
        try:
            version_info = await self.db["dataset_versions"].find_one({
                "project_id": ObjectId(project_id),
                "version": version
            })
            
            if version_info:
                version_info["_id"] = str(version_info["_id"])
                version_info["project_id"] = str(version_info["project_id"])
            
            return version_info
            
        except Exception as e:
            raise Exception(f"获取版本信息失败: {str(e)}")
    
    async def list_versions(
        self,
        project_id: str
    ) -> List[Dict[str, Any]]:
        """
        列出项目的所有版本
        
        Args:
            project_id: 项目ID
            
        Returns:
            List[Dict[str, Any]]: 版本信息列表
        """
        try:
            versions = await self.db["dataset_versions"].find({
                "project_id": ObjectId(project_id)
            }).to_list(None)
            
            # 转换ID为字符串
            for version in versions:
                version["_id"] = str(version["_id"])
                version["project_id"] = str(version["project_id"])
            
            return versions
            
        except Exception as e:
            raise Exception(f"列出版本失败: {str(e)}")
    
    async def delete_version(
        self,
        project_id: str,
        version: str
    ) -> bool:
        """
        删除数据集版本
        
        Args:
            project_id: 项目ID
            version: 数据集版本
            
        Returns:
            bool: 是否删除成功
        """
        try:
            # 获取版本信息
            version_info = await self.get_version_info(project_id, version)
            if not version_info:
                return False
            
            # 删除导出的文件
            if "export_path" in version_info:
                export_path = version_info["export_path"]
                if os.path.exists(export_path):
                    for root, dirs, files in os.walk(export_path, topdown=False):
                        for name in files:
                            os.remove(os.path.join(root, name))
                        for name in dirs:
                            os.rmdir(os.path.join(root, name))
                    os.rmdir(export_path)
            
            # 删除版本记录
            result = await self.db["dataset_versions"].delete_one({
                "project_id": ObjectId(project_id),
                "version": version
            })
            
            return result.deleted_count > 0
            
        except Exception as e:
            raise Exception(f"删除版本失败: {str(e)}") 
"""
性能监控服务
提供系统性能监控和分析功能
"""

from typing import List, Dict, Any, Optional
import time
import psutil
import numpy as np
from datetime import datetime, timedelta
import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
from redis.asyncio import Redis
from prometheus_client import Counter, Histogram, Gauge

from src.backend.logger import get_logger
from src.backend.config import get_settings, Settings as Config

logger = get_logger(__name__)

class PerformanceMonitoringService:
    """性能监控服务"""
    
    def __init__(
        self,
        mongodb_url: str,
        redis_host: str,
        redis_port: int,
        collection_interval: int = 60,  # 默认60秒
        retention_days: int = 7,  # 默认保留7天
        thresholds: Optional[Dict[str, float]] = None
    ):
        """
        初始化性能监控服务
        
        Args:
            mongodb_url: MongoDB连接URL
            redis_host: Redis主机地址
            redis_port: Redis端口
            collection_interval: 数据收集间隔（秒）
            retention_days: 数据保留天数
            thresholds: 性能阈值配置
        """
        self.mongodb_url = mongodb_url
        self.redis_host = redis_host
        self.redis_port = redis_port
        self.collection_interval = collection_interval
        self.retention_days = retention_days
        self.thresholds = thresholds or {
            'cpu': 80.0,  # CPU使用率阈值（百分比）
            'memory': 80.0,  # 内存使用率阈值（百分比）
            'disk': 80.0  # 磁盘使用率阈值（百分比）
        }
        
        # 初始化数据库连接
        self.mongodb_client: Optional[AsyncIOMotorClient] = None
        self.redis_client: Optional[Redis] = None
        
        # 监控任务
        self.monitoring_task: Optional[asyncio.Task] = None
        self.is_running = False
        
        # Initialize Prometheus metrics
        self._setup_prometheus_metrics()
        
        # Initialize internal state
        self.last_collection_time = None
        self.collection_running = False
        
    def _setup_prometheus_metrics(self):
        """Initialize all Prometheus metrics"""
        # System metrics
        self.cpu_usage = Gauge('system_cpu_usage_percent', 'CPU usage percentage')
        self.memory_usage = Gauge('system_memory_usage_percent', 'Memory usage percentage')
        self.disk_usage = Gauge('system_disk_usage_percent', 'Disk usage percentage')
        
        # API metrics
        self.request_count = Counter('api_request_total', 'Total API requests', ['endpoint', 'method'])
        self.request_latency = Histogram(
            'api_request_latency_seconds',
            'API request latency in seconds',
            ['endpoint', 'method'],
            buckets=[0.1, 0.5, 1.0, 2.0, 5.0]
        )
        self.error_count = Counter('api_error_total', 'Total API errors', ['endpoint', 'method'])
        
        # Database metrics
        self.db_operation_count = Counter('db_operation_total', 'Total database operations', ['operation'])
        self.db_operation_latency = Histogram(
            'db_operation_latency_seconds',
            'Database operation latency in seconds',
            ['operation'],
            buckets=[0.01, 0.05, 0.1, 0.5, 1.0]
        )
        self.db_connection_gauge = Gauge('db_connections_active', 'Active database connections')
        
        # Cache metrics
        self.cache_hit_count = Counter('cache_hit_total', 'Total cache hits')
        self.cache_miss_count = Counter('cache_miss_total', 'Total cache misses')
        self.cache_memory_usage = Gauge('cache_memory_usage_bytes', 'Cache memory usage in bytes')
        
    async def start_collection(self):
        """Start the metrics collection process"""
        if self.collection_running:
            logger.warning("Metrics collection already running")
            return
            
        self.collection_running = True
        logger.info("Started performance metrics collection")
        
    async def stop_collection(self):
        """Stop the metrics collection process"""
        if not self.collection_running:
            logger.warning("Metrics collection not running")
            return
            
        self.collection_running = False
        logger.info("Stopped performance metrics collection")
        
    async def collect_system_metrics(self) -> Dict:
        """Collect system performance metrics"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_usage.set(cpu_percent)
            
            # Memory usage
            memory = psutil.virtual_memory()
            self.memory_usage.set(memory.percent)
            
            # Disk usage
            disk = psutil.disk_usage('/')
            self.disk_usage.set(disk.percent)
            
            metrics = {
                'timestamp': datetime.utcnow(),
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'disk_usage': disk.percent
            }
            
            # Check thresholds and log warnings
            if cpu_percent > self.thresholds['cpu']:
                logger.warning(f"CPU usage ({cpu_percent}%) exceeds threshold ({self.thresholds['cpu']}%)")
            
            if memory.percent > self.thresholds['memory']:
                logger.warning(f"Memory usage ({memory.percent}%) exceeds threshold ({self.thresholds['memory']}%)")
            
            if disk.percent > self.thresholds['disk']:
                logger.warning(f"Disk usage ({disk.percent}%) exceeds threshold ({self.thresholds['disk']}%)")
                
            return metrics
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {str(e)}")
            return None
            
    async def collect_database_metrics(self) -> Dict:
        """Collect database performance metrics"""
        try:
            # Get MongoDB statistics
            db_stats = await self.mongodb_client[self.config.mongodb.database].command('serverStatus')
            
            # Get connection pool statistics
            connections = db_stats['connections']
            self.db_connection_gauge.set(connections['current'])
            
            # Calculate connection usage percentage
            connection_usage = (connections['current'] / connections['available']) * 100
            
            metrics = {
                'timestamp': datetime.utcnow(),
                'active_connections': connections['current'],
                'available_connections': connections['available'],
                'connection_usage': connection_usage
            }
            
            # Check thresholds
            if connection_usage > self.config.monitoring.db_connection_threshold:
                logger.warning(f"Database connection usage ({connection_usage}%) exceeds threshold ({self.config.monitoring.db_connection_threshold}%)")
                
            return metrics
            
        except Exception as e:
            logger.error(f"Error collecting database metrics: {str(e)}")
            return None
            
    async def collect_cache_metrics(self) -> Dict:
        """Collect cache performance metrics"""
        try:
            # Get Redis statistics
            info = self.redis_client.info()
            
            # Calculate cache hit rate
            hits = int(info['keyspace_hits'])
            misses = int(info['keyspace_misses'])
            total_ops = hits + misses
            hit_rate = hits / total_ops if total_ops > 0 else 0
            
            # Update Prometheus metrics
            self.cache_hit_count._value.set(hits)
            self.cache_miss_count._value.set(misses)
            
            # Get memory usage
            used_memory = int(info['used_memory'])
            max_memory = int(info['maxmemory']) if info['maxmemory'] != 0 else used_memory
            memory_usage = (used_memory / max_memory) * 100
            
            self.cache_memory_usage.set(used_memory)
            
            metrics = {
                'timestamp': datetime.utcnow(),
                'hit_rate': hit_rate,
                'memory_usage': memory_usage,
                'used_memory': used_memory,
                'max_memory': max_memory
            }
            
            # Check thresholds
            if hit_rate < self.config.monitoring.cache_hit_rate_threshold:
                logger.warning(f"Cache hit rate ({hit_rate:.2f}) below threshold ({self.config.monitoring.cache_hit_rate_threshold})")
                
            if memory_usage > self.config.monitoring.cache_memory_threshold:
                logger.warning(f"Cache memory usage ({memory_usage}%) exceeds threshold ({self.config.monitoring.cache_memory_threshold}%)")
                
            return metrics
            
        except Exception as e:
            logger.error(f"Error collecting cache metrics: {str(e)}")
            return None
            
    async def store_metrics(self, metrics: Dict):
        """Store collected metrics in MongoDB"""
        try:
            collection = self.mongodb_client[self.config.mongodb.database]['performance_metrics']
            await collection.insert_one(metrics)
            
            # Apply data retention policy
            retention_date = datetime.utcnow() - timedelta(days=self.retention_days)
            await collection.delete_many({'timestamp': {'$lt': retention_date}})
            
        except Exception as e:
            logger.error(f"Error storing metrics: {str(e)}")
            
    async def get_historical_metrics(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        metric_types: Optional[List[str]] = None
    ) -> List[Dict]:
        """Retrieve historical metrics from MongoDB"""
        try:
            collection = self.mongodb_client[self.config.mongodb.database]['performance_metrics']
            
            # Build query
            query = {}
            if start_time:
                query['timestamp'] = {'$gte': start_time}
            if end_time:
                query['timestamp'] = query.get('timestamp', {})
                query['timestamp']['$lte'] = end_time
                
            # Build projection
            projection = {'_id': 0}
            if metric_types:
                for metric_type in metric_types:
                    projection[metric_type] = 1
                    
            cursor = collection.find(query, projection)
            metrics = await cursor.to_list(length=None)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error retrieving historical metrics: {str(e)}")
            return []
            
    async def analyze_performance(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> Dict:
        """Analyze performance metrics and generate insights"""
        try:
            metrics = await self.get_historical_metrics(start_time, end_time)
            
            if not metrics:
                return {'error': 'No metrics found for the specified time range'}
                
            # Calculate averages
            cpu_values = [m['cpu_usage'] for m in metrics if 'cpu_usage' in m]
            memory_values = [m['memory_usage'] for m in metrics if 'memory_usage' in m]
            disk_values = [m['disk_usage'] for m in metrics if 'disk_usage' in m]
            
            analysis = {
                'time_range': {
                    'start': start_time,
                    'end': end_time
                },
                'system_metrics': {
                    'cpu': {
                        'average': sum(cpu_values) / len(cpu_values) if cpu_values else 0,
                        'max': max(cpu_values) if cpu_values else 0,
                        'min': min(cpu_values) if cpu_values else 0
                    },
                    'memory': {
                        'average': sum(memory_values) / len(memory_values) if memory_values else 0,
                        'max': max(memory_values) if memory_values else 0,
                        'min': min(memory_values) if memory_values else 0
                    },
                    'disk': {
                        'average': sum(disk_values) / len(disk_values) if disk_values else 0,
                        'max': max(disk_values) if disk_values else 0,
                        'min': min(disk_values) if disk_values else 0
                    }
                }
            }
            
            # Add insights
            insights = []
            
            # CPU insights
            if analysis['system_metrics']['cpu']['max'] > self.thresholds['cpu']:
                insights.append(f"CPU usage peaked at {analysis['system_metrics']['cpu']['max']}%, exceeding threshold")
                
            # Memory insights
            if analysis['system_metrics']['memory']['max'] > self.thresholds['memory']:
                insights.append(f"Memory usage peaked at {analysis['system_metrics']['memory']['max']}%, exceeding threshold")
                
            # Disk insights
            if analysis['system_metrics']['disk']['max'] > self.thresholds['disk']:
                insights.append(f"Disk usage peaked at {analysis['system_metrics']['disk']['max']}%, exceeding threshold")
                
            analysis['insights'] = insights
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing performance metrics: {str(e)}")
            return {'error': str(e)}
            
    def record_api_request(self, endpoint: str, method: str, duration: float, error: bool = False):
        """Record API request metrics"""
        try:
            # Update request count
            self.request_count.labels(endpoint=endpoint, method=method).inc()
            
            # Record request latency
            self.request_latency.labels(endpoint=endpoint, method=method).observe(duration)
            
            # Record error if applicable
            if error:
                self.error_count.labels(endpoint=endpoint, method=method).inc()
                
            # Check for slow requests
            if duration * 1000 > self.config.monitoring.slow_request_threshold:
                logger.warning(f"Slow request detected: {endpoint} {method} took {duration*1000:.2f}ms")
                
        except Exception as e:
            logger.error(f"Error recording API metrics: {str(e)}")
            
    def record_db_operation(self, operation: str, duration: float):
        """Record database operation metrics"""
        try:
            # Update operation count
            self.db_operation_count.labels(operation=operation).inc()
            
            # Record operation latency
            self.db_operation_latency.labels(operation=operation).observe(duration)
            
            # Check for slow queries
            if duration * 1000 > self.config.monitoring.db_query_timeout:
                logger.warning(f"Slow database operation detected: {operation} took {duration*1000:.2f}ms")
                
        except Exception as e:
            logger.error(f"Error recording database metrics: {str(e)}") 
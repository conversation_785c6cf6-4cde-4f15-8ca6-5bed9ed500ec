"""
批处理服务消息队列消费者
处理批量文件处理队列中的消息
"""

import json
import asyncio
from typing import Dict, Any

from src.common.queue import QueueManager
from src.common.logger import get_logger
from src.common.metrics import QUEUE_SIZE
from src.services.batch_processing_service import BatchProcessingService

logger = get_logger("batch_processing_consumer")

class BatchProcessingConsumer:
    """批处理消息消费者类"""
    
    def __init__(self):
        """初始化消费者"""
        self.service = BatchProcessingService()
        self.running = False
        self.tasks = set()
    
    async def start(self):
        """启动消费者"""
        if self.running:
            return
            
        self.running = True
        logger.info("Starting batch processing consumer")
        
        try:
            # 连接到消息队列
            await QueueManager.connect()
            
            # 声明队列
            channel = await QueueManager.get_channel()
            await channel.queue_declare(queue="batch_processing")
            
            # 设置预取数量
            await channel.basic_qos(prefetch_count=10)
            
            # 开始消费消息
            await channel.basic_consume(
                queue="batch_processing",
                callback=self._process_message
            )
            
            # 更新队列大小指标
            asyncio.create_task(self._update_queue_size())
            
            logger.info("Batch processing consumer started")
            
        except Exception as e:
            logger.error(f"Failed to start batch processing consumer: {str(e)}")
            self.running = False
            raise
    
    async def stop(self):
        """停止消费者"""
        if not self.running:
            return
            
        self.running = False
        logger.info("Stopping batch processing consumer")
        
        try:
            # 等待所有任务完成
            if self.tasks:
                await asyncio.gather(*self.tasks, return_exceptions=True)
            
            # 关闭消息队列连接
            await QueueManager.close()
            
            logger.info("Batch processing consumer stopped")
            
        except Exception as e:
            logger.error(f"Error stopping batch processing consumer: {str(e)}")
            raise
    
    async def _process_message(
        self,
        message: Dict[str, Any]
    ) -> None:
        """
        处理消息
        
        Args:
            message: 消息内容
        """
        try:
            # 解析消息
            body = message.body.decode()
            data = json.loads(body)
            
            # 验证消息格式
            if not all(k in data for k in ["batch_id", "file_id", "action"]):
                logger.error(f"Invalid message format: {body}")
                await message.reject()
                return
            
            # 创建处理任务
            task = asyncio.create_task(
                self._handle_message(data)
            )
            self.tasks.add(task)
            task.add_done_callback(self.tasks.discard)
            
            # 确认消息
            await message.ack()
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode message: {str(e)}")
            await message.reject()
            
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            await message.reject()
    
    async def _handle_message(self, data: Dict[str, Any]) -> None:
        """
        处理消息内容
        
        Args:
            data: 消息数据
        """
        try:
            if data["action"] == "process":
                await self.service.process_file(
                    data["file_id"],
                    data["batch_id"]
                )
            else:
                logger.warning(f"Unknown action: {data['action']}")
                
        except Exception as e:
            logger.error(
                f"Failed to handle message for file {data['file_id']}: {str(e)}"
            )
    
    async def _update_queue_size(self) -> None:
        """更新队列大小指标"""
        while self.running:
            try:
                # 获取队列信息
                channel = await QueueManager.get_channel()
                queue = await channel.queue_declare(
                    queue="batch_processing",
                    passive=True
                )
                
                # 更新指标
                QUEUE_SIZE.labels(
                    queue="batch_processing"
                ).set(queue.message_count)
                
            except Exception as e:
                logger.error(f"Failed to update queue size metric: {str(e)}")
                
            finally:
                await asyncio.sleep(60)  # 每分钟更新一次

async def run_consumer():
    """运行消费者"""
    consumer = BatchProcessingConsumer()
    
    try:
        await consumer.start()
        
        # 保持运行直到收到停止信号
        while consumer.running:
            await asyncio.sleep(1)
            
    except asyncio.CancelledError:
        logger.info("Received stop signal")
        
    finally:
        await consumer.stop()

if __name__ == "__main__":
    asyncio.run(run_consumer()) 
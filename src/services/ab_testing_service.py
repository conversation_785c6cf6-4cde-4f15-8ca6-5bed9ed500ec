"""
A/B测试服务
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import random
import asyncio
from bson import ObjectId

from src.backend.database.database import Database
from src.services.onnx_model_manager import ONNXModelManager

class ABTestingService:
    """A/B测试服务类"""
    
    def __init__(self):
        """初始化A/B测试服务"""
        self.db = Database.get_mongodb()
        self.model_manager = ONNXModelManager()
        self.active_tests: Dict[str, Dict[str, Any]] = {}
        
    async def create_test(
        self,
        name: str,
        model_a: Dict[str, str],
        model_b: Dict[str, str],
        traffic_split: float = 0.5,
        description: Optional[str] = None,
        metrics: Optional[List[str]] = None
    ) -> str:
        """
        创建新的A/B测试
        
        Args:
            name: 测试名称
            model_a: 模型A的信息 {"name": "model_name", "version": "version"}
            model_b: 模型B的信息 {"name": "model_name", "version": "version"}
            traffic_split: 流量分配比例（0-1之间，表示分配给模型A的流量）
            description: 测试描述
            metrics: 需要跟踪的指标列表
            
        Returns:
            test_id: 测试ID
        """
        # 验证模型是否存在
        for model in [model_a, model_b]:
            if not await self.model_manager.model_exists(
                model_name=model["name"],
                version=model["version"]
            ):
                raise ValueError(f"模型不存在: {model['name']} v{model['version']}")
        
        # 创建测试记录
        test = {
            "name": name,
            "model_a": model_a,
            "model_b": model_b,
            "traffic_split": traffic_split,
            "description": description,
            "metrics": metrics or ["accuracy", "latency"],
            "status": "active",
            "created_at": datetime.utcnow(),
            "results": {
                "model_a": {"total_requests": 0, "metrics": {}},
                "model_b": {"total_requests": 0, "metrics": {}}
            }
        }
        
        result = await self.db["ab_tests"].insert_one(test)
        test_id = str(result.inserted_id)
        
        # 加载模型
        await self.model_manager.load_model(
            model_name=model_a["name"],
            version=model_a["version"]
        )
        await self.model_manager.load_model(
            model_name=model_b["name"],
            version=model_b["version"]
        )
        
        # 添加到活动测试列表
        self.active_tests[test_id] = test
        return test_id
        
    async def get_model_for_request(self, test_id: str) -> Tuple[str, str]:
        """
        为请求选择模型
        
        Args:
            test_id: 测试ID
            
        Returns:
            tuple: (model_name, version)
        """
        if test_id not in self.active_tests:
            test = await self.db["ab_tests"].find_one({"_id": ObjectId(test_id)})
            if not test:
                raise ValueError(f"测试不存在: {test_id}")
            self.active_tests[test_id] = test
            
        test = self.active_tests[test_id]
        if test["status"] != "active":
            raise ValueError(f"测试已停止: {test_id}")
            
        # 根据流量分配选择模型
        if random.random() < test["traffic_split"]:
            model = test["model_a"]
            await self._update_metrics(test_id, "model_a", "total_requests", 1)
        else:
            model = test["model_b"]
            await self._update_metrics(test_id, "model_b", "total_requests", 1)
            
        return model["name"], model["version"]
        
    async def record_metrics(
        self,
        test_id: str,
        model_name: str,
        version: str,
        metrics: Dict[str, float]
    ) -> None:
        """
        记录指标
        
        Args:
            test_id: 测试ID
            model_name: 模型名称
            version: 模型版本
            metrics: 指标数据
        """
        test = self.active_tests.get(test_id)
        if not test:
            raise ValueError(f"测试不存在: {test_id}")
            
        # 确定是哪个模型
        model_key = None
        if (
            test["model_a"]["name"] == model_name and
            test["model_a"]["version"] == version
        ):
            model_key = "model_a"
        elif (
            test["model_b"]["name"] == model_name and
            test["model_b"]["version"] == version
        ):
            model_key = "model_b"
            
        if not model_key:
            raise ValueError(f"模型不属于此测试: {model_name} v{version}")
            
        # 更新指标
        for metric_name, value in metrics.items():
            if metric_name in test["metrics"]:
                await self._update_metrics(test_id, model_key, metric_name, value)
                
    async def _update_metrics(
        self,
        test_id: str,
        model_key: str,
        metric_name: str,
        value: float
    ) -> None:
        """
        更新指标
        """
        update = {
            f"results.{model_key}.metrics.{metric_name}": value
        }
        await self.db["ab_tests"].update_one(
            {"_id": ObjectId(test_id)},
            {"$inc": update}
        )
        
    async def get_test_results(self, test_id: str) -> Dict[str, Any]:
        """
        获取测试结果
        
        Args:
            test_id: 测试ID
            
        Returns:
            dict: 测试结果
        """
        test = await self.db["ab_tests"].find_one({"_id": ObjectId(test_id)})
        if not test:
            raise ValueError(f"测试不存在: {test_id}")
            
        return {
            "name": test["name"],
            "status": test["status"],
            "model_a": {
                "info": test["model_a"],
                "results": test["results"]["model_a"]
            },
            "model_b": {
                "info": test["model_b"],
                "results": test["results"]["model_b"]
            },
            "traffic_split": test["traffic_split"],
            "created_at": test["created_at"]
        }
        
    async def stop_test(self, test_id: str) -> None:
        """
        停止测试
        
        Args:
            test_id: 测试ID
        """
        test = self.active_tests.get(test_id)
        if not test:
            raise ValueError(f"测试不存在: {test_id}")
            
        # 更新状态
        await self.db["ab_tests"].update_one(
            {"_id": ObjectId(test_id)},
            {"$set": {"status": "completed"}}
        )
        
        # 从活动测试列表中移除
        del self.active_tests[test_id]
        
        # 卸载不再需要的模型
        await self.model_manager.unload_model(
            model_name=test["model_a"]["name"],
            version=test["model_a"]["version"]
        )
        await self.model_manager.unload_model(
            model_name=test["model_b"]["name"],
            version=test["model_b"]["version"]
        ) 
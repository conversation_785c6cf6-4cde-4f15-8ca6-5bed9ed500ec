"""
文件管理服务模块
提供文件管理和状态跟踪功能
"""

from datetime import datetime
from typing import Dict, List, Optional, Union
from pathlib import Path
import logging
from pymongo import MongoClient, ASCENDING, DESCENDING
from bson import ObjectId

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FileStatus:
    """文件状态常量"""
    UPLOADING = "uploading"
    UPLOADED = "uploaded"
    VALIDATING = "validating"
    VALIDATED = "validated"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    DELETED = "deleted"

class FileManagementService:
    """文件管理服务类"""
    
    def __init__(self, mongodb_client: MongoClient):
        """初始化文件管理服务"""
        self.db = mongodb_client.agent_test
        self.files_collection = self.db.ocr_files
        self.logger = logging.getLogger(__name__)
        
    def list_files(self, 
                   status: Optional[str] = None,
                   page: int = 1,
                   limit: int = 10,
                   sort_by: str = "created_at",
                   sort_order: int = -1) -> Dict:
        """
        获取文件列表
        
        参数:
            status: 可选的状态过滤
            page: 页码（从1开始）
            limit: 每页数量
            sort_by: 排序字段
            sort_order: 排序方向（1升序，-1降序）
        """
        try:
            # 构建查询条件
            query = {}
            if status:
                query["status"] = status
                
            # 计算总数
            total = self.files_collection.count_documents(query)
            
            # 获取分页数据
            files = self.files_collection.find(query) \
                .sort(sort_by, sort_order) \
                .skip((page - 1) * limit) \
                .limit(limit)
                
            return {
                "success": True,
                "data": {
                    "files": list(files),
                    "pagination": {
                        "total": total,
                        "page": page,
                        "limit": limit,
                        "total_pages": (total + limit - 1) // limit
                    }
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取文件列表失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
            
    def get_file_details(self, file_id: str) -> Dict:
        """获取文件详细信息"""
        try:
            file = self.files_collection.find_one({"_id": ObjectId(file_id)})
            if not file:
                return {
                    "success": False,
                    "error": "文件不存在"
                }
                
            return {
                "success": True,
                "data": file
            }
            
        except Exception as e:
            self.logger.error(f"获取文件详情失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
            
    def update_file_status(self, 
                          file_id: str,
                          status: str,
                          message: str = "",
                          metadata: Optional[Dict] = None) -> Dict:
        """
        更新文件状态
        
        参数:
            file_id: 文件ID
            status: 新状态
            message: 状态更新说明
            metadata: 可选的元数据更新
        """
        try:
            update_data = {
                "status": status,
                "updated_at": datetime.utcnow()
            }
            
            # 添加状态历史记录
            history_entry = {
                "status": status,
                "message": message,
                "timestamp": datetime.utcnow()
            }
            
            # 如果有元数据更新，添加到更新数据中
            if metadata:
                update_data["metadata"] = metadata
                
            result = self.files_collection.update_one(
                {"_id": ObjectId(file_id)},
                {
                    "$set": update_data,
                    "$push": {
                        "status_history": history_entry
                    }
                }
            )
            
            if result.modified_count == 0:
                return {
                    "success": False,
                    "error": "文件不存在或状态未更新"
                }
                
            return {
                "success": True,
                "data": {
                    "file_id": file_id,
                    "status": status
                }
            }
            
        except Exception as e:
            self.logger.error(f"更新文件状态失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
            
    def delete_file(self, file_id: str) -> Dict:
        """删除文件（软删除）"""
        try:
            result = self.update_file_status(
                file_id=file_id,
                status=FileStatus.DELETED,
                message="文件已删除"
            )
            
            if not result["success"]:
                return result
                
            return {
                "success": True,
                "data": {
                    "file_id": file_id,
                    "status": FileStatus.DELETED
                }
            }
            
        except Exception as e:
            self.logger.error(f"删除文件失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
            
    def get_file_statistics(self) -> Dict:
        """获取文件统计信息"""
        try:
            # 按状态统计文件数量
            pipeline = [
                {
                    "$group": {
                        "_id": "$status",
                        "count": {"$sum": 1}
                    }
                }
            ]
            
            status_stats = list(self.files_collection.aggregate(pipeline))
            
            # 获取总文件数和存储大小
            total_files = self.files_collection.count_documents({})
            total_size = self.files_collection.aggregate([
                {
                    "$group": {
                        "_id": None,
                        "total_size": {"$sum": "$file_size"}
                    }
                }
            ])
            total_size = list(total_size)[0]["total_size"] if list(total_size) else 0
            
            # 获取最近的文件
            recent_files = list(self.files_collection.find()
                              .sort("created_at", -1)
                              .limit(5))
            
            return {
                "success": True,
                "data": {
                    "total_files": total_files,
                    "total_size": total_size,
                    "status_breakdown": {
                        stat["_id"]: stat["count"]
                        for stat in status_stats
                    },
                    "recent_files": recent_files
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取文件统计失败: {e}")
            return {
                "success": False,
                "error": str(e)
            } 
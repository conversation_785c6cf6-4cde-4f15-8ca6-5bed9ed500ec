"""
文件处理服务

处理上传文件的后台任务，包括文件验证、转换和元数据提取。
"""

import os
from typing import Dict, Any, Optional
from pathlib import Path
import asyncio
import magic
import hashlib
from datetime import datetime
from pymongo import MongoClient
import logging
from PIL import Image
import fitz  # PyMuPDF
import pyheif
from bson import ObjectId

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FileProcessor:
    def __init__(self, db_client: MongoClient):
        """
        初始化文件处理器
        
        Args:
            db_client: MongoDB客户端实例
        """
        self.db = db_client.agent_test
        self.files_collection = self.db.files
        self.preview_dir = Path("uploads/previews")
        self.thumbnail_dir = Path("uploads/thumbnails")
        self.preview_dir.mkdir(parents=True, exist_ok=True)
        self.thumbnail_dir.mkdir(parents=True, exist_ok=True)
        
    async def process_file(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理上传的文件
        
        Args:
            file_info: 文件信息字典
            
        Returns:
            更新后的文件信息
        """
        file_id = file_info["_id"]
        try:
            # 更新文件状态为处理中
            await self._update_status(file_id, "processing")
            
            # 根据文件类型进行处理
            mime_type = file_info["mime_type"]
            file_path = Path(file_info["path"])
            
            metadata = {}
            
            if mime_type.startswith("image/"):
                metadata = await self._process_image(file_path, mime_type)
            elif mime_type == "application/pdf":
                metadata = await self._process_pdf(file_path)
            
            # 更新文件元数据
            await self._update_metadata(file_id, metadata)
            
            # 更新文件状态为已完成
            await self._update_status(file_id, "completed")
            
            return await self._get_file_info(file_id)
            
        except Exception as e:
            logger.error(f"处理文件失败: {str(e)}", exc_info=True)
            await self._update_status(file_id, "error", str(e))
            raise
    
    async def _process_image(self, file_path: Path, mime_type: str) -> Dict[str, Any]:
        """
        处理图片文件
        """
        metadata = {}
        
        try:
            if mime_type in ["image/heic", "image/heif"]:
                # 处理HEIC/HEIF格式
                heif_file = pyheif.read(str(file_path))
                image = Image.frombytes(
                    heif_file.mode, 
                    heif_file.size, 
                    heif_file.data,
                    "raw",
                    heif_file.mode,
                    heif_file.stride,
                )
            else:
                # 处理其他图片格式
                image = Image.open(file_path)
            
            # 提取图片信息
            metadata.update({
                "dimensions": {
                    "width": image.width,
                    "height": image.height
                },
                "mode": image.mode,
                "format": image.format
            })
            
            # 生成缩略图
            thumbnail_path = self.thumbnail_dir / f"{file_path.stem}_thumb.jpg"
            await self._generate_thumbnail(image, thumbnail_path)
            metadata["thumbnail_path"] = str(thumbnail_path)
            
            # 如果是HEIC/HEIF，生成JPEG预览
            if mime_type in ["image/heic", "image/heif"]:
                preview_path = self.preview_dir / f"{file_path.stem}.jpg"
                await self._generate_preview(image, preview_path)
                metadata["preview_path"] = str(preview_path)
            
        except Exception as e:
            logger.error(f"处理图片失败: {str(e)}", exc_info=True)
            raise
        
        return metadata
    
    async def _process_pdf(self, file_path: Path) -> Dict[str, Any]:
        """
        处理PDF文件
        """
        metadata = {}
        
        try:
            # 打开PDF文件
            pdf_document = fitz.open(str(file_path))
            
            # 提取PDF信息
            metadata.update({
                "page_count": len(pdf_document),
                "metadata": pdf_document.metadata
            })
            
            # 生成首页预览
            if len(pdf_document) > 0:
                preview_path = self.preview_dir / f"{file_path.stem}_preview.jpg"
                await self._generate_pdf_preview(pdf_document[0], preview_path)
                metadata["preview_path"] = str(preview_path)
            
            pdf_document.close()
            
        except Exception as e:
            logger.error(f"处理PDF失败: {str(e)}", exc_info=True)
            raise
        
        return metadata
    
    async def _generate_thumbnail(self, image: Image.Image, output_path: Path, size: tuple = (200, 200)) -> None:
        """
        生成缩略图
        """
        # 创建缩略图
        thumb = image.copy()
        thumb.thumbnail(size)
        
        # 保存缩略图
        thumb.save(str(output_path), "JPEG", quality=85)
    
    async def _generate_preview(self, image: Image.Image, output_path: Path, max_size: tuple = (1024, 1024)) -> None:
        """
        生成预览图
        """
        # 创建预览图
        preview = image.copy()
        preview.thumbnail(max_size)
        
        # 保存预览图
        preview.save(str(output_path), "JPEG", quality=85)
    
    async def _generate_pdf_preview(self, page: fitz.Page, output_path: Path) -> None:
        """
        生成PDF预览图
        """
        # 设置渲染参数
        zoom = 2  # 增加分辨率
        mat = fitz.Matrix(zoom, zoom)
        
        # 渲染页面
        pix = page.get_pixmap(matrix=mat)
        
        # 保存为图片
        pix.save(str(output_path))
    
    async def _update_status(self, file_id: ObjectId, status: str, error_message: Optional[str] = None) -> None:
        """
        更新文件状态
        """
        update = {
            "status": status,
            "updated_at": datetime.utcnow()
        }
        
        if error_message:
            update["error_message"] = error_message
        
        self.files_collection.update_one(
            {"_id": file_id},
            {"$set": update}
        )
    
    async def _update_metadata(self, file_id: ObjectId, metadata: Dict[str, Any]) -> None:
        """
        更新文件元数据
        """
        self.files_collection.update_one(
            {"_id": file_id},
            {
                "$set": {
                    "metadata": metadata,
                    "updated_at": datetime.utcnow()
                }
            }
        )
    
    async def _get_file_info(self, file_id: ObjectId) -> Dict[str, Any]:
        """
        获取文件信息
        """
        file_info = self.files_collection.find_one({"_id": file_id})
        if not file_info:
            raise ValueError(f"文件不存在: {file_id}")
        return file_info 
"""
模型更新服务
用于管理模型的更新、部署和回滚
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
import os
import json
import shutil
from pathlib import Path
import subprocess
import tempfile

from src.common.logger import get_logger
from src.common.config import get_config
from src.services.onnx_model_manager import ONNXModelManager
from src.services.error_analysis_service import ErrorAnalysisService

logger = get_logger(__name__)

class ModelUpdateService:
    """模型更新服务类"""
    
    def __init__(self):
        """初始化更新服务"""
        self.config = get_config()
        self.model_manager = ONNXModelManager()
        self.error_analysis = ErrorAnalysisService()
        
        # 确保工作目录存在
        self.work_dir = Path(self.config.get("model_update", {}).get(
            "work_dir", "/tmp/model_updates"
        ))
        self.work_dir.mkdir(parents=True, exist_ok=True)
        
        # 模型性能监控阈值
        self.performance_thresholds = {
            "accuracy_drop": 0.05,  # 准确率下降阈值
            "error_rate_increase": 0.1,  # 错误率增加阈值
            "latency_increase": 0.2  # 延迟增加阈值
        }
    
    async def update_model(self, model_path: Path) -> bool:
        """
        更新模型
        
        Args:
            model_path: 新模型路径
            
        Returns:
            bool: 更新是否成功
        """
        try:
            # 验证新模型
            if not await self._validate_model(model_path):
                logger.error("Model validation failed")
                return False
            
            # 备份当前模型
            backup_path = await self._backup_current_model()
            
            # 部署新模型
            if not await self._deploy_model(model_path):
                logger.error("Model deployment failed")
                # 恢复备份
                await self._restore_model(backup_path)
                return False
            
            # 监控性能
            if not await self._monitor_performance(backup_path):
                logger.error("Performance check failed, rolling back")
                # 回滚到备份
                await self._restore_model(backup_path)
                return False
            
            # 清理备份
            if backup_path.exists():
                backup_path.unlink()
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating model: {str(e)}")
            return False
    
    async def _validate_model(self, model_path: Path) -> bool:
        """
        验证模型文件
        
        Args:
            model_path: 模型路径
            
        Returns:
            bool: 模型是否有效
        """
        try:
            # 检查文件存在性
            if not model_path.is_file():
                logger.error(f"Model file not found: {model_path}")
                return False
            
            # 检查文件格式
            if model_path.suffix != ".onnx":
                logger.error(f"Invalid model format: {model_path.suffix}")
                return False
            
            # 验证ONNX模型
            process = subprocess.Popen(
                ["python", "-m", "onnxruntime.tools.check_model", str(model_path)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            stdout, stderr = process.communicate()
            
            if process.returncode != 0:
                logger.error(f"ONNX model validation failed: {stderr.decode()}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating model: {str(e)}")
            return False
    
    async def _backup_current_model(self) -> Path:
        """
        备份当前模型
        
        Returns:
            Path: 备份文件路径
        """
        try:
            current_model = await self.model_manager.get_current_model()
            if not current_model:
                logger.warning("No current model found to backup")
                return None
            
            backup_path = self.work_dir / f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.onnx"
            shutil.copy2(current_model, backup_path)
            
            logger.info(f"Current model backed up to: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"Error backing up current model: {str(e)}")
            raise
    
    async def _deploy_model(self, model_path: Path) -> bool:
        """
        部署新模型
        
        Args:
            model_path: 新模型路径
            
        Returns:
            bool: 部署是否成功
        """
        try:
            # 更新模型管理器
            await self.model_manager.update_model(model_path)
            
            # 验证部署
            current_model = await self.model_manager.get_current_model()
            if not current_model or not current_model.samefile(model_path):
                logger.error("Model deployment verification failed")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error deploying model: {str(e)}")
            return False
    
    async def _monitor_performance(self, backup_path: Path) -> bool:
        """
        监控新模型性能
        
        Args:
            backup_path: 备份模型路径，用于性能比较
            
        Returns:
            bool: 性能是否达标
        """
        try:
            # 获取基准性能指标
            baseline_metrics = await self._get_model_metrics(backup_path)
            if not baseline_metrics:
                logger.error("Failed to get baseline metrics")
                return False
            
            # 获取新模型性能指标
            new_metrics = await self._get_model_metrics(
                await self.model_manager.get_current_model()
            )
            if not new_metrics:
                logger.error("Failed to get new model metrics")
                return False
            
            # 比较性能
            if new_metrics["accuracy"] < baseline_metrics["accuracy"] - self.performance_thresholds["accuracy_drop"]:
                logger.error("Accuracy dropped significantly")
                return False
            
            if new_metrics["error_rate"] > baseline_metrics["error_rate"] + self.performance_thresholds["error_rate_increase"]:
                logger.error("Error rate increased significantly")
                return False
            
            if new_metrics["latency"] > baseline_metrics["latency"] * (1 + self.performance_thresholds["latency_increase"]):
                logger.error("Latency increased significantly")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error monitoring performance: {str(e)}")
            return False
    
    async def _get_model_metrics(self, model_path: Path) -> Dict[str, float]:
        """
        获取模型性能指标
        
        Args:
            model_path: 模型路径
            
        Returns:
            Dict[str, float]: 性能指标
        """
        try:
            # 获取错误分析报告
            report = await self.error_analysis.analyze_errors(
                start_date=datetime.now() - timedelta(days=1)
            )
            
            if not report:
                return None
            
            # 计算指标
            metrics = {
                "accuracy": report["accuracy"],
                "error_rate": report["error_statistics"]["error_rate"],
                "latency": report["performance"]["average_latency"]
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting model metrics: {str(e)}")
            return None
    
    async def _restore_model(self, backup_path: Path) -> bool:
        """
        恢复到备份模型
        
        Args:
            backup_path: 备份模型路径
            
        Returns:
            bool: 恢复是否成功
        """
        try:
            if not backup_path or not backup_path.is_file():
                logger.error("Invalid backup path")
                return False
            
            # 部署备份模型
            if not await self._deploy_model(backup_path):
                logger.error("Failed to restore backup model")
                return False
            
            logger.info("Successfully restored to backup model")
            return True
            
        except Exception as e:
            logger.error(f"Error restoring model: {str(e)}")
            return False 
"""
批处理服务
提供文件批量处理和管理功能
"""

from typing import Dict, List, Optional, Union, Any
from datetime import datetime
import asyncio
from bson import ObjectId

from src.backend.database.database import Database
from src.common.queue import QueueManager
from src.common.logger import get_logger
from src.common.metrics import FILE_PROCESS_COUNT, QUEUE_SIZE
from src.services.file_management_service import FileManagementService
from src.services.file_quality_checker import FileQualityChecker
from src.ocr.processor import OCRProcessor

logger = get_logger("batch_processing")

class BatchProcessingService:
    """批处理服务类"""
    
    def __init__(self):
        """初始化批处理服务"""
        self.db = Database.get_mongodb()
        self.queue = QueueManager()
        self.quality_checker = FileQualityChecker()
        self.file_service = FileManagementService()
    
    async def create_batch_task(
        self,
        files: List[Dict[str, Any]],
        batch_name: str,
        description: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        创建批处理任务
        
        Args:
            files: 文件列表
            batch_name: 批处理任务名称
            description: 任务描述
            
        Returns:
            创建的任务信息
        """
        task = {
            "batch_name": batch_name,
            "description": description,
            "status": "pending",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "files": [
                {
                    **file,
                    "status": "pending",
                    "error": None,
                    "processed_at": None
                }
                for file in files
            ],
            "total_files": len(files),
            "completed_files": 0,
            "failed_files": 0
        }
        
        result = await self.db["batch_tasks"].insert_one(task)
        task["_id"] = result.inserted_id
        return task
    
    async def process_batch_task(self, task_id: str) -> None:
        """
        处理批处理任务
        
        Args:
            task_id: 任务ID
        """
        task = await self.get_batch_task(task_id)
        if not task:
            raise ValueError(f"任务不存在: {task_id}")
            
        # 更新任务状态
        await self.update_task_status(task_id, "processing")
        
        try:
            # 并发处理文件
            await asyncio.gather(*[
                self._process_file(task_id, file)
                for file in task["files"]
                if file["status"] == "pending"
            ])
            
            # 检查是否所有文件都处理完成
            updated_task = await self.get_batch_task(task_id)
            if all(f["status"] in ["completed", "failed"] for f in updated_task["files"]):
                final_status = "completed" if not updated_task["failed_files"] else "partially_completed"
                await self.update_task_status(task_id, final_status)
                
        except Exception as e:
            await self.update_task_status(task_id, "failed", str(e))
            raise
    
    async def _process_file(self, task_id: str, file: Dict[str, Any]) -> None:
        """
        处理单个文件
        
        Args:
            task_id: 任务ID
            file: 文件信息
        """
        try:
            # 检查文件质量
            if not await self.quality_checker.check_quality(file["file_id"]):
                raise ValueError("文件质量不符合要求")
                
            # 处理文件
            await self.file_service.process_file(file["file_id"])
            
            # 更新文件状态
            await self.update_file_status(task_id, file["file_id"], "completed")
            
        except Exception as e:
            await self.update_file_status(task_id, file["file_id"], "failed", str(e))
    
    async def cancel_batch_task(self, task_id: str) -> None:
        """
        取消批处理任务
        
        Args:
            task_id: 任务ID
        """
        await self.update_task_status(task_id, "cancelled")
    
    async def retry_failed_files(self, task_id: str) -> None:
        """
        重试失败的文件
        
        Args:
            task_id: 任务ID
        """
        task = await self.get_batch_task(task_id)
        if not task:
            raise ValueError(f"任务不存在: {task_id}")
            
        failed_files = [
            file for file in task["files"]
            if file["status"] == "failed"
        ]
        
        if not failed_files:
            return
            
        # 重置失败文件状态
        await self.db["batch_tasks"].update_one(
            {"_id": ObjectId(task_id)},
            {
                "$set": {
                    "status": "processing",
                    "files.$[elem].status": "pending",
                    "files.$[elem].error": None,
                    "files.$[elem].processed_at": None
                }
            },
            array_filters=[{"elem.status": "failed"}]
        )
        
        # 重新处理失败的文件
        await self.process_batch_task(task_id)
    
    async def get_batch_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取批处理任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务信息
        """
        return await self.db["batch_tasks"].find_one({"_id": ObjectId(task_id)})
    
    async def get_batch_task_progress(self, task_id: str) -> float:
        """
        获取批处理任务进度
        
        Args:
            task_id: 任务ID
            
        Returns:
            进度百分比（0-1）
        """
        task = await self.get_batch_task(task_id)
        if not task:
            raise ValueError(f"任务不存在: {task_id}")
            
        completed = len([
            f for f in task["files"]
            if f["status"] == "completed"
        ])
        
        return completed / task["total_files"]
    
    async def update_task_status(
        self,
        task_id: str,
        status: str,
        error: Optional[str] = None
    ) -> None:
        """
        更新任务状态
        
        Args:
            task_id: 任务ID
            status: 新状态
            error: 错误信息
        """
        update = {
            "status": status,
            "updated_at": datetime.utcnow()
        }
        if error:
            update["error"] = error
            
        await self.db["batch_tasks"].update_one(
            {"_id": ObjectId(task_id)},
            {"$set": update}
        )
    
    async def update_file_status(
        self,
        task_id: str,
        file_id: str,
        status: str,
        error: Optional[str] = None
    ) -> None:
        """
        更新文件状态
        
        Args:
            task_id: 任务ID
            file_id: 文件ID
            status: 新状态
            error: 错误信息
        """
        update = {
            "files.$[elem].status": status,
            "files.$[elem].processed_at": datetime.utcnow()
        }
        if error:
            update["files.$[elem].error"] = error
            
        # 更新文件状态
        await self.db["batch_tasks"].update_one(
            {"_id": ObjectId(task_id)},
            {
                "$set": update,
                "$inc": {
                    "completed_files": 1 if status == "completed" else 0,
                    "failed_files": 1 if status == "failed" else 0
                }
            },
            array_filters=[{"elem.file_id": file_id}]
        )
    
    async def create_batch(
        self,
        files: List[Dict[str, Any]],
        batch_name: str,
        description: Optional[str] = None,
        tags: Optional[List[str]] = None
    ) -> str:
        """
        创建批处理任务
        
        Args:
            files: 文件列表，每个文件包含文件ID和其他元数据
            batch_name: 批处理任务名称
            description: 批处理任务描述
            tags: 标签列表
            
        Returns:
            str: 批处理任务ID
        """
        try:
            # 创建批处理任务记录
            batch_id = ObjectId()
            batch_doc = {
                "_id": batch_id,
                "name": batch_name,
                "description": description,
                "tags": tags or [],
                "status": "created",
                "total_files": len(files),
                "processed_files": 0,
                "failed_files": 0,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "files": files
            }
            
            # 保存批处理任务
            await self.file_service.db.batch_tasks.insert_one(batch_doc)
            
            # 将文件加入处理队列
            for file in files:
                await QueueManager.publish_message(
                    "batch_processing",
                    {
                        "batch_id": str(batch_id),
                        "file_id": file["file_id"],
                        "action": "process"
                    }
                )
            
            logger.info(f"Created batch processing task: {batch_id}")
            return str(batch_id)
            
        except Exception as e:
            logger.error(f"Failed to create batch processing task: {str(e)}")
            raise
    
    async def process_file(self, file_id: str, batch_id: str) -> bool:
        """
        处理单个文件
        
        Args:
            file_id: 文件ID
            batch_id: 批处理任务ID
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 更新文件状态为处理中
            await self.file_service.update_file_status(file_id, "processing")
            
            # 获取文件信息
            file_info = await self.file_service.get_file_info(file_id)
            if not file_info:
                raise ValueError(f"File not found: {file_id}")
            
            # 质量检查
            quality_result = await self.quality_checker.check_file(file_info["path"])
            if not quality_result["is_qualified"]:
                await self.file_service.update_file_status(
                    file_id,
                    "failed",
                    f"Quality check failed: {quality_result['reason']}"
                )
                await self._update_batch_status(batch_id, failed=True)
                return False
            
            # OCR处理
            ocr_result = await self.ocr_processor.process_file(file_info["path"])
            if not ocr_result["success"]:
                await self.file_service.update_file_status(
                    file_id,
                    "failed",
                    f"OCR processing failed: {ocr_result['error']}"
                )
                await self._update_batch_status(batch_id, failed=True)
                return False
            
            # 更新文件状态和OCR结果
            await self.file_service.update_file(
                file_id,
                {
                    "status": "completed",
                    "ocr_result": ocr_result["data"],
                    "processed_at": datetime.utcnow()
                }
            )
            
            # 更新批处理任务状态
            await self._update_batch_status(batch_id)
            
            # 更新指标
            FILE_PROCESS_COUNT.labels(
                service="batch_processing",
                status="success",
                file_type=file_info["file_type"]
            ).inc()
            
            logger.info(f"Successfully processed file {file_id} in batch {batch_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to process file {file_id}: {str(e)}")
            await self.file_service.update_file_status(
                file_id,
                "failed",
                f"Processing error: {str(e)}"
            )
            await self._update_batch_status(batch_id, failed=True)
            return False
    
    async def _update_batch_status(self, batch_id: str, failed: bool = False) -> None:
        """
        更新批处理任务状态
        
        Args:
            batch_id: 批处理任务ID
            failed: 是否处理失败
        """
        try:
            # 获取批处理任务信息
            batch = await self.file_service.db.batch_tasks.find_one(
                {"_id": ObjectId(batch_id)}
            )
            if not batch:
                raise ValueError(f"Batch task not found: {batch_id}")
            
            # 更新处理状态
            update = {
                "$inc": {
                    "processed_files": 1,
                    "failed_files": 1 if failed else 0
                },
                "$set": {
                    "updated_at": datetime.utcnow()
                }
            }
            
            # 如果所有文件都已处理，更新任务状态
            if batch["processed_files"] + 1 >= batch["total_files"]:
                if batch["failed_files"] + (1 if failed else 0) > 0:
                    update["$set"]["status"] = "completed_with_errors"
                else:
                    update["$set"]["status"] = "completed"
            
            await self.file_service.db.batch_tasks.update_one(
                {"_id": ObjectId(batch_id)},
                update
            )
            
        except Exception as e:
            logger.error(f"Failed to update batch status for {batch_id}: {str(e)}")
            raise
    
    async def get_batch_info(self, batch_id: str) -> Optional[Dict[str, Any]]:
        """
        获取批处理任务信息
        
        Args:
            batch_id: 批处理任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 批处理任务信息
        """
        try:
            batch = await self.file_service.db.batch_tasks.find_one(
                {"_id": ObjectId(batch_id)}
            )
            if batch:
                batch["_id"] = str(batch["_id"])
            return batch
        except Exception as e:
            logger.error(f"Failed to get batch info for {batch_id}: {str(e)}")
            raise
    
    async def list_batches(
        self,
        status: Optional[str] = None,
        tags: Optional[List[str]] = None,
        skip: int = 0,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        获取批处理任务列表
        
        Args:
            status: 状态过滤
            tags: 标签过滤
            skip: 跳过数量
            limit: 返回数量
            
        Returns:
            List[Dict[str, Any]]: 批处理任务列表
        """
        try:
            # 构建查询条件
            query = {}
            if status:
                query["status"] = status
            if tags:
                query["tags"] = {"$all": tags}
            
            # 执行查询
            cursor = self.file_service.db.batch_tasks.find(query)
            cursor = cursor.sort("created_at", -1).skip(skip).limit(limit)
            
            # 格式化结果
            batches = []
            async for batch in cursor:
                batch["_id"] = str(batch["_id"])
                batches.append(batch)
            
            return batches
            
        except Exception as e:
            logger.error(f"Failed to list batch tasks: {str(e)}")
            raise
    
    async def cancel_batch(self, batch_id: str) -> bool:
        """
        取消批处理任务
        
        Args:
            batch_id: 批处理任务ID
            
        Returns:
            bool: 是否成功取消
        """
        try:
            # 更新批处理任务状态
            result = await self.file_service.db.batch_tasks.update_one(
                {"_id": ObjectId(batch_id), "status": {"$nin": ["completed", "cancelled"]}},
                {
                    "$set": {
                        "status": "cancelled",
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            
            if result.modified_count > 0:
                # 更新所有未处理文件的状态
                await self.file_service.db.files.update_many(
                    {
                        "batch_id": batch_id,
                        "status": {"$in": ["pending", "processing"]}
                    },
                    {
                        "$set": {
                            "status": "cancelled",
                            "updated_at": datetime.utcnow()
                        }
                    }
                )
                logger.info(f"Successfully cancelled batch task {batch_id}")
                return True
            else:
                logger.warning(f"Failed to cancel batch task {batch_id}: Invalid status")
                return False
                
        except Exception as e:
            logger.error(f"Failed to cancel batch task {batch_id}: {str(e)}")
            raise 
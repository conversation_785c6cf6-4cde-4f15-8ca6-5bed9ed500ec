"""
数据验证器
"""

from typing import Dict, List, Optional, Any, Tuple
import json
import cv2
import numpy as np
from bson import ObjectId

from src.backend.database.database import Database
from src.backend.minio_client import MinioClient

class DataValidator:
    """数据验证器类"""
    
    def __init__(self):
        """初始化数据验证器"""
        self.db = Database.get_mongodb()
        self.minio = MinioClient()
        
        # 验证规则
        self.rules = {
            "image": {
                "min_width": 100,
                "min_height": 100,
                "max_width": 4096,
                "max_height": 4096,
                "allowed_formats": ["jpg", "jpeg", "png", "bmp"]
            },
            "annotation": {
                "min_boxes": 1,
                "max_boxes": 100,
                "min_points": 4,
                "max_points": 100,
                "min_text_length": 1,
                "max_text_length": 1000
            }
        }
    
    async def validate_annotation_format(
        self,
        annotation: Dict[str, Any]
    ) -> <PERSON><PERSON>[bool, Optional[str]]:
        """
        验证标注格式
        
        Args:
            annotation: 标注数据
            
        Returns:
            Tuple[bool, Optional[str]]: (是否有效, 错误信息)
        """
        try:
            # 验证必需字段
            required_fields = ["image_id", "boxes", "project_id"]
            for field in required_fields:
                if field not in annotation:
                    return False, f"缺少必需字段: {field}"
            
            # 验证boxes格式
            boxes = annotation["boxes"]
            if not isinstance(boxes, list):
                return False, "boxes必须是列表"
            
            if not (self.rules["annotation"]["min_boxes"] <= len(boxes) <= 
                   self.rules["annotation"]["max_boxes"]):
                return False, f"boxes数量必须在{self.rules['annotation']['min_boxes']}到{self.rules['annotation']['max_boxes']}之间"
            
            # 验证每个box
            for box in boxes:
                if not isinstance(box, dict):
                    return False, "每个box必须是字典"
                
                # 验证必需字段
                box_fields = ["points", "text", "category"]
                for field in box_fields:
                    if field not in box:
                        return False, f"box缺少必需字段: {field}"
                
                # 验证points
                points = box["points"]
                if not isinstance(points, list):
                    return False, "points必须是列表"
                
                if not (self.rules["annotation"]["min_points"] <= len(points) <= 
                       self.rules["annotation"]["max_points"]):
                    return False, f"points数量必须在{self.rules['annotation']['min_points']}到{self.rules['annotation']['max_points']}之间"
                
                for point in points:
                    if not isinstance(point, dict) or "x" not in point or "y" not in point:
                        return False, "每个point必须是包含x和y坐标的字典"
                    
                    if not (isinstance(point["x"], (int, float)) and 
                           isinstance(point["y"], (int, float))):
                        return False, "坐标值必须是数字"
                
                # 验证text
                text = box["text"]
                if not isinstance(text, str):
                    return False, "text必须是字符串"
                
                if not (self.rules["annotation"]["min_text_length"] <= len(text) <= 
                       self.rules["annotation"]["max_text_length"]):
                    return False, f"text长度必须在{self.rules['annotation']['min_text_length']}到{self.rules['annotation']['max_text_length']}之间"
                
                # 验证category
                if not isinstance(box["category"], str):
                    return False, "category必须是字符串"
            
            return True, None
            
        except Exception as e:
            return False, str(e)
    
    async def validate_image_data(
        self,
        image_id: str
    ) -> Tuple[bool, Optional[str]]:
        """
        验证图像数据
        
        Args:
            image_id: 图像ID
            
        Returns:
            Tuple[bool, Optional[str]]: (是否有效, 错误信息)
        """
        try:
            # 获取图像信息
            image_info = await self.db["images"].find_one({
                "_id": ObjectId(image_id)
            })
            if not image_info:
                return False, f"找不到图像: {image_id}"
            
            # 验证图像格式
            image_format = image_info.get("format", "").lower()
            if image_format not in self.rules["image"]["allowed_formats"]:
                return False, f"不支持的图像格式: {image_format}"
            
            # 验证图像尺寸
            width = image_info.get("width", 0)
            height = image_info.get("height", 0)
            
            if not (self.rules["image"]["min_width"] <= width <= 
                   self.rules["image"]["max_width"]):
                return False, f"图像宽度必须在{self.rules['image']['min_width']}到{self.rules['image']['max_width']}之间"
            
            if not (self.rules["image"]["min_height"] <= height <= 
                   self.rules["image"]["max_height"]):
                return False, f"图像高度必须在{self.rules['image']['min_height']}到{self.rules['image']['max_height']}之间"
            
            # 验证图像文件完整性
            try:
                image_data = await self.minio.get_file(
                    image_info["bucket"],
                    image_info["object_name"]
                )
                
                # 尝试解码图像
                nparr = np.frombuffer(image_data, np.uint8)
                img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                if img is None:
                    return False, "图像文件损坏或无法解码"
                
                # 验证实际尺寸与记录是否一致
                actual_height, actual_width = img.shape[:2]
                if actual_width != width or actual_height != height:
                    return False, "图像实际尺寸与记录不一致"
                
            except Exception as e:
                return False, f"图像文件访问失败: {str(e)}"
            
            return True, None
            
        except Exception as e:
            return False, str(e)
    
    async def validate_dataset_integrity(
        self,
        project_id: str,
        version: str
    ) -> List[Dict[str, Any]]:
        """
        验证数据集完整性
        
        Args:
            project_id: 项目ID
            version: 数据集版本
            
        Returns:
            List[Dict[str, Any]]: 验证问题列表
        """
        try:
            issues = []
            
            # 获取版本信息
            version_info = await self.db["dataset_versions"].find_one({
                "project_id": ObjectId(project_id),
                "version": version
            })
            if not version_info:
                raise ValueError(f"找不到数据集版本: {version}")
            
            # 获取所有标注
            annotations = await self.db["annotations"].find({
                "project_id": ObjectId(project_id)
            }).to_list(None)
            
            # 验证每个标注
            for annotation in annotations:
                # 验证标注格式
                is_valid, error = await self.validate_annotation_format(annotation)
                if not is_valid:
                    issues.append({
                        "type": "annotation_format",
                        "id": str(annotation["_id"]),
                        "error": error
                    })
                    continue
                
                # 验证图像数据
                is_valid, error = await self.validate_image_data(
                    str(annotation["image_id"])
                )
                if not is_valid:
                    issues.append({
                        "type": "image_data",
                        "id": str(annotation["image_id"]),
                        "error": error
                    })
            
            # 验证数据集划分
            if "split_info" in version_info:
                split_info = version_info["split_info"]
                total = (split_info["train_size"] + split_info["val_size"] + 
                        split_info["test_size"])
                
                if total != len(annotations):
                    issues.append({
                        "type": "dataset_split",
                        "error": "数据集划分总数与标注总数不一致"
                    })
            
            return issues
            
        except Exception as e:
            raise Exception(f"验证数据集完整性失败: {str(e)}")
    
    async def generate_validation_report(
        self,
        project_id: str,
        version: str,
        output_file: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成验证报告
        
        Args:
            project_id: 项目ID
            version: 数据集版本
            output_file: 输出文件路径
            
        Returns:
            Dict[str, Any]: 验证报告
        """
        try:
            # 验证数据集
            issues = await self.validate_dataset_integrity(project_id, version)
            
            # 统计问题
            stats = {
                "total_issues": len(issues),
                "by_type": {}
            }
            
            for issue in issues:
                issue_type = issue["type"]
                if issue_type not in stats["by_type"]:
                    stats["by_type"][issue_type] = 0
                stats["by_type"][issue_type] += 1
            
            # 生成报告
            report = {
                "project_id": project_id,
                "version": version,
                "timestamp": datetime.utcnow().isoformat(),
                "statistics": stats,
                "issues": issues
            }
            
            # 保存报告
            if output_file:
                with open(output_file, "w", encoding="utf-8") as f:
                    json.dump(report, f, indent=2, ensure_ascii=False)
            
            return report
            
        except Exception as e:
            raise Exception(f"生成验证报告失败: {str(e)}") 
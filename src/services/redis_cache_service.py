"""
Redis缓存服务
用于管理系统级别的缓存，提高API响应速度
"""

from typing import Any, Optional, Union
import json
import pickle
from datetime import datetime, timedelta
import asyncio
import aioredis
from aioredis.client import Redis

from src.common.logger import get_logger
from src.common.config import get_config

logger = get_logger(__name__)

class RedisCacheService:
    """Redis缓存服务类"""
    
    def __init__(self):
        """初始化缓存服务"""
        self.config = get_config()
        self.redis: Optional[Redis] = None
        
        # 默认缓存配置
        self.default_ttl = int(self.config.get("redis", {}).get(
            "default_ttl", 3600  # 1小时
        ))
        
        # 缓存键前缀
        self.key_prefix = self.config.get("redis", {}).get(
            "key_prefix", "sake:"
        )
        
        # 缓存命中计数器
        self._hit_count = 0
        self._miss_count = 0
    
    async def connect(self):
        """连接到Redis服务器"""
        if self.redis is not None:
            return
        
        try:
            redis_url = self.config.get("redis", {}).get(
                "url", "redis://localhost:6379"
            )
            
            self.redis = await aioredis.from_url(
                redis_url,
                encoding="utf-8",
                decode_responses=True
            )
            
            # 测试连接
            await self.redis.ping()
            logger.info("Successfully connected to Redis")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {str(e)}")
            raise
    
    async def disconnect(self):
        """断开Redis连接"""
        if self.redis is not None:
            await self.redis.close()
            self.redis = None
    
    def _build_key(self, key: str) -> str:
        """
        构建缓存键
        
        Args:
            key: 原始键
            
        Returns:
            str: 带前缀的缓存键
        """
        return f"{self.key_prefix}{key}"
    
    async def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            Any: 缓存值，如果不存在则返回None
        """
        if self.redis is None:
            await self.connect()
        
        try:
            full_key = self._build_key(key)
            value = await self.redis.get(full_key)
            
            if value is not None:
                self._hit_count += 1
                try:
                    return json.loads(value)
                except json.JSONDecodeError:
                    return pickle.loads(value)
            else:
                self._miss_count += 1
                return None
                
        except Exception as e:
            logger.error(f"Error getting cache value for key {key}: {str(e)}")
            return None
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        use_pickle: bool = False
    ) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒）
            use_pickle: 是否使用pickle序列化
            
        Returns:
            bool: 是否成功
        """
        if self.redis is None:
            await self.connect()
        
        try:
            full_key = self._build_key(key)
            
            # 序列化值
            if use_pickle:
                serialized = pickle.dumps(value)
            else:
                try:
                    serialized = json.dumps(value)
                except (TypeError, ValueError):
                    serialized = pickle.dumps(value)
                    use_pickle = True
            
            # 设置缓存
            if ttl is None:
                ttl = self.default_ttl
            
            await self.redis.set(
                full_key,
                serialized,
                ex=ttl
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting cache value for key {key}: {str(e)}")
            return False
    
    async def delete(self, key: str) -> bool:
        """
        删除缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 是否成功
        """
        if self.redis is None:
            await self.connect()
        
        try:
            full_key = self._build_key(key)
            await self.redis.delete(full_key)
            return True
            
        except Exception as e:
            logger.error(f"Error deleting cache value for key {key}: {str(e)}")
            return False
    
    async def clear_prefix(self, prefix: str) -> int:
        """
        清除指定前缀的所有缓存
        
        Args:
            prefix: 键前缀
            
        Returns:
            int: 清除的键数量
        """
        if self.redis is None:
            await self.connect()
        
        try:
            pattern = f"{self.key_prefix}{prefix}*"
            keys = await self.redis.keys(pattern)
            
            if not keys:
                return 0
            
            await self.redis.delete(*keys)
            return len(keys)
            
        except Exception as e:
            logger.error(f"Error clearing cache with prefix {prefix}: {str(e)}")
            return 0
    
    async def get_stats(self) -> dict:
        """
        获取缓存统计信息
        
        Returns:
            dict: 统计信息
        """
        try:
            total_requests = self._hit_count + self._miss_count
            hit_rate = (
                self._hit_count / total_requests * 100
                if total_requests > 0 else 0
            )
            
            info = await self.redis.info()
            
            return {
                "hits": self._hit_count,
                "misses": self._miss_count,
                "hit_rate": f"{hit_rate:.2f}%",
                "memory_used": info.get("used_memory_human", "N/A"),
                "connected_clients": info.get("connected_clients", 0),
                "uptime_days": info.get("uptime_in_days", 0)
            }
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {str(e)}")
            return {
                "hits": self._hit_count,
                "misses": self._miss_count,
                "hit_rate": "N/A",
                "error": str(e)
            }
    
    async def health_check(self) -> bool:
        """
        检查Redis服务健康状态
        
        Returns:
            bool: 是否健康
        """
        try:
            if self.redis is None:
                await self.connect()
            
            # 测试基本操作
            test_key = f"{self.key_prefix}health_check"
            await self.redis.set(test_key, "ok", ex=10)
            value = await self.redis.get(test_key)
            await self.redis.delete(test_key)
            
            return value == "ok"
            
        except Exception as e:
            logger.error(f"Redis health check failed: {str(e)}")
            return False 
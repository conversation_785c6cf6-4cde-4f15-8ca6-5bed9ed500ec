"""
批处理服务API接口
提供批量文件处理的REST API
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field

from src.services.batch_processing_service import BatchProcessingService

router = APIRouter(prefix="/api/batch", tags=["batch"])
service = BatchProcessingService()

class FileInfo(BaseModel):
    """文件信息模型"""
    file_id: str = Field(..., description="文件ID")
    file_type: str = Field(..., description="文件类型")
    file_name: str = Field(..., description="文件名")

class CreateBatchRequest(BaseModel):
    """创建批处理请求模型"""
    files: List[FileInfo] = Field(..., description="文件列表")
    batch_name: str = Field(..., description="批处理任务名称")
    description: Optional[str] = Field(None, description="批处理任务描述")
    tags: Optional[List[str]] = Field(None, description="标签列表")

class BatchResponse(BaseModel):
    """批处理响应模型"""
    batch_id: str = Field(..., description="批处理任务ID")
    name: str = Field(..., description="批处理任务名称")
    description: Optional[str] = Field(None, description="批处理任务描述")
    tags: List[str] = Field(default_factory=list, description="标签列表")
    status: str = Field(..., description="任务状态")
    total_files: int = Field(..., description="总文件数")
    processed_files: int = Field(..., description="已处理文件数")
    failed_files: int = Field(..., description="失败文件数")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")

@router.post("/tasks", response_model=BatchResponse, status_code=201)
async def create_batch_task(request: CreateBatchRequest):
    """
    创建批处理任务
    """
    try:
        files = [file.dict() for file in request.files]
        batch_id = await service.create_batch(
            files,
            request.batch_name,
            request.description,
            request.tags
        )
        batch_info = await service.get_batch_info(batch_id)
        return BatchResponse(**batch_info)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tasks/{batch_id}", response_model=BatchResponse)
async def get_batch_task(batch_id: str):
    """
    获取批处理任务信息
    """
    try:
        batch_info = await service.get_batch_info(batch_id)
        if not batch_info:
            raise HTTPException(status_code=404, detail="Batch task not found")
        return BatchResponse(**batch_info)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tasks", response_model=List[BatchResponse])
async def list_batch_tasks(
    status: Optional[str] = Query(None, description="状态过滤"),
    tags: Optional[List[str]] = Query(None, description="标签过滤"),
    skip: int = Query(0, description="跳过数量"),
    limit: int = Query(10, description="返回数量")
):
    """
    获取批处理任务列表
    """
    try:
        batches = await service.list_batches(status, tags, skip, limit)
        return [BatchResponse(**batch) for batch in batches]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/tasks/{batch_id}/cancel")
async def cancel_batch_task(batch_id: str):
    """
    取消批处理任务
    """
    try:
        success = await service.cancel_batch(batch_id)
        if not success:
            raise HTTPException(
                status_code=400,
                detail="Cannot cancel completed or already cancelled task"
            )
        return {"message": "Batch task cancelled successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/tasks/{batch_id}/retry")
async def retry_failed_files(batch_id: str):
    """
    重试失败的文件
    """
    try:
        retried_count = await service.retry_failed_files(batch_id)
        return {
            "message": f"Retrying {retried_count} failed files",
            "retried_count": retried_count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 
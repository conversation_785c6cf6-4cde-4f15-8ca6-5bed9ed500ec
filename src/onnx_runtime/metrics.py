import time
from dataclasses import dataclass
from typing import Dict, Optional
from contextlib import contextmanager

@dataclass
class ModelMetrics:
    """模型指标数据类。"""
    model_name: str
    version: str
    total_requests: int = 0
    total_errors: int = 0
    total_latency: float = 0.0
    
    @property
    def avg_latency(self) -> float:
        """计算平均延迟。"""
        if self.total_requests == 0:
            return 0.0
        return self.total_latency / self.total_requests

class MetricsCollector:
    """指标收集器类。"""
    def __init__(self):
        self.metrics: Dict[str, Dict[str, ModelMetrics]] = {}
    
    def _get_or_create_metrics(self, model_name: str, version: str) -> ModelMetrics:
        """获取或创建模型指标。"""
        if model_name not in self.metrics:
            self.metrics[model_name] = {}
        if version not in self.metrics[model_name]:
            self.metrics[model_name][version] = ModelMetrics(model_name, version)
        return self.metrics[model_name][version]
    
    def get_metrics(self, model_name: str, version: str) -> Optional[ModelMetrics]:
        """获取模型指标。"""
        if model_name not in self.metrics or version not in self.metrics[model_name]:
            return None
        return self.metrics[model_name][version]
    
    def reset_metrics(self, model_name: str, version: str) -> None:
        """重置模型指标。"""
        if model_name in self.metrics and version in self.metrics[model_name]:
            metrics = self.metrics[model_name][version]
            metrics.total_requests = 0
            metrics.total_errors = 0
            metrics.total_latency = 0.0
    
    @contextmanager
    def record_inference(self, model_name: str, version: str):
        """记录推理指标的上下文管理器。"""
        metrics = self._get_or_create_metrics(model_name, version)
        start_time = time.time()
        error_occurred = False
        
        try:
            yield
        except Exception:
            error_occurred = True
            raise
        finally:
            end_time = time.time()
            metrics.total_requests += 1
            metrics.total_latency += (end_time - start_time)
            if error_occurred:
                metrics.total_errors += 1 
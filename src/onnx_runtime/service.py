import logging
from typing import Dict, List, Optional, Union, Any
import numpy as np
from pathlib import Path
import onnxruntime as ort
from .config import ONNXRuntimeConfig
from .environment import ONNXRuntimeEnvironment
from .model_manager import ONNXModelManager

class ONNXRuntimeService:
    """ONNX Runtime服务类，提供高级API接口。"""
    
    def __init__(self, config: ONNXRuntimeConfig):
        """初始化ONNX Runtime服务。"""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 初始化环境和模型管理器
        self.env = ONNXRuntimeEnvironment(config)
        self.model_manager = ONNXModelManager(config, self.env)
        
        # 加载默认模型
        self._load_default_models()
    
    def _load_default_models(self):
        """加载默认模型。"""
        for model_name, version in self.config.default_version.items():
            try:
                self.model_manager.load_model(model_name, version)
                self.logger.info(f"Loaded default model {model_name} version {version}")
            except Exception as e:
                self.logger.error(f"Failed to load default model {model_name} version {version}: {e}")
    
    def get_model_info(self, model_name: str, version: Optional[str] = None) -> Dict[str, Any]:
        """获取模型信息。"""
        if version is None:
            version = self.config.default_version.get(model_name)
            if version is None:
                raise ValueError(f"No default version found for model {model_name}")
        
        metadata = self.get_model_metadata(model_name, version)
        return {
            "name": model_name,
            "versions": self.config.model_versions.get(model_name, []),
            "default_version": self.config.default_version.get(model_name),
            **metadata
        }
    
    def predict(
        self,
        model_name: str,
        inputs: Dict[str, np.ndarray],
        version: Optional[str] = None,
        timeout: Optional[int] = None
    ) -> Dict[str, np.ndarray]:
        """使用指定模型进行预测。
        
        Args:
            model_name: 模型名称
            inputs: 输入数据字典，键为输入名称，值为numpy数组
            version: 模型版本，如果为None则使用默认版本
            timeout: 超时时间（毫秒），如果为None则使用配置中的默认值
        
        Returns:
            预测结果字典，键为输出名称，值为numpy数组
        """
        # 参数验证
        if not inputs or not isinstance(inputs, dict):
            raise ValueError("Inputs must be a non-empty dictionary")
        
        # 获取模型版本
        if version is None:
            version = self.config.default_version.get(model_name)
            if version is None:
                raise ValueError(f"No default version found for model {model_name}")
        
        # 获取会话
        session = self.model_manager.load_model(model_name, version)
        
        # 获取模型信息
        model_info = self.get_model_metadata(model_name, version)
        
        # 验证输入
        for name, array in inputs.items():
            if name not in model_info["input_names"]:
                raise ValueError(f"Invalid input name: {name}")
            expected_shape = model_info["input_shapes"][name]
            actual_shape = list(array.shape)
            
            # 只验证维度数量和非动态维度
            if len(actual_shape) != len(expected_shape):
                raise ValueError(f"Input shape mismatch for {name}: expected {len(expected_shape)} dimensions, got {len(actual_shape)}")
            
            for i, (exp, act) in enumerate(zip(expected_shape, actual_shape)):
                # 只验证非动态维度（非None）
                if exp is not None and exp != act:
                    # 第一个维度（batch_size）允许灵活
                    if i == 0:
                        continue
                    raise ValueError(f"Input shape mismatch for {name} at dimension {i}: expected {exp}, got {act}")
        
        # 设置超时
        run_options = None
        if timeout is not None or self.config.timeout_ms is not None:
            run_options = ort.RunOptions()
            run_options.timeout_in_ms = timeout or self.config.timeout_ms
        
        try:
            # 执行预测
            outputs = session.run(None, inputs, run_options)
            # 使用输出名称作为键
            return dict(zip([output.name for output in session.get_outputs()], outputs))
        except Exception as e:
            raise RuntimeError(f"Prediction failed: {str(e)}")
    
    def get_model_metadata(self, model_name: str, version: str) -> Dict[str, Any]:
        """获取模型元数据。"""
        session = self.model_manager.load_model(model_name, version)
        
        input_names = [input.name for input in session.get_inputs()]
        output_names = [output.name for output in session.get_outputs()]
        
        # 确保形状列表是可序列化的
        input_shapes = {}
        for input in session.get_inputs():
            shape = [int(dim) if isinstance(dim, (int, np.integer)) else None for dim in input.shape]
            input_shapes[input.name] = shape
            
        output_shapes = {}
        for output in session.get_outputs():
            shape = [int(dim) if isinstance(dim, (int, np.integer)) else None for dim in output.shape]
            output_shapes[output.name] = shape
        
        return {
            "input_names": input_names,
            "output_names": output_names,
            "input_shapes": input_shapes,
            "output_shapes": output_shapes
        }
    
    def list_available_models(self) -> Dict[str, List[str]]:
        """列出所有可用的模型及其版本。"""
        return self.config.model_versions
    
    def unload_model(self, model_name: str, version: Optional[str] = None) -> None:
        """卸载指定模型。"""
        if version is None:
            # 卸载所有版本
            self.model_manager.unload_all_versions(model_name)
        else:
            self.model_manager.unload_model(model_name, version)
    
    def cleanup(self) -> None:
        """清理资源。"""
        if self.model_manager:
            self.model_manager.cleanup()
        if self.env:
            self.env.cleanup() 
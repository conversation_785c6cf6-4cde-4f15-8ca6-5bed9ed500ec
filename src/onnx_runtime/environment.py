import os
import logging
from typing import List, Optional
import onnxruntime as ort
from .config import ONNXRuntimeConfig

class ONNXRuntimeEnvironment:
    """ONNX Runtime环境管理器。"""
    
    def __init__(self, config: ONNXRuntimeConfig):
        """初始化ONNX Runtime环境。"""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 设置ONNX Runtime环境变量
        os.environ['OMP_NUM_THREADS'] = str(config.num_threads)
        os.environ['OMP_WAIT_POLICY'] = 'ACTIVE'  # 主动等待策略
        
        # 初始化会话选项
        self.session_options = ort.SessionOptions()
        self.session_options.intra_op_num_threads = config.num_threads
        self.session_options.graph_optimization_level = (
            ort.GraphOptimizationLevel.ORT_ENABLE_ALL
            if config.enable_optimization
            else ort.GraphOptimizationLevel.ORT_DISABLE_ALL
        )
        self.session_options.execution_mode = ort.ExecutionMode.ORT_SEQUENTIAL
        
        # 设置日志级别（使用新的API）
        self.session_options.log_severity_level = 3  # WARNING级别
        self.session_options.log_verbosity_level = 0  # 禁用详细日志
        
        # 验证并过滤可用的执行提供者
        self.providers = self._validate_providers(config.providers)
        if not self.providers:
            raise RuntimeError("No valid ONNX Runtime providers available")
        
        self.logger.info(f"Initialized ONNX Runtime environment with providers: {self.providers}")
    
    def _validate_providers(self, requested_providers: List[str]) -> List[str]:
        """验证并过滤可用的执行提供者。"""
        available_providers = ort.get_available_providers()
        valid_providers = []
        
        for provider in requested_providers:
            if provider in available_providers:
                valid_providers.append(provider)
            else:
                self.logger.warning(f"Provider {provider} not available")
        
        return valid_providers
    
    def create_inference_session(self, model_path: str) -> ort.InferenceSession:
        """创建推理会话。"""
        try:
            session = ort.InferenceSession(
                model_path,
                sess_options=self.session_options,
                providers=self.providers
            )
            return session
        except Exception as e:
            self.logger.error(f"Failed to create inference session: {str(e)}")
            raise
    
    def get_system_info(self) -> dict:
        """获取系统信息。"""
        return {
            'onnxruntime_version': ort.__version__,
            'available_providers': ort.get_available_providers(),
            'active_providers': self.providers,
            'num_threads': self.config.num_threads,
            'optimization_enabled': self.config.enable_optimization,
            'batch_size': self.config.batch_size,
            'timeout_ms': self.config.timeout_ms
        }
    
    def validate_environment(self) -> bool:
        """验证环境是否正确配置。"""
        try:
            # 检查ONNX Runtime版本
            if not hasattr(ort, '__version__'):
                self.logger.error("ONNX Runtime version not found")
                return False
            
            # 检查执行提供者
            if not self.providers:
                self.logger.error("No valid providers available")
                return False
            
            # 检查模型目录
            if not self.config.model_dir.exists():
                self.logger.error(f"Model directory not found: {self.config.model_dir}")
                return False
            
            # 检查缓存目录
            if not self.config.cache_dir.exists():
                self.logger.error(f"Cache directory not found: {self.config.cache_dir}")
                return False
            
            return True
        except Exception as e:
            self.logger.error(f"Environment validation failed: {str(e)}")
            return False
    
    def cleanup(self):
        """清理环境资源。"""
        # 目前ONNX Runtime不需要特别的清理操作
        pass 
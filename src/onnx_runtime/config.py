from dataclasses import dataclass
from typing import Dict, List, Optional
from pathlib import Path
import os

@dataclass
class ONNXRuntimeConfig:
    """ONNX Runtime configuration class."""
    
    # 基础配置
    model_dir: Path  # ONNX模型存储目录
    cache_dir: Path  # 模型缓存目录
    providers: List[str]  # 推理提供者列表 (CPU/GPU)
    num_threads: int  # 线程数
    
    # 模型版本配置
    model_versions: Dict[str, str]  # 模型名称到版本的映射
    default_version: Dict[str, str]  # 每个模型的默认版本
    
    # A/B测试配置
    enable_ab_testing: bool = False  # 是否启用A/B测试
    ab_test_ratio: float = 0.5  # A/B测试比例
    
    # 性能配置
    enable_optimization: bool = True  # 是否启用优化
    enable_profiling: bool = False  # 是否启用性能分析
    batch_size: int = 1  # 批处理大小
    timeout_ms: int = 5000  # 推理超时时间（毫秒）
    
    @classmethod
    def from_env(cls) -> 'ONNXRuntimeConfig':
        """从环境变量创建配置实例。"""
        model_dir = Path(os.getenv('ONNX_MODEL_DIR', 'models/onnx'))
        cache_dir = Path(os.getenv('ONNX_CACHE_DIR', '.cache/onnx'))
        
        # 确保目录存在
        model_dir.mkdir(parents=True, exist_ok=True)
        cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取推理提供者列表
        providers = os.getenv('ONNX_PROVIDERS', 'CPUExecutionProvider').split(',')
        
        # 获取线程数，默认为CPU核心数的一半
        import multiprocessing
        default_threads = max(1, multiprocessing.cpu_count() // 2)
        num_threads = int(os.getenv('ONNX_NUM_THREADS', default_threads))
        
        return cls(
            model_dir=model_dir,
            cache_dir=cache_dir,
            providers=providers,
            num_threads=num_threads,
            model_versions={},  # 初始为空，后续动态加载
            default_version={},  # 初始为空，后续动态加载
            enable_ab_testing=os.getenv('ONNX_ENABLE_AB_TESTING', '').lower() == 'true',
            ab_test_ratio=float(os.getenv('ONNX_AB_TEST_RATIO', '0.5')),
            enable_optimization=os.getenv('ONNX_ENABLE_OPTIMIZATION', '').lower() != 'false',
            enable_profiling=os.getenv('ONNX_ENABLE_PROFILING', '').lower() == 'true',
            batch_size=int(os.getenv('ONNX_BATCH_SIZE', '1')),
            timeout_ms=int(os.getenv('ONNX_TIMEOUT_MS', '5000'))
        )
    
    def update_model_versions(self, model_name: str, versions: List[str], default_version: Optional[str] = None):
        """更新模型版本信息。"""
        # 更新可用版本
        self.model_versions[model_name] = versions
        
        # 更新默认版本
        if default_version is None and versions:
            default_version = versions[-1]  # 使用最新版本作为默认版本
        
        if default_version:
            self.default_version[model_name] = default_version
    
    def get_model_path(self, model_name: str, version: Optional[str] = None) -> Path:
        """获取模型文件路径。"""
        if version is None:
            version = self.default_version.get(model_name)
            if not version:
                raise ValueError(f"No default version found for model {model_name}")
        
        model_path = self.model_dir / model_name / version / f"{model_name}.onnx"
        if not model_path.exists():
            raise FileNotFoundError(f"Model file not found: {model_path}")
        
        return model_path
    
    def get_cache_path(self, model_name: str, version: str) -> Path:
        """获取模型缓存路径。"""
        return self.cache_dir / model_name / version 
import os
import json
import logging
import hashlib
import time
import shutil
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import onnxruntime as ort
from .config import ONNXRuntimeConfig
from .environment import ONNXRuntimeEnvironment

class ONNXModelManager:
    """ONNX模型管理器。"""
    
    def __init__(self, config: ONNXRuntimeConfig, env: ONNXRuntimeEnvironment):
        """初始化模型管理器。"""
        self.config = config
        self.env = env
        self.models: Dict[str, Dict[str, ort.InferenceSession]] = {}
        self.model_hashes: Dict[str, Dict[str, str]] = {}
        self.logger = logging.getLogger(__name__)
        self.model_info: Dict[str, Dict[str, dict]] = {}
        
        # 创建必要的目录
        self.config.model_dir.mkdir(parents=True, exist_ok=True)
        self.config.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载模型信息
        self._load_model_info()
    
    def _load_model_info(self):
        """加载模型信息。"""
        info_file = self.config.model_dir / 'model_info.json'
        if info_file.exists():
            try:
                with open(info_file, 'r') as f:
                    self.model_info = json.load(f)
            except Exception as e:
                self.logger.error(f"Failed to load model info: {str(e)}")
                self.model_info = {}
    
    def _save_model_info(self):
        """保存模型信息。"""
        info_file = self.config.model_dir / 'model_info.json'
        try:
            with open(info_file, 'w') as f:
                json.dump(self.model_info, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save model info: {str(e)}")
    
    def _get_model_hash(self, model_path: str) -> str:
        """计算模型文件的哈希值。"""
        hasher = hashlib.sha256()
        with open(model_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b''):
                hasher.update(chunk)
        return hasher.hexdigest()
    
    def _get_cached_model_path(self, model_name: str, version: str) -> Optional[Path]:
        """获取缓存的模型路径。"""
        if model_name in self.model_info and version in self.model_info[model_name]:
            model_hash = self.model_info[model_name][version].get('hash')
            if model_hash:
                cached_path = self.config.cache_dir / f"{model_name}_{version}_{model_hash}.onnx"
                if cached_path.exists():
                    return cached_path
        return None
    
    def load_model(self, model_name: str, version: Optional[str] = None) -> ort.InferenceSession:
        """加载指定版本的模型。"""
        # 如果未指定版本，使用默认版本
        if version is None:
            version = self.config.default_version.get(model_name)
            if not version:
                raise ValueError(f"No default version specified for model {model_name}")
        
        # 检查模型是否已加载
        if model_name in self.models and version in self.models[model_name]:
            return self.models[model_name][version]
        
        # 获取模型路径
        model_path = self.config.model_dir / model_name / version / f"{model_name}.onnx"
        if not model_path.exists():
            raise FileNotFoundError(f"Model file not found: {model_path}")
        
        try:
            # 计算模型哈希值
            model_hash = self._get_model_hash(str(model_path))
            
            # 检查缓存
            cached_session = self._load_from_cache(model_name, version, model_hash)
            if cached_session:
                self._update_model_cache(model_name, version, cached_session, model_hash)
                return cached_session
            
            # 创建新会话
            session = self._create_session(str(model_path))
            
            # 更新缓存
            self._update_model_cache(model_name, version, session, model_hash)
            
            return session
            
        except Exception as e:
            raise RuntimeError(f"Failed to load model {model_name} version {version}: {str(e)}")
    
    def get_model_info(self, model_name: str, version: Optional[str] = None) -> dict:
        """获取模型信息。"""
        if version is None:
            version = self.config.default_version.get(model_name)
            if not version:
                raise ValueError(f"No default version specified for model {model_name}")
        
        if model_name not in self.model_info or version not in self.model_info[model_name]:
            # 尝试加载模型以获取信息
            try:
                self.load_model(model_name, version)
            except Exception as e:
                raise ValueError(f"Failed to get info for model {model_name} version {version}: {str(e)}")
        
        return self.model_info[model_name][version]
    
    def list_models(self) -> Dict[str, List[str]]:
        """列出所有可用的模型及其版本。"""
        available_models = {}
        for model_dir in self.config.model_dir.iterdir():
            if model_dir.is_dir():
                model_name = model_dir.name
                versions = []
                for model_file in model_dir.glob("*.onnx"):
                    version = model_file.stem
                    versions.append(version)
                if versions:
                    available_models[model_name] = sorted(versions)
        return available_models
    
    def unload_model(self, model_name: str, version: str) -> None:
        """卸载指定版本的模型。"""
        if model_name in self.models and version in self.models[model_name]:
            session = self.models[model_name][version]
            if hasattr(session, 'end_profiling'):
                session.end_profiling()
            del self.models[model_name][version]
            if not self.models[model_name]:
                del self.models[model_name]
            if model_name in self.model_hashes and version in self.model_hashes[model_name]:
                del self.model_hashes[model_name][version]
                if not self.model_hashes[model_name]:
                    del self.model_hashes[model_name]
    
    def unload_all_versions(self, model_name: str) -> None:
        """卸载指定模型的所有版本。"""
        if model_name in self.models:
            versions = list(self.models[model_name].keys())
            for version in versions:
                self.unload_model(model_name, version)
    
    def cleanup(self) -> None:
        """清理所有资源。"""
        # 卸载所有模型
        model_names = list(self.models.keys())
        for model_name in model_names:
            self.unload_all_versions(model_name)
        
        # 清理缓存目录
        if self.config.cache_dir.exists():
            shutil.rmtree(self.config.cache_dir)
            self.config.cache_dir.mkdir(parents=True, exist_ok=True)
    
    def _load_from_cache(
        self,
        model_name: str,
        version: str,
        model_hash: str
    ) -> Optional[ort.InferenceSession]:
        """从缓存加载模型。"""
        if not self.config.enable_optimization:
            return None
        
        cache_path = self._get_cache_path(model_name, version, model_hash)
        if not cache_path.exists():
            return None
        
        try:
            return self._create_session(str(cache_path))
        except Exception:
            # 如果加载缓存失败，删除缓存文件
            cache_path.unlink(missing_ok=True)
            return None
    
    def _update_model_cache(
        self,
        model_name: str,
        version: str,
        session: ort.InferenceSession,
        model_hash: str
    ) -> None:
        """更新模型缓存。"""
        # 更新内存缓存
        if model_name not in self.models:
            self.models[model_name] = {}
        self.models[model_name][version] = session
        
        if model_name not in self.model_hashes:
            self.model_hashes[model_name] = {}
        self.model_hashes[model_name][version] = model_hash
        
        # 更新文件缓存
        if self.config.enable_optimization:
            cache_path = self._get_cache_path(model_name, version, model_hash)
            if not cache_path.exists():
                cache_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(
                    self.config.model_dir / model_name / version / f"{model_name}.onnx",
                    cache_path
                )
    
    def _get_cache_path(self, model_name: str, version: str, model_hash: str) -> Path:
        """获取缓存文件路径。"""
        return self.config.cache_dir / f"{model_name}_{version}_{model_hash}.onnx"
    
    def _create_session(self, model_path: str) -> ort.InferenceSession:
        """创建推理会话。"""
        session_options = ort.SessionOptions()
        
        # 设置线程数
        if self.config.num_threads:
            session_options.intra_op_num_threads = self.config.num_threads
        
        # 设置优化级别
        if self.config.enable_optimization:
            session_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
            session_options.optimized_model_filepath = str(model_path)
        
        # 设置性能分析
        if self.config.enable_profiling:
            session_options.enable_profiling = True
            session_options.profile_file_prefix = f"onnxruntime_profile_{int(time.time())}"
        
        return ort.InferenceSession(
            model_path,
            session_options,
            providers=self.config.providers
        )
    
    def cleanup_cache(self):
        """清理未使用的缓存文件。"""
        try:
            # 获取所有缓存文件
            cache_files = list(self.config.cache_dir.glob("*.onnx"))
            
            # 获取所有活跃的模型哈希值
            active_hashes = set()
            for model_info in self.model_info.values():
                for version_info in model_info.values():
                    active_hashes.add(version_info.get('hash'))
            
            # 删除未使用的缓存文件
            for cache_file in cache_files:
                file_hash = cache_file.stem.split('_')[-1]
                if file_hash not in active_hashes:
                    cache_file.unlink()
                    self.logger.info(f"Removed unused cache file: {cache_file}")
        
        except Exception as e:
            self.logger.error(f"Failed to cleanup cache: {str(e)}")
    
    def __del__(self):
        """清理资源。"""
        self.cleanup_cache() 
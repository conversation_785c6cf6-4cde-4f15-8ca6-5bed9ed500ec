<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实验监控仪表板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .connection-status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
            margin-top: 10px;
        }

        .connected {
            background: #4CAF50;
            color: white;
        }

        .disconnected {
            background: #f44336;
            color: white;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .card h3 {
            color: #5a67d8;
            margin-bottom: 15px;
            font-size: 1.2rem;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-radius: 10px;
            border-left: 4px solid #5a67d8;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .experiment-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .experiment-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            transition: all 0.3s ease;
        }

        .experiment-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .experiment-item.running {
            border-left-color: #007bff;
            background: linear-gradient(90deg, #e3f2fd 0%, #f8f9fa 100%);
        }

        .experiment-item.completed {
            border-left-color: #28a745;
            background: linear-gradient(90deg, #e8f5e8 0%, #f8f9fa 100%);
        }

        .experiment-item.failed {
            border-left-color: #dc3545;
            background: linear-gradient(90deg, #ffeaea 0%, #f8f9fa 100%);
        }

        .experiment-info h4 {
            margin-bottom: 5px;
            color: #2d3748;
        }

        .experiment-meta {
            font-size: 0.8rem;
            color: #718096;
        }

        .progress-bar {
            width: 100px;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4299e1 0%, #3182ce 100%);
            transition: width 0.3s ease;
        }

        .event-log {
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
        }

        .event-item {
            padding: 8px 12px;
            margin-bottom: 5px;
            border-radius: 5px;
            border-left: 3px solid #cbd5e0;
        }

        .event-item.experiment_started {
            border-left-color: #4299e1;
            background: #ebf8ff;
        }

        .event-item.experiment_completed {
            border-left-color: #48bb78;
            background: #f0fff4;
        }

        .event-item.experiment_failed {
            border-left-color: #f56565;
            background: #fed7d7;
        }

        .event-item.resource_update {
            border-left-color: #ed8936;
            background: #fffaf0;
        }

        .event-timestamp {
            color: #718096;
            font-size: 0.75rem;
        }

        .system-resources {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
        }

        .resource-item {
            text-align: center;
            padding: 10px;
            background: #f7fafc;
            border-radius: 8px;
        }

        .resource-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2d3748;
        }

        .resource-label {
            font-size: 0.8rem;
            color: #718096;
            margin-top: 5px;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #718096;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #5a67d8;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .refresh-btn {
            background: #5a67d8;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s ease;
        }

        .refresh-btn:hover {
            background: #4c51bf;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔬 实验监控仪表板</h1>
            <div id="connectionStatus" class="connection-status disconnected">
                🔴 未连接
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- 统计概览 -->
            <div class="card">
                <h3>📊 统计概览</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="totalExperiments">-</div>
                        <div class="stat-label">总实验数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="runningExperiments">-</div>
                        <div class="stat-label">运行中</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="completedExperiments">-</div>
                        <div class="stat-label">已完成</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="queuedExperiments">-</div>
                        <div class="stat-label">队列中</div>
                    </div>
                </div>
            </div>

            <!-- 系统资源 -->
            <div class="card">
                <h3>💻 系统资源</h3>
                <div class="system-resources" id="systemResources">
                    <div class="loading">
                        <div class="spinner"></div>
                        加载中...
                    </div>
                </div>
            </div>

            <!-- 调度器状态 -->
            <div class="card">
                <h3>⚙️ 调度器状态</h3>
                <div class="stat-item">
                    <div class="stat-value" id="schedulerStatus">-</div>
                    <div class="stat-label">当前状态</div>
                </div>
                <div style="margin-top: 15px;">
                    <button class="refresh-btn" onclick="requestStats()">刷新状态</button>
                </div>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- 实验列表 -->
            <div class="card" style="grid-column: 1 / -1;">
                <h3>🧪 实验列表</h3>
                <div class="experiment-list" id="experimentList">
                    <div class="loading">
                        <div class="spinner"></div>
                        加载实验数据...
                    </div>
                </div>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- 事件日志 -->
            <div class="card" style="grid-column: 1 / -1;">
                <h3>📝 事件日志</h3>
                <div class="event-log" id="eventLog">
                    <div class="loading">
                        <div class="spinner"></div>
                        加载事件日志...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class DashboardClient {
            constructor() {
                this.ws = null;
                this.reconnectInterval = 5000;
                this.maxReconnectAttempts = 10;
                this.reconnectAttempts = 0;
                this.isConnected = false;
                
                this.connect();
            }

            connect() {
                try {
                    this.ws = new WebSocket('ws://localhost:8765');
                    
                    this.ws.onopen = () => {
                        console.log('WebSocket连接已建立');
                        this.isConnected = true;
                        this.reconnectAttempts = 0;
                        this.updateConnectionStatus(true);
                        this.requestInitialData();
                    };

                    this.ws.onmessage = (event) => {
                        try {
                            const message = JSON.parse(event.data);
                            this.handleMessage(message);
                        } catch (error) {
                            console.error('解析消息失败:', error);
                        }
                    };

                    this.ws.onclose = () => {
                        console.log('WebSocket连接已关闭');
                        this.isConnected = false;
                        this.updateConnectionStatus(false);
                        this.scheduleReconnect();
                    };

                    this.ws.onerror = (error) => {
                        console.error('WebSocket错误:', error);
                        this.isConnected = false;
                        this.updateConnectionStatus(false);
                    };

                } catch (error) {
                    console.error('连接失败:', error);
                    this.scheduleReconnect();
                }
            }

            scheduleReconnect() {
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.reconnectAttempts++;
                    console.log(`${this.reconnectInterval/1000}秒后尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                    setTimeout(() => this.connect(), this.reconnectInterval);
                } else {
                    console.error('达到最大重连次数，停止重连');
                }
            }

            updateConnectionStatus(connected) {
                const statusElement = document.getElementById('connectionStatus');
                if (connected) {
                    statusElement.textContent = '🟢 已连接';
                    statusElement.className = 'connection-status connected';
                } else {
                    statusElement.textContent = '🔴 未连接';
                    statusElement.className = 'connection-status disconnected';
                }
            }

            requestInitialData() {
                this.send({ type: 'get_stats' });
                this.send({ type: 'get_experiments' });
                this.send({ type: 'get_events', limit: 20 });
            }

            send(message) {
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.ws.send(JSON.stringify(message));
                }
            }

            handleMessage(message) {
                switch (message.type) {
                    case 'initial_stats':
                    case 'stats_response':
                        this.updateStats(message.data);
                        break;
                    case 'experiment_summaries':
                    case 'experiments_response':
                        this.updateExperiments(message.data);
                        break;
                    case 'recent_events':
                    case 'events_response':
                        this.updateEvents(message.data);
                        break;
                    case 'event':
                        this.handleRealtimeEvent(message.data);
                        break;
                    default:
                        console.log('未知消息类型:', message.type);
                }
            }

            updateStats(stats) {
                document.getElementById('totalExperiments').textContent = stats.total_experiments || 0;
                document.getElementById('runningExperiments').textContent = stats.running_experiments || 0;
                document.getElementById('completedExperiments').textContent = stats.completed_experiments || 0;
                document.getElementById('queuedExperiments').textContent = stats.queued_experiments || 0;
                document.getElementById('schedulerStatus').textContent = stats.scheduler_status || 'unknown';
            }

            updateExperiments(experiments) {
                const container = document.getElementById('experimentList');
                
                if (!experiments || experiments.length === 0) {
                    container.innerHTML = '<div class="loading">暂无实验数据</div>';
                    return;
                }

                container.innerHTML = experiments.map(exp => `
                    <div class="experiment-item ${exp.status}">
                        <div class="experiment-info">
                            <h4>${exp.name}</h4>
                            <div class="experiment-meta">
                                ID: ${exp.experiment_id} | 状态: ${exp.status}
                                ${exp.start_time ? ` | 开始: ${new Date(exp.start_time).toLocaleString()}` : ''}
                            </div>
                            ${exp.status === 'running' ? `
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${(exp.progress * 100).toFixed(1)}%"></div>
                                </div>
                            ` : ''}
                        </div>
                        <div class="experiment-status">
                            ${exp.current_metrics ? `
                                <div style="font-size: 0.8rem; text-align: right;">
                                    准确率: ${(exp.current_metrics.accuracy * 100).toFixed(1)}%<br>
                                    损失: ${exp.current_metrics.loss.toFixed(3)}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `).join('');
            }

            updateEvents(events) {
                const container = document.getElementById('eventLog');
                
                if (!events || events.length === 0) {
                    container.innerHTML = '<div class="loading">暂无事件日志</div>';
                    return;
                }

                container.innerHTML = events.reverse().map(event => `
                    <div class="event-item ${event.event_type}">
                        <div class="event-timestamp">${new Date(event.timestamp).toLocaleString()}</div>
                        <div>${this.formatEventMessage(event)}</div>
                    </div>
                `).join('');
            }

            formatEventMessage(event) {
                switch (event.event_type) {
                    case 'experiment_started':
                        return `🚀 实验开始: ${event.data.name}`;
                    case 'experiment_completed':
                        return `✅ 实验完成: ${event.data.experiment_id}`;
                    case 'experiment_failed':
                        return `❌ 实验失败: ${event.data.experiment_id}`;
                    case 'experiment_progress':
                        return `📈 实验进度: ${event.data.experiment_id} (${(event.data.progress * 100).toFixed(1)}%)`;
                    case 'resource_update':
                        return `💻 资源更新: CPU ${event.data.cpu_usage?.toFixed(1)}%, 内存 ${event.data.memory_usage?.toFixed(1)}%`;
                    case 'scheduler_status':
                        return `⚙️ 调度器状态: ${event.data.status}`;
                    case 'queue_update':
                        return `📋 队列更新: ${event.data.pending_experiments} 个待处理`;
                    default:
                        return `📝 ${event.event_type}: ${JSON.stringify(event.data)}`;
                }
            }

            handleRealtimeEvent(event) {
                // 添加新事件到日志顶部
                const container = document.getElementById('eventLog');
                const eventHtml = `
                    <div class="event-item ${event.event_type}">
                        <div class="event-timestamp">${new Date(event.timestamp).toLocaleString()}</div>
                        <div>${this.formatEventMessage(event)}</div>
                    </div>
                `;
                container.insertAdjacentHTML('afterbegin', eventHtml);

                // 限制事件数量
                const events = container.children;
                if (events.length > 50) {
                    container.removeChild(events[events.length - 1]);
                }

                // 根据事件类型更新相应数据
                if (event.event_type === 'scheduler_status') {
                    this.send({ type: 'get_stats' });
                }
                if (event.event_type.includes('experiment')) {
                    this.send({ type: 'get_experiments' });
                    this.send({ type: 'get_stats' });
                }
            }
        }

        // 全局函数
        function requestStats() {
            if (dashboard.isConnected) {
                dashboard.send({ type: 'get_stats' });
                dashboard.send({ type: 'get_experiments' });
            }
        }

        // 初始化仪表板
        const dashboard = new DashboardClient();

        // 定期刷新数据
        setInterval(() => {
            if (dashboard.isConnected) {
                dashboard.send({ type: 'get_stats' });
            }
        }, 5000);
    </script>
</body>
</html> 
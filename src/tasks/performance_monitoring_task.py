"""
性能监控后台任务
定期收集和存储系统性能指标
"""

import asyncio
from datetime import datetime
from typing import Dict, Any

from src.services.performance_monitoring_service import PerformanceMonitoringService
from src.common.logger import get_logger

logger = get_logger(__name__)

class PerformanceMonitoringTask:
    """性能监控后台任务类"""
    
    def __init__(self):
        """初始化任务"""
        self.monitoring_service = PerformanceMonitoringService()
        self.is_running = False
        
    async def collect_and_store_metrics(self):
        """收集并存储指标数据"""
        try:
            # 收集系统指标
            system_metrics = await self.monitoring_service.collect_system_metrics()
            
            # 收集数据库指标
            database_metrics = await self.monitoring_service.collect_database_metrics()
            
            # 收集API指标
            api_metrics = await self.monitoring_service.collect_api_metrics()
            
            # 合并指标数据
            metrics = {
                "timestamp": datetime.now().isoformat(),
                "system": system_metrics,
                "database": database_metrics,
                "api": api_metrics
            }
            
            # 存储指标数据
            await self.monitoring_service.store_metrics(metrics)
            
        except Exception as e:
            logger.error(f"Failed to collect and store metrics: {str(e)}")
            
    async def run(self):
        """运行任务"""
        self.is_running = True
        
        try:
            while self.is_running:
                await self.collect_and_store_metrics()
                
                # 等待下一个收集周期
                await asyncio.sleep(
                    self.monitoring_service.collection_interval
                )
                
        except Exception as e:
            logger.error(f"Performance monitoring task failed: {str(e)}")
            self.is_running = False
            
    def stop(self):
        """停止任务"""
        self.is_running = False 
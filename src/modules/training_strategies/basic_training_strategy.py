#!/usr/bin/env python3
"""
基础训练策略模块
演示模块化架构中的训练策略实现
"""

import sys
import os
from pathlib import Path

# 添加backend路径
backend_path = Path(__file__).parent.parent.parent / "backend"
sys.path.insert(0, str(backend_path))

from modular_architecture import TrainingStrategyModule, ModuleStatus
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)


class BasicTrainingStrategy(TrainingStrategyModule):
    """基础训练策略模块"""
    
    def __init__(self):
        super().__init__(
            name="BasicTrainingStrategy",
            version="1.0.0",
            description="基础的训练策略，支持标准的监督学习训练流程"
        )
        
        # 设置模块特定属性
        self.supported_model_types = ["classification", "detection", "recognition"]
        self.required_data_format = {
            "input": "tensor",
            "labels": "tensor",
            "batch_size": "int"
        }
        self.hyperparameters = {
            "learning_rate": 0.001,
            "batch_size": 32,
            "epochs": 100,
            "optimizer": "adam",
            "loss_function": "cross_entropy"
        }
        
        # 初始化状态
        self.update_status(ModuleStatus.INACTIVE)
    
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化模块"""
        try:
            # 验证配置
            if not self.validate_config(config):
                logger.error("配置验证失败")
                return False
            
            # 更新超参数
            if "hyperparameters" in config:
                self.hyperparameters.update(config["hyperparameters"])
            
            # 设置元数据
            self.metadata = {
                "initialized_at": str(self.last_updated),
                "config": config,
                "hyperparameters": self.hyperparameters
            }
            
            # 更新状态
            self.update_status(ModuleStatus.ACTIVE)
            
            logger.info(f"模块 {self.name} 初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"模块初始化失败: {e}")
            self.update_status(ModuleStatus.ERROR)
            return False
    
    def execute(self, *args, **kwargs) -> Any:
        """执行模块主要功能"""
        return self.train(*args, **kwargs)
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        try:
            # 检查必需的配置项
            required_keys = ["model_type", "data_format"]
            for key in required_keys:
                if key not in config:
                    logger.error(f"缺少必需的配置项: {key}")
                    return False
            
            # 验证模型类型
            if config["model_type"] not in self.supported_model_types:
                logger.error(f"不支持的模型类型: {config['model_type']}")
                return False
            
            # 验证超参数
            if "hyperparameters" in config:
                hp = config["hyperparameters"]
                if "learning_rate" in hp and (hp["learning_rate"] <= 0 or hp["learning_rate"] > 1):
                    logger.error("学习率必须在 (0, 1] 范围内")
                    return False
                
                if "batch_size" in hp and hp["batch_size"] <= 0:
                    logger.error("批次大小必须大于0")
                    return False
                
                if "epochs" in hp and hp["epochs"] <= 0:
                    logger.error("训练轮数必须大于0")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False
    
    def train(self, data, model, config: Dict[str, Any]) -> Dict[str, Any]:
        """执行训练"""
        try:
            if self.status != ModuleStatus.ACTIVE:
                return {
                    "success": False,
                    "message": "模块未激活",
                    "status": self.status.value
                }
            
            logger.info(f"开始使用 {self.name} 进行训练")
            
            # 获取训练配置
            training_config = config.get("training_config", {})
            hyperparams = {**self.hyperparameters, **training_config.get("hyperparameters", {})}
            
            # 模拟训练过程
            training_results = {
                "strategy": self.name,
                "model_type": config.get("model_type", "unknown"),
                "hyperparameters": hyperparams,
                "training_steps": hyperparams["epochs"],
                "final_loss": 0.1,  # 模拟最终损失
                "accuracy": 0.95,   # 模拟准确率
                "training_time": "30 minutes",  # 模拟训练时间
                "convergence": True
            }
            
            logger.info(f"训练完成，准确率: {training_results['accuracy']}")
            
            return {
                "success": True,
                "results": training_results,
                "message": "训练完成"
            }
            
        except Exception as e:
            logger.error(f"训练失败: {e}")
            return {
                "success": False,
                "message": f"训练失败: {e}",
                "error": str(e)
            }
    
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "model_type": "classification",
            "data_format": "tensor",
            "hyperparameters": self.hyperparameters.copy(),
            "training_config": {
                "use_gpu": True,
                "mixed_precision": False,
                "gradient_clipping": 1.0,
                "early_stopping": {
                    "enabled": True,
                    "patience": 10,
                    "min_delta": 0.001
                }
            },
            "validation": {
                "split_ratio": 0.2,
                "shuffle": True,
                "stratify": True
            }
        }
    
    def get_training_metrics(self) -> List[str]:
        """获取支持的训练指标"""
        return [
            "loss",
            "accuracy", 
            "precision",
            "recall",
            "f1_score",
            "learning_rate",
            "gradient_norm"
        ]
    
    def supports_model_type(self, model_type: str) -> bool:
        """检查是否支持指定的模型类型"""
        return model_type in self.supported_model_types
    
    def get_recommended_hyperparameters(self, model_type: str, dataset_size: int) -> Dict[str, Any]:
        """根据模型类型和数据集大小推荐超参数"""
        base_params = self.hyperparameters.copy()
        
        # 根据数据集大小调整
        if dataset_size < 1000:
            base_params["learning_rate"] = 0.01
            base_params["batch_size"] = 16
            base_params["epochs"] = 50
        elif dataset_size < 10000:
            base_params["learning_rate"] = 0.001
            base_params["batch_size"] = 32
            base_params["epochs"] = 100
        else:
            base_params["learning_rate"] = 0.0001
            base_params["batch_size"] = 64
            base_params["epochs"] = 200
        
        # 根据模型类型调整
        if model_type == "detection":
            base_params["learning_rate"] *= 0.1  # 检测模型通常需要更小的学习率
            base_params["batch_size"] = min(base_params["batch_size"], 16)  # 检测模型内存占用大
        elif model_type == "recognition":
            base_params["epochs"] *= 1.5  # 识别模型可能需要更多训练轮数
        
        return base_params 
#!/usr/bin/env python3
"""
投票集成方法模块
演示模块化架构中的集成方法实现
"""

import sys
import os
from pathlib import Path

# 添加backend路径
backend_path = Path(__file__).parent.parent.parent / "backend"
sys.path.insert(0, str(backend_path))

from modular_architecture import BaseModule, ModuleType, ModuleStatus
from typing import Dict, List, Optional, Any, Union
import logging
import numpy as np

logger = logging.getLogger(__name__)


class VotingEnsemble(BaseModule):
    """投票集成方法模块"""
    
    def __init__(self):
        super().__init__(
            name="VotingEnsemble",
            version="1.0.0",
            description="投票集成方法，支持硬投票和软投票两种模式"
        )
        
        # 设置模块类型
        self.module_type = ModuleType.ENSEMBLE_METHOD
        
        # 设置模块特定属性
        self.voting_types = ["hard", "soft"]
        self.supported_tasks = ["classification", "detection"]
        self.models = []
        self.weights = []
        self.voting_type = "soft"
        
        # 初始化状态
        self.update_status(ModuleStatus.INACTIVE)
    
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化模块"""
        try:
            # 验证配置
            if not self.validate_config(config):
                logger.error("配置验证失败")
                return False
            
            # 设置投票类型
            self.voting_type = config.get("voting_type", "soft")
            
            # 设置模型列表
            self.models = config.get("models", [])
            
            # 设置权重
            weights = config.get("weights", None)
            if weights:
                self.weights = weights
            else:
                # 默认等权重
                self.weights = [1.0 / len(self.models)] * len(self.models)
            
            # 设置元数据
            self.metadata = {
                "initialized_at": str(self.last_updated),
                "config": config,
                "voting_type": self.voting_type,
                "num_models": len(self.models),
                "weights": self.weights
            }
            
            # 更新状态
            self.update_status(ModuleStatus.ACTIVE)
            
            logger.info(f"模块 {self.name} 初始化成功，投票类型: {self.voting_type}")
            return True
            
        except Exception as e:
            logger.error(f"模块初始化失败: {e}")
            self.update_status(ModuleStatus.ERROR)
            return False
    
    def execute(self, *args, **kwargs) -> Any:
        """执行模块主要功能"""
        return self.ensemble_predict(*args, **kwargs)
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        try:
            # 检查投票类型
            voting_type = config.get("voting_type", "soft")
            if voting_type not in self.voting_types:
                logger.error(f"不支持的投票类型: {voting_type}")
                return False
            
            # 检查模型列表
            models = config.get("models", [])
            if len(models) < 2:
                logger.error("集成方法至少需要2个模型")
                return False
            
            # 检查权重
            weights = config.get("weights", None)
            if weights:
                if len(weights) != len(models):
                    logger.error("权重数量必须与模型数量相等")
                    return False
                
                if abs(sum(weights) - 1.0) > 1e-6:
                    logger.error("权重总和必须等于1.0")
                    return False
                
                if any(w < 0 for w in weights):
                    logger.error("权重不能为负数")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False
    
    def ensemble_predict(self, predictions: List[Any], task_type: str = "classification") -> Dict[str, Any]:
        """执行集成预测"""
        try:
            if self.status != ModuleStatus.ACTIVE:
                return {
                    "success": False,
                    "message": "模块未激活",
                    "status": self.status.value
                }
            
            if len(predictions) != len(self.models):
                return {
                    "success": False,
                    "message": "预测结果数量与模型数量不匹配"
                }
            
            logger.info(f"开始使用 {self.voting_type} 投票进行集成预测")
            
            # 根据任务类型和投票类型进行集成
            if task_type == "classification":
                result = self._ensemble_classification(predictions)
            elif task_type == "detection":
                result = self._ensemble_detection(predictions)
            else:
                return {
                    "success": False,
                    "message": f"不支持的任务类型: {task_type}"
                }
            
            return {
                "success": True,
                "result": result,
                "voting_type": self.voting_type,
                "num_models": len(predictions),
                "weights": self.weights,
                "message": "集成预测完成"
            }
            
        except Exception as e:
            logger.error(f"集成预测失败: {e}")
            return {
                "success": False,
                "message": f"集成预测失败: {e}",
                "error": str(e)
            }
    
    def _ensemble_classification(self, predictions: List[Any]) -> Dict[str, Any]:
        """分类任务的集成预测"""
        if self.voting_type == "hard":
            return self._hard_voting_classification(predictions)
        else:
            return self._soft_voting_classification(predictions)
    
    def _hard_voting_classification(self, predictions: List[Any]) -> Dict[str, Any]:
        """硬投票分类"""
        # 模拟硬投票过程
        # 假设predictions是类别标签列表
        weighted_votes = {}
        
        for i, pred in enumerate(predictions):
            weight = self.weights[i]
            if pred not in weighted_votes:
                weighted_votes[pred] = 0
            weighted_votes[pred] += weight
        
        # 选择得票最多的类别
        final_prediction = max(weighted_votes, key=weighted_votes.get)
        confidence = weighted_votes[final_prediction]
        
        return {
            "prediction": final_prediction,
            "confidence": confidence,
            "votes": weighted_votes,
            "method": "hard_voting"
        }
    
    def _soft_voting_classification(self, predictions: List[Any]) -> Dict[str, Any]:
        """软投票分类"""
        # 模拟软投票过程
        # 假设predictions是概率分布列表
        
        # 模拟概率分布（实际应用中这些来自模型输出）
        num_classes = 3  # 假设3个类别
        ensemble_probs = np.zeros(num_classes)
        
        for i, pred in enumerate(predictions):
            weight = self.weights[i]
            # 模拟概率分布
            if isinstance(pred, (list, np.ndarray)):
                probs = np.array(pred)
            else:
                # 如果是单个预测，转换为one-hot
                probs = np.zeros(num_classes)
                probs[pred] = 1.0
            
            ensemble_probs += weight * probs
        
        # 归一化
        ensemble_probs = ensemble_probs / np.sum(ensemble_probs)
        
        # 选择概率最高的类别
        final_prediction = np.argmax(ensemble_probs)
        confidence = ensemble_probs[final_prediction]
        
        return {
            "prediction": int(final_prediction),
            "confidence": float(confidence),
            "probabilities": ensemble_probs.tolist(),
            "method": "soft_voting"
        }
    
    def _ensemble_detection(self, predictions: List[Any]) -> Dict[str, Any]:
        """检测任务的集成预测"""
        # 模拟检测结果集成
        # 通常使用NMS (Non-Maximum Suppression) 或加权平均
        
        all_boxes = []
        all_scores = []
        all_classes = []
        
        for i, pred in enumerate(predictions):
            weight = self.weights[i]
            
            # 假设pred包含boxes, scores, classes
            if isinstance(pred, dict):
                boxes = pred.get("boxes", [])
                scores = pred.get("scores", [])
                classes = pred.get("classes", [])
                
                # 应用权重到分数
                weighted_scores = [s * weight for s in scores]
                
                all_boxes.extend(boxes)
                all_scores.extend(weighted_scores)
                all_classes.extend(classes)
        
        # 模拟NMS过程（简化版）
        # 实际应用中需要实现完整的NMS算法
        final_boxes = all_boxes[:10]  # 取前10个框
        final_scores = all_scores[:10]
        final_classes = all_classes[:10]
        
        return {
            "boxes": final_boxes,
            "scores": final_scores,
            "classes": final_classes,
            "num_detections": len(final_boxes),
            "method": "weighted_nms"
        }
    
    def add_model(self, model_id: str, weight: float = None) -> bool:
        """添加模型到集成"""
        try:
            self.models.append(model_id)
            
            if weight is None:
                # 重新计算等权重
                num_models = len(self.models)
                self.weights = [1.0 / num_models] * num_models
            else:
                self.weights.append(weight)
                # 重新归一化权重
                total_weight = sum(self.weights)
                self.weights = [w / total_weight for w in self.weights]
            
            logger.info(f"添加模型 {model_id} 到集成，当前模型数: {len(self.models)}")
            return True
            
        except Exception as e:
            logger.error(f"添加模型失败: {e}")
            return False
    
    def remove_model(self, model_id: str) -> bool:
        """从集成中移除模型"""
        try:
            if model_id not in self.models:
                logger.warning(f"模型 {model_id} 不在集成中")
                return False
            
            index = self.models.index(model_id)
            self.models.pop(index)
            self.weights.pop(index)
            
            # 重新归一化权重
            if self.weights:
                total_weight = sum(self.weights)
                self.weights = [w / total_weight for w in self.weights]
            
            logger.info(f"从集成中移除模型 {model_id}，当前模型数: {len(self.models)}")
            return True
            
        except Exception as e:
            logger.error(f"移除模型失败: {e}")
            return False
    
    def update_weights(self, new_weights: List[float]) -> bool:
        """更新模型权重"""
        try:
            if len(new_weights) != len(self.models):
                logger.error("权重数量必须与模型数量相等")
                return False
            
            if abs(sum(new_weights) - 1.0) > 1e-6:
                logger.error("权重总和必须等于1.0")
                return False
            
            self.weights = new_weights.copy()
            logger.info("模型权重更新成功")
            return True
            
        except Exception as e:
            logger.error(f"更新权重失败: {e}")
            return False
    
    def get_ensemble_info(self) -> Dict[str, Any]:
        """获取集成信息"""
        return {
            "name": self.name,
            "version": self.version,
            "voting_type": self.voting_type,
            "num_models": len(self.models),
            "models": self.models,
            "weights": self.weights,
            "supported_tasks": self.supported_tasks,
            "status": self.status.value
        } 
#!/usr/bin/env python3
"""
JSON数据交换标准模块
定义OCR和注释系统之间的完整JSON模式和验证机制
"""

import json
import jsonschema
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataType(Enum):
    """数据类型枚举"""
    OCR_RESULT = "ocr_result"
    ENHANCED_OCR_RESULT = "enhanced_ocr_result"
    ANNOTATION_TASK = "annotation_task"
    ANNOTATION_BATCH = "annotation_batch"
    ANNOTATION_CHANGE = "annotation_change"
    COMPLETED_ANNOTATION = "completed_annotation"

class ConfidenceLevel(Enum):
    """置信度级别枚举"""
    VERY_LOW = "very_low"      # 0.0 - 0.3
    LOW = "low"                # 0.3 - 0.5
    MEDIUM = "medium"          # 0.5 - 0.7
    HIGH = "high"              # 0.7 - 0.9
    VERY_HIGH = "very_high"    # 0.9 - 1.0

class AnnotationStatus(Enum):
    """注释状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    VERIFIED = "verified"
    REJECTED = "rejected"
    CANCELLED = "cancelled"

@dataclass
class BoundingBox:
    """标准化边界框数据结构"""
    x1: float
    y1: float
    x2: float
    y2: float
    width: Optional[float] = None
    height: Optional[float] = None
    
    def __post_init__(self):
        """计算宽度和高度"""
        if self.width is None:
            self.width = abs(self.x2 - self.x1)
        if self.height is None:
            self.height = abs(self.y2 - self.y1)
    
    def to_polygon(self) -> List[List[float]]:
        """转换为多边形格式"""
        return [
            [self.x1, self.y1],
            [self.x2, self.y1],
            [self.x2, self.y2],
            [self.x1, self.y2]
        ]
    
    @classmethod
    def from_polygon(cls, polygon: List[List[float]]) -> 'BoundingBox':
        """从多边形格式创建边界框"""
        if len(polygon) < 4:
            raise ValueError("多边形至少需要4个点")
        
        x_coords = [point[0] for point in polygon]
        y_coords = [point[1] for point in polygon]
        
        return cls(
            x1=min(x_coords),
            y1=min(y_coords),
            x2=max(x_coords),
            y2=max(y_coords)
        )

@dataclass
class TextRegion:
    """文本区域数据结构"""
    region_id: str
    text: str
    confidence: float
    bounding_box: BoundingBox
    language: Optional[str] = None
    reading_direction: Optional[str] = "ltr"  # ltr, rtl, ttb
    font_size: Optional[float] = None
    font_family: Optional[str] = None
    
    def get_confidence_level(self) -> ConfidenceLevel:
        """获取置信度级别"""
        if self.confidence < 0.3:
            return ConfidenceLevel.VERY_LOW
        elif self.confidence < 0.5:
            return ConfidenceLevel.LOW
        elif self.confidence < 0.7:
            return ConfidenceLevel.MEDIUM
        elif self.confidence < 0.9:
            return ConfidenceLevel.HIGH
        else:
            return ConfidenceLevel.VERY_HIGH

@dataclass
class CharacterResult:
    """字符级识别结果"""
    character: str
    confidence: float
    bounding_box: BoundingBox
    alternatives: Optional[List[Dict[str, Any]]] = None

@dataclass
class LineResult:
    """行级识别结果"""
    line_id: str
    text: str
    confidence: float
    bounding_box: BoundingBox
    characters: Optional[List[CharacterResult]] = None
    baseline: Optional[List[List[float]]] = None

class JSONSchemaStandards:
    """JSON模式标准管理器"""
    
    def __init__(self):
        """初始化模式标准"""
        self.schemas = self._define_schemas()
        self.validators = {
            data_type: jsonschema.Draft7Validator(schema)
            for data_type, schema in self.schemas.items()
        }
    
    def _define_schemas(self) -> Dict[DataType, Dict[str, Any]]:
        """定义所有JSON模式"""
        schemas = {}
        
        # 基础边界框模式
        bounding_box_schema = {
            "type": "object",
            "properties": {
                "x1": {"type": "number"},
                "y1": {"type": "number"},
                "x2": {"type": "number"},
                "y2": {"type": "number"},
                "width": {"type": "number"},
                "height": {"type": "number"}
            },
            "required": ["x1", "y1", "x2", "y2"],
            "additionalProperties": False
        }
        
        # 文本区域模式
        text_region_schema = {
            "type": "object",
            "properties": {
                "region_id": {"type": "string"},
                "text": {"type": "string"},
                "confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                "bounding_box": bounding_box_schema,
                "language": {"type": ["string", "null"]},
                "reading_direction": {"type": "string", "enum": ["ltr", "rtl", "ttb"]},
                "font_size": {"type": ["number", "null"]},
                "font_family": {"type": ["string", "null"]}
            },
            "required": ["region_id", "text", "confidence", "bounding_box"],
            "additionalProperties": False
        }
        
        # OCR结果模式
        schemas[DataType.OCR_RESULT] = {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "OCR Result Schema",
            "type": "object",
            "properties": {
                "data_type": {"type": "string", "const": "ocr_result"},
                "version": {"type": "string", "pattern": r"^\d+\.\d+\.\d+$"},
                "timestamp": {"type": "string", "format": "date-time"},
                "file_id": {"type": "string"},
                "file_path": {"type": "string"},
                "image_info": {
                    "type": "object",
                    "properties": {
                        "width": {"type": "integer", "minimum": 1},
                        "height": {"type": "integer", "minimum": 1},
                        "format": {"type": "string"},
                        "dpi": {"type": ["number", "null"]},
                        "color_mode": {"type": ["string", "null"]}
                    },
                    "required": ["width", "height", "format"]
                },
                "processing_info": {
                    "type": "object",
                    "properties": {
                        "engine": {"type": "string"},
                        "model_version": {"type": "string"},
                        "processing_time": {"type": "number", "minimum": 0},
                        "parameters": {"type": "object"}
                    },
                    "required": ["engine", "processing_time"]
                },
                "text_regions": {
                    "type": "array",
                    "items": text_region_schema
                },
                "statistics": {
                    "type": "object",
                    "properties": {
                        "total_regions": {"type": "integer", "minimum": 0},
                        "avg_confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                        "low_confidence_count": {"type": "integer", "minimum": 0},
                        "detected_languages": {"type": "array", "items": {"type": "string"}}
                    },
                    "required": ["total_regions", "avg_confidence"]
                }
            },
            "required": ["data_type", "version", "timestamp", "file_id", "text_regions", "statistics"],
            "additionalProperties": False
        }
        
        # 增强OCR结果模式
        schemas[DataType.ENHANCED_OCR_RESULT] = {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "Enhanced OCR Result Schema",
            "type": "object",
            "properties": {
                "data_type": {"type": "string", "const": "enhanced_ocr_result"},
                "version": {"type": "string", "pattern": r"^\d+\.\d+\.\d+$"},
                "timestamp": {"type": "string", "format": "date-time"},
                "file_id": {"type": "string"},
                "file_path": {"type": "string"},
                "image_info": {
                    "type": "object",
                    "properties": {
                        "width": {"type": "integer", "minimum": 1},
                        "height": {"type": "integer", "minimum": 1},
                        "format": {"type": "string"},
                        "dpi": {"type": ["number", "null"]},
                        "color_mode": {"type": ["string", "null"]}
                    },
                    "required": ["width", "height", "format"]
                },
                "processing_info": {
                    "type": "object",
                    "properties": {
                        "engine": {"type": "string"},
                        "model_version": {"type": "string"},
                        "processing_time": {"type": "number", "minimum": 0},
                        "parameters": {"type": "object"},
                        "multilingual_enabled": {"type": "boolean"},
                        "character_level_enabled": {"type": "boolean"}
                    },
                    "required": ["engine", "processing_time"]
                },
                "text_regions": {
                    "type": "array",
                    "items": text_region_schema
                },
                "line_results": {
                    "type": ["array", "null"],
                    "items": {
                        "type": "object",
                        "properties": {
                            "line_id": {"type": "string"},
                            "text": {"type": "string"},
                            "confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                            "bounding_box": bounding_box_schema,
                            "characters": {
                                "type": ["array", "null"],
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "character": {"type": "string"},
                                        "confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                                        "bounding_box": bounding_box_schema,
                                        "alternatives": {
                                            "type": ["array", "null"],
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "character": {"type": "string"},
                                                    "confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0}
                                                },
                                                "required": ["character", "confidence"]
                                            }
                                        }
                                    },
                                    "required": ["character", "confidence", "bounding_box"]
                                }
                            },
                            "baseline": {
                                "type": ["array", "null"],
                                "items": {
                                    "type": "array",
                                    "items": {"type": "number"},
                                    "minItems": 2,
                                    "maxItems": 2
                                }
                            }
                        },
                        "required": ["line_id", "text", "confidence", "bounding_box"]
                    }
                },
                "language_analysis": {
                    "type": ["object", "null"],
                    "properties": {
                        "detected_languages": {"type": "array", "items": {"type": "string"}},
                        "primary_language": {"type": "string"},
                        "language_distribution": {
                            "type": "object",
                            "patternProperties": {
                                "^[a-z]{2,3}$": {"type": "number", "minimum": 0.0, "maximum": 1.0}
                            }
                        },
                        "reading_direction": {"type": "string", "enum": ["ltr", "rtl", "ttb", "mixed"]}
                    }
                },
                "confidence_analysis": {
                    "type": ["object", "null"],
                    "properties": {
                        "overall_confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                        "character_level_confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                        "line_level_confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                        "confidence_distribution": {
                            "type": "object",
                            "properties": {
                                "very_high": {"type": "integer", "minimum": 0},
                                "high": {"type": "integer", "minimum": 0},
                                "medium": {"type": "integer", "minimum": 0},
                                "low": {"type": "integer", "minimum": 0},
                                "very_low": {"type": "integer", "minimum": 0}
                            }
                        }
                    }
                },
                "statistics": {
                    "type": "object",
                    "properties": {
                        "total_regions": {"type": "integer", "minimum": 0},
                        "total_lines": {"type": "integer", "minimum": 0},
                        "total_characters": {"type": "integer", "minimum": 0},
                        "avg_confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                        "low_confidence_count": {"type": "integer", "minimum": 0},
                        "detected_languages": {"type": "array", "items": {"type": "string"}}
                    },
                    "required": ["total_regions", "avg_confidence"]
                }
            },
            "required": ["data_type", "version", "timestamp", "file_id", "text_regions", "statistics"],
            "additionalProperties": False
        }
        
        # 注释任务模式
        schemas[DataType.ANNOTATION_TASK] = {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "Annotation Task Schema",
            "type": "object",
            "properties": {
                "data_type": {"type": "string", "const": "annotation_task"},
                "version": {"type": "string", "pattern": r"^\d+\.\d+\.\d+$"},
                "timestamp": {"type": "string", "format": "date-time"},
                "task_id": {"type": "string"},
                "file_id": {"type": "string"},
                "source_data": {"type": "object"},  # 允许任何对象作为源数据
                "annotation_config": {
                    "type": "object",
                    "properties": {
                        "interface_type": {"type": "string", "enum": ["web", "mobile", "api"]},
                        "confidence_threshold": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                        "auto_review_enabled": {"type": "boolean"},
                        "batch_size": {"type": "integer", "minimum": 1},
                        "task_priority": {"type": "string", "enum": ["low", "medium", "high", "urgent"]},
                        "deadline": {"type": ["string", "null"], "format": "date-time"},
                        "assigned_user": {"type": ["string", "null"]},
                        "quality_requirements": {
                            "type": ["object", "null"],
                            "properties": {
                                "min_confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                                "require_verification": {"type": "boolean"},
                                "max_edit_time": {"type": ["number", "null"]}
                            }
                        }
                    },
                    "required": ["interface_type", "confidence_threshold"]
                },
                "regions_to_annotate": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "region_id": {"type": "string"},
                            "original_text": {"type": "string"},
                            "original_confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                            "bounding_box": bounding_box_schema,
                            "requires_review": {"type": "boolean"},
                            "annotation_type": {"type": "string", "enum": ["text_correction", "verification", "translation", "classification"]},
                            "metadata": {"type": ["object", "null"]}
                        },
                        "required": ["region_id", "original_text", "original_confidence", "bounding_box", "annotation_type"]
                    }
                },
                "task_metadata": {
                    "type": ["object", "null"],
                    "properties": {
                        "created_by": {"type": "string"},
                        "creation_time": {"type": "string", "format": "date-time"},
                        "estimated_duration": {"type": ["number", "null"]},
                        "complexity_score": {"type": ["number", "null"], "minimum": 0.0, "maximum": 10.0},
                        "tags": {"type": "array", "items": {"type": "string"}},
                        "notes": {"type": ["string", "null"]}
                    }
                },
                "status": {"type": "string", "enum": ["pending", "in_progress", "completed", "verified", "rejected", "cancelled"]},
                "progress": {
                    "type": "object",
                    "properties": {
                        "total_regions": {"type": "integer", "minimum": 0},
                        "completed_regions": {"type": "integer", "minimum": 0},
                        "verified_regions": {"type": "integer", "minimum": 0},
                        "progress_percentage": {"type": "number", "minimum": 0.0, "maximum": 100.0}
                    },
                    "required": ["total_regions", "completed_regions", "progress_percentage"]
                }
            },
            "required": ["data_type", "version", "timestamp", "task_id", "file_id", "source_data", "annotation_config", "regions_to_annotate", "status", "progress"],
            "additionalProperties": False
        }
        
        # 注释变更模式
        schemas[DataType.ANNOTATION_CHANGE] = {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "Annotation Change Schema",
            "type": "object",
            "properties": {
                "data_type": {"type": "string", "const": "annotation_change"},
                "version": {"type": "string", "pattern": r"^\d+\.\d+\.\d+$"},
                "change_id": {"type": "string"},
                "task_id": {"type": "string"},
                "region_id": {"type": "string"},
                "field_name": {"type": "string"},
                "old_value": {},  # 可以是任何类型
                "new_value": {},  # 可以是任何类型
                "change_type": {"type": "string", "enum": ["text_edit", "confidence_adjust", "bbox_modify", "status_change", "metadata_update"]},
                "timestamp": {"type": "string", "format": "date-time"},
                "user_id": {"type": "string"},
                "confidence_score": {"type": ["number", "null"], "minimum": 0.0, "maximum": 1.0},
                "validation_status": {"type": "string", "enum": ["pending", "approved", "rejected"], "default": "pending"},
                "metadata": {
                    "type": ["object", "null"],
                    "properties": {
                        "edit_duration": {"type": ["number", "null"]},
                        "edit_method": {"type": ["string", "null"]},
                        "quality_score": {"type": ["number", "null"], "minimum": 0.0, "maximum": 1.0},
                        "notes": {"type": ["string", "null"]}
                    }
                }
            },
            "required": ["data_type", "version", "change_id", "task_id", "region_id", "field_name", "old_value", "new_value", "change_type", "timestamp", "user_id"],
            "additionalProperties": False
        }
        
        # 完成注释模式
        schemas[DataType.COMPLETED_ANNOTATION] = {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "Completed Annotation Schema",
            "type": "object",
            "properties": {
                "data_type": {"type": "string", "const": "completed_annotation"},
                "version": {"type": "string", "pattern": r"^\d+\.\d+\.\d+$"},
                "annotation_id": {"type": "string"},
                "task_id": {"type": "string"},
                "file_id": {"type": "string"},
                "region_id": {"type": "string"},
                "original_text": {"type": "string"},
                "annotated_text": {"type": "string"},
                "original_confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                "final_confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                "bounding_box": bounding_box_schema,
                "annotation_type": {"type": "string", "enum": ["text_correction", "verification", "translation", "classification"]},
                "quality_score": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                "completion_time": {"type": "string", "format": "date-time"},
                "user_id": {"type": "string"},
                "edit_time": {"type": "number", "minimum": 0}
            },
            "required": ["data_type", "version", "annotation_id", "task_id", "file_id", "region_id", "original_text", "annotated_text", "original_confidence", "final_confidence", "bounding_box", "annotation_type", "quality_score", "completion_time", "user_id", "edit_time"],
            "additionalProperties": False
        }
        
        # 注释批次模式
        schemas[DataType.ANNOTATION_BATCH] = {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "Annotation Batch Schema",
            "type": "object",
            "properties": {
                "data_type": {"type": "string", "const": "annotation_batch"},
                "version": {"type": "string", "pattern": r"^\d+\.\d+\.\d+$"},
                "batch_id": {"type": "string"},
                "task_id": {"type": "string"},
                "file_id": {"type": "string"},
                "completion_time": {"type": "string", "format": "date-time"},
                "total_regions": {"type": "integer", "minimum": 0},
                "completed_regions": {"type": "integer", "minimum": 0},
                "user_id": {"type": "string"},
                "batch_statistics": {
                    "type": "object",
                    "properties": {
                        "total_annotations": {"type": "integer", "minimum": 0},
                        "avg_quality_score": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                        "completion_rate": {"type": "number", "minimum": 0.0, "maximum": 1.0}
                    },
                    "required": ["total_annotations", "avg_quality_score", "completion_rate"]
                }
            },
            "required": ["data_type", "version", "batch_id", "task_id", "file_id", "completion_time", "total_regions", "completed_regions", "user_id", "batch_statistics"],
            "additionalProperties": True
        }
        
        return schemas
    
    def validate_data(self, data: Dict[str, Any], data_type: DataType) -> tuple[bool, List[str]]:
        """验证数据是否符合指定的模式"""
        try:
            validator = self.validators[data_type]
            errors = list(validator.iter_errors(data))
            
            if errors:
                error_messages = [
                    f"路径 '{'.'.join(str(p) for p in error.absolute_path)}': {error.message}"
                    for error in errors
                ]
                return False, error_messages
            
            return True, []
            
        except Exception as e:
            logger.error(f"验证数据时发生错误: {e}")
            return False, [f"验证过程中发生错误: {str(e)}"]
    
    def get_schema(self, data_type: DataType) -> Dict[str, Any]:
        """获取指定数据类型的JSON模式"""
        return self.schemas.get(data_type, {})
    
    def get_all_schemas(self) -> Dict[DataType, Dict[str, Any]]:
        """获取所有JSON模式"""
        return self.schemas.copy()
    
    def export_schema(self, data_type: DataType, file_path: str) -> bool:
        """导出指定模式到文件"""
        try:
            schema = self.get_schema(data_type)
            if not schema:
                logger.error(f"未找到数据类型 {data_type} 的模式")
                return False
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(schema, f, indent=2, ensure_ascii=False)
            
            logger.info(f"模式已导出到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出模式失败: {e}")
            return False
    
    def export_all_schemas(self, directory: str) -> bool:
        """导出所有模式到指定目录"""
        try:
            import os
            os.makedirs(directory, exist_ok=True)
            
            success_count = 0
            for data_type in DataType:
                file_name = f"{data_type.value}_schema.json"
                file_path = os.path.join(directory, file_name)
                
                if self.export_schema(data_type, file_path):
                    success_count += 1
            
            logger.info(f"成功导出 {success_count}/{len(DataType)} 个模式")
            return success_count == len(DataType)
            
        except Exception as e:
            logger.error(f"批量导出模式失败: {e}")
            return False

class DataConverter:
    """数据格式转换器"""
    
    def __init__(self, schema_standards: JSONSchemaStandards):
        """初始化转换器"""
        self.schema_standards = schema_standards
    
    def convert_ocr_result_to_standard(self, ocr_result: Dict[str, Any]) -> Dict[str, Any]:
        """将OCR结果转换为标准格式"""
        try:
            # 确保必要字段存在
            standard_result = {
                "data_type": "ocr_result",
                "version": "1.0.0",
                "timestamp": datetime.now().isoformat(),
                "file_id": ocr_result.get("file_id", "unknown"),
                "file_path": ocr_result.get("file_path", ""),
                "image_info": {
                    "width": ocr_result.get("image_width", 1920),
                    "height": ocr_result.get("image_height", 1080),
                    "format": ocr_result.get("image_format", "JPEG"),
                    "dpi": ocr_result.get("dpi"),
                    "color_mode": ocr_result.get("color_mode")
                },
                "processing_info": {
                    "engine": ocr_result.get("engine", "unknown"),
                    "model_version": ocr_result.get("model_version", "unknown"),
                    "processing_time": ocr_result.get("processing_time", 0.0),
                    "parameters": ocr_result.get("parameters", {})
                },
                "text_regions": [],
                "statistics": {
                    "total_regions": 0,
                    "avg_confidence": 0.0,
                    "low_confidence_count": 0,
                    "detected_languages": []
                }
            }
            
            # 转换文本区域
            regions = ocr_result.get("regions", [])
            converted_regions = []
            total_confidence = 0.0
            low_confidence_count = 0
            languages = set()
            
            for i, region in enumerate(regions):
                # 转换边界框
                bbox_data = region.get("bounding_box", region.get("bbox", {}))
                if isinstance(bbox_data, list) and len(bbox_data) >= 4:
                    # 处理多边形格式
                    bbox = BoundingBox.from_polygon(bbox_data)
                else:
                    # 处理对象格式
                    bbox = BoundingBox(
                        x1=bbox_data.get("x1", bbox_data.get("x", 0)),
                        y1=bbox_data.get("y1", bbox_data.get("y", 0)),
                        x2=bbox_data.get("x2", bbox_data.get("x", 0) + bbox_data.get("width", 0)),
                        y2=bbox_data.get("y2", bbox_data.get("y", 0) + bbox_data.get("height", 0))
                    )
                
                confidence = region.get("confidence", 0.0)
                language = region.get("language")
                
                converted_region = {
                    "region_id": region.get("region_id", f"region_{i+1}"),
                    "text": region.get("text", ""),
                    "confidence": confidence,
                    "bounding_box": asdict(bbox),
                    "language": language,
                    "reading_direction": region.get("reading_direction", "ltr"),
                    "font_size": region.get("font_size"),
                    "font_family": region.get("font_family")
                }
                
                converted_regions.append(converted_region)
                total_confidence += confidence
                
                if confidence < 0.7:
                    low_confidence_count += 1
                
                if language:
                    languages.add(language)
            
            # 更新统计信息
            standard_result["text_regions"] = converted_regions
            standard_result["statistics"] = {
                "total_regions": len(converted_regions),
                "avg_confidence": total_confidence / len(converted_regions) if converted_regions else 0.0,
                "low_confidence_count": low_confidence_count,
                "detected_languages": list(languages)
            }
            
            return standard_result
            
        except Exception as e:
            logger.error(f"转换OCR结果失败: {e}")
            raise
    
    def convert_to_annotation_task(self, ocr_result: Dict[str, Any], config: Dict[str, Any] = None) -> Dict[str, Any]:
        """将OCR结果转换为注释任务格式"""
        try:
            if config is None:
                config = {
                    "interface_type": "web",
                    "confidence_threshold": 0.8,
                    "auto_review_enabled": True,
                    "batch_size": 10,
                    "task_priority": "medium"
                }
            
            task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            annotation_task = {
                "data_type": "annotation_task",
                "version": "1.0.0",
                "timestamp": datetime.now().isoformat(),
                "task_id": task_id,
                "file_id": ocr_result.get("file_id", "unknown"),
                "source_data": ocr_result,  # 添加源数据字段
                "annotation_config": config,
                "regions_to_annotate": [],
                "status": "pending",
                "progress": {
                    "total_regions": 0,
                    "completed_regions": 0,
                    "progress_percentage": 0.0
                }
            }
            
            # 转换区域为注释区域
            regions = ocr_result.get("text_regions", [])
            regions_to_annotate = []
            
            for region in regions:
                confidence = region.get("confidence", 0.0)
                requires_review = confidence < config.get("confidence_threshold", 0.8)
                
                annotation_region = {
                    "region_id": region.get("region_id"),
                    "original_text": region.get("text", ""),
                    "original_confidence": confidence,
                    "bounding_box": region.get("bounding_box"),
                    "requires_review": requires_review,
                    "annotation_type": "text_correction" if requires_review else "verification"
                }
                
                regions_to_annotate.append(annotation_region)
            
            annotation_task["regions_to_annotate"] = regions_to_annotate
            annotation_task["progress"]["total_regions"] = len(regions_to_annotate)
            
            return annotation_task
            
        except Exception as e:
            logger.error(f"转换为注释任务失败: {e}")
            raise

def create_json_schema_standards() -> JSONSchemaStandards:
    """创建JSON模式标准实例"""
    return JSONSchemaStandards()

def create_data_converter(schema_standards: JSONSchemaStandards = None) -> DataConverter:
    """创建数据转换器实例"""
    if schema_standards is None:
        schema_standards = create_json_schema_standards()
    return DataConverter(schema_standards)

# 导出主要类和函数
__all__ = [
    'DataType',
    'ConfidenceLevel', 
    'AnnotationStatus',
    'BoundingBox',
    'TextRegion',
    'CharacterResult',
    'LineResult',
    'JSONSchemaStandards',
    'DataConverter',
    'create_json_schema_standards',
    'create_data_converter'
] 
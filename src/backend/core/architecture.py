#!/usr/bin/env python3
"""
模块化架构系统
支持动态加载和管理不同的训练策略、模型类型和实验方法
"""

import os
import json
import logging
import importlib
import importlib.util
import inspect
from typing import Dict, List, Optional, Any, Type, Union, Callable
from pathlib import Path
from datetime import datetime
from abc import ABC, abstractmethod
from enum import Enum
import traceback

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ModuleType(Enum):
    """模块类型枚举"""
    TRAINING_STRATEGY = "training_strategy"
    MODEL_ARCHITECTURE = "model_architecture"
    DATA_PROCESSOR = "data_processor"
    EVALUATOR = "evaluator"
    OPTIMIZER = "optimizer"
    SCHEDULER = "scheduler"
    LOSS_FUNCTION = "loss_function"
    METRIC = "metric"
    AUGMENTATION = "augmentation"
    ENSEMBLE_METHOD = "ensemble_method"


class ModuleStatus(Enum):
    """模块状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    LOADING = "loading"
    ERROR = "error"
    DEPRECATED = "deprecated"


class BaseModule(ABC):
    """基础模块抽象类"""
    
    def __init__(self, name: str, version: str = "1.0.0", description: str = ""):
        self.name = name
        self.version = version
        self.description = description
        self.status = ModuleStatus.INACTIVE
        self.metadata = {}
        self.dependencies = []
        self.created_at = datetime.now()
        self.last_updated = datetime.now()
    
    @abstractmethod
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化模块"""
        pass
    
    @abstractmethod
    def execute(self, *args, **kwargs) -> Any:
        """执行模块主要功能"""
        pass
    
    @abstractmethod
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        pass
    
    def get_info(self) -> Dict[str, Any]:
        """获取模块信息"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "status": self.status.value,
            "metadata": self.metadata,
            "dependencies": self.dependencies,
            "created_at": self.created_at.isoformat(),
            "last_updated": self.last_updated.isoformat()
        }
    
    def update_status(self, status: ModuleStatus):
        """更新模块状态"""
        self.status = status
        self.last_updated = datetime.now()


class TrainingStrategyModule(BaseModule):
    """训练策略模块基类"""
    
    def __init__(self, name: str, version: str = "1.0.0", description: str = ""):
        super().__init__(name, version, description)
        self.module_type = ModuleType.TRAINING_STRATEGY
        self.supported_model_types = []
        self.required_data_format = {}
        self.hyperparameters = {}
    
    @abstractmethod
    def train(self, data, model, config: Dict[str, Any]) -> Dict[str, Any]:
        """执行训练"""
        pass
    
    @abstractmethod
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        pass


class ModelArchitectureModule(BaseModule):
    """模型架构模块基类"""
    
    def __init__(self, name: str, version: str = "1.0.0", description: str = ""):
        super().__init__(name, version, description)
        self.module_type = ModuleType.MODEL_ARCHITECTURE
        self.input_shape = None
        self.output_shape = None
        self.parameters_count = 0
    
    @abstractmethod
    def build_model(self, config: Dict[str, Any]) -> Any:
        """构建模型"""
        pass
    
    @abstractmethod
    def get_model_summary(self) -> Dict[str, Any]:
        """获取模型摘要"""
        pass


class DataProcessorModule(BaseModule):
    """数据处理模块基类"""
    
    def __init__(self, name: str, version: str = "1.0.0", description: str = ""):
        super().__init__(name, version, description)
        self.module_type = ModuleType.DATA_PROCESSOR
        self.supported_formats = []
        self.output_format = None
    
    @abstractmethod
    def process(self, data: Any, config: Dict[str, Any]) -> Any:
        """处理数据"""
        pass
    
    @abstractmethod
    def validate_data(self, data: Any) -> bool:
        """验证数据格式"""
        pass


class ModuleRegistry:
    """模块注册表"""
    
    def __init__(self):
        self.modules: Dict[str, BaseModule] = {}
        self.module_types: Dict[ModuleType, List[str]] = {
            module_type: [] for module_type in ModuleType
        }
        self.module_dependencies: Dict[str, List[str]] = {}
        self.logger = logging.getLogger(__name__)
    
    def register_module(self, module: BaseModule) -> bool:
        """注册模块"""
        try:
            module_id = f"{module.name}_{module.version}"
            
            # 检查模块是否已存在
            if module_id in self.modules:
                self.logger.warning(f"模块 {module_id} 已存在，将被覆盖")
            
            # 注册模块
            self.modules[module_id] = module
            
            # 按类型分类
            if hasattr(module, 'module_type'):
                if module_id not in self.module_types[module.module_type]:
                    self.module_types[module.module_type].append(module_id)
            
            # 记录依赖关系
            self.module_dependencies[module_id] = module.dependencies.copy()
            
            self.logger.info(f"成功注册模块: {module_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"注册模块失败: {e}")
            return False
    
    def unregister_module(self, module_id: str) -> bool:
        """注销模块"""
        try:
            if module_id not in self.modules:
                self.logger.warning(f"模块 {module_id} 不存在")
                return False
            
            module = self.modules[module_id]
            
            # 从类型分类中移除
            if hasattr(module, 'module_type'):
                if module_id in self.module_types[module.module_type]:
                    self.module_types[module.module_type].remove(module_id)
            
            # 移除依赖关系
            if module_id in self.module_dependencies:
                del self.module_dependencies[module_id]
            
            # 移除模块
            del self.modules[module_id]
            
            self.logger.info(f"成功注销模块: {module_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"注销模块失败: {e}")
            return False
    
    def get_module(self, module_id: str) -> Optional[BaseModule]:
        """获取模块"""
        return self.modules.get(module_id)
    
    def get_modules_by_type(self, module_type: ModuleType) -> List[BaseModule]:
        """按类型获取模块"""
        module_ids = self.module_types.get(module_type, [])
        return [self.modules[module_id] for module_id in module_ids if module_id in self.modules]
    
    def list_modules(self, status_filter: Optional[ModuleStatus] = None) -> List[Dict[str, Any]]:
        """列出所有模块"""
        modules_info = []
        for module_id, module in self.modules.items():
            if status_filter is None or module.status == status_filter:
                info = module.get_info()
                info['module_id'] = module_id
                modules_info.append(info)
        return modules_info
    
    def validate_dependencies(self, module_id: str) -> bool:
        """验证模块依赖"""
        if module_id not in self.module_dependencies:
            return True
        
        dependencies = self.module_dependencies[module_id]
        for dep in dependencies:
            if dep not in self.modules:
                self.logger.error(f"模块 {module_id} 的依赖 {dep} 不存在")
                return False
            
            if self.modules[dep].status != ModuleStatus.ACTIVE:
                self.logger.error(f"模块 {module_id} 的依赖 {dep} 状态不正确")
                return False
        
        return True


class ModuleLoader:
    """模块加载器"""
    
    def __init__(self, registry: ModuleRegistry):
        self.registry = registry
        self.logger = logging.getLogger(__name__)
        self.module_paths = []
    
    def add_module_path(self, path: str):
        """添加模块搜索路径"""
        if path not in self.module_paths:
            self.module_paths.append(path)
            self.logger.info(f"添加模块路径: {path}")
    
    def load_module_from_file(self, file_path: str, module_class_name: str) -> Optional[BaseModule]:
        """从文件加载模块"""
        try:
            # 获取模块规范
            spec = importlib.util.spec_from_file_location("dynamic_module", file_path)
            if spec is None:
                self.logger.error(f"无法加载模块规范: {file_path}")
                return None
            
            # 加载模块
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 获取模块类
            if not hasattr(module, module_class_name):
                self.logger.error(f"模块文件中未找到类: {module_class_name}")
                return None
            
            module_class = getattr(module, module_class_name)
            
            # 验证是否继承自BaseModule
            if not issubclass(module_class, BaseModule):
                self.logger.error(f"模块类必须继承自BaseModule: {module_class_name}")
                return None
            
            # 实例化模块
            module_instance = module_class()
            
            self.logger.info(f"成功加载模块: {module_class_name} from {file_path}")
            return module_instance
            
        except Exception as e:
            self.logger.error(f"加载模块失败: {e}")
            traceback.print_exc()
            return None
    
    def discover_modules(self, directory: str) -> List[BaseModule]:
        """发现目录中的模块"""
        discovered_modules = []
        
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file.endswith('.py') and not file.startswith('__'):
                        file_path = os.path.join(root, file)
                        
                        # 尝试加载模块并查找BaseModule子类
                        try:
                            spec = importlib.util.spec_from_file_location("temp_module", file_path)
                            if spec is None:
                                continue
                            
                            temp_module = importlib.util.module_from_spec(spec)
                            spec.loader.exec_module(temp_module)
                            
                            # 查找BaseModule子类
                            for name, obj in inspect.getmembers(temp_module):
                                if (inspect.isclass(obj) and 
                                    issubclass(obj, BaseModule) and 
                                    obj != BaseModule):
                                    
                                    try:
                                        module_instance = obj()
                                        discovered_modules.append(module_instance)
                                        self.logger.info(f"发现模块: {name} in {file_path}")
                                    except Exception as e:
                                        self.logger.warning(f"实例化模块失败 {name}: {e}")
                        
                        except Exception as e:
                            self.logger.debug(f"跳过文件 {file_path}: {e}")
                            continue
        
        except Exception as e:
            self.logger.error(f"发现模块失败: {e}")
        
        return discovered_modules
    
    def auto_register_modules(self, directory: str) -> int:
        """自动注册目录中的模块"""
        discovered_modules = self.discover_modules(directory)
        registered_count = 0
        
        for module in discovered_modules:
            if self.registry.register_module(module):
                registered_count += 1
        
        self.logger.info(f"自动注册了 {registered_count} 个模块")
        return registered_count


class ModularArchitecture:
    """模块化架构主类"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.registry = ModuleRegistry()
        self.loader = ModuleLoader(self.registry)
        self.config_path = config_path or "modular_architecture_config.json"
        self.logger = logging.getLogger(__name__)
        
        # 默认模块路径
        self.default_module_paths = [
            "src/modules/training_strategies",
            "src/modules/model_architectures", 
            "src/modules/data_processors",
            "src/modules/evaluators",
            "src/modules/ensemble_methods"
        ]
        
        # 添加默认模块路径
        for path in self.default_module_paths:
            self.loader.add_module_path(path)
        
        # 加载配置
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                # 加载模块路径
                if 'module_paths' in config:
                    for path in config['module_paths']:
                        self.loader.add_module_path(path)
                
                self.logger.info("配置加载成功")
            else:
                self.logger.info("配置文件不存在，使用默认配置")
                self.save_config()
                
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
    
    def save_config(self):
        """保存配置"""
        try:
            config = {
                "module_paths": self.loader.module_paths,
                "default_modules": self.get_active_modules_info()
            }
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
                
            self.logger.info("配置保存成功")
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
    
    def initialize_default_modules(self):
        """初始化默认模块"""
        # 创建默认模块目录
        for path in self.default_module_paths:
            os.makedirs(path, exist_ok=True)
        
        # 自动发现并注册模块
        total_registered = 0
        for path in self.default_module_paths:
            if os.path.exists(path):
                count = self.loader.auto_register_modules(path)
                total_registered += count
        
        self.logger.info(f"初始化完成，共注册 {total_registered} 个模块")
        return total_registered
    
    def get_active_modules_info(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取活跃模块信息"""
        active_modules = {}
        for module_type in ModuleType:
            modules = self.registry.get_modules_by_type(module_type)
            active_modules[module_type.value] = [
                module.get_info() for module in modules 
                if module.status == ModuleStatus.ACTIVE
            ]
        return active_modules
    
    def create_training_pipeline(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """创建训练管道"""
        try:
            pipeline_config = {
                "strategy": None,
                "model": None,
                "data_processor": None,
                "evaluator": None,
                "ensemble_method": None
            }
            
            # 获取训练策略
            if 'training_strategy' in config:
                strategy_id = config['training_strategy']
                strategy = self.registry.get_module(strategy_id)
                if strategy and strategy.status == ModuleStatus.ACTIVE:
                    pipeline_config["strategy"] = strategy
            
            # 获取模型架构
            if 'model_architecture' in config:
                model_id = config['model_architecture']
                model = self.registry.get_module(model_id)
                if model and model.status == ModuleStatus.ACTIVE:
                    pipeline_config["model"] = model
            
            # 获取数据处理器
            if 'data_processor' in config:
                processor_id = config['data_processor']
                processor = self.registry.get_module(processor_id)
                if processor and processor.status == ModuleStatus.ACTIVE:
                    pipeline_config["data_processor"] = processor
            
            # 获取评估器
            if 'evaluator' in config:
                evaluator_id = config['evaluator']
                evaluator = self.registry.get_module(evaluator_id)
                if evaluator and evaluator.status == ModuleStatus.ACTIVE:
                    pipeline_config["evaluator"] = evaluator
            
            # 获取集成方法
            if 'ensemble_method' in config:
                ensemble_id = config['ensemble_method']
                ensemble = self.registry.get_module(ensemble_id)
                if ensemble and ensemble.status == ModuleStatus.ACTIVE:
                    pipeline_config["ensemble_method"] = ensemble
            
            return {
                "success": True,
                "pipeline": pipeline_config,
                "message": "训练管道创建成功"
            }
            
        except Exception as e:
            self.logger.error(f"创建训练管道失败: {e}")
            return {
                "success": False,
                "pipeline": None,
                "message": f"创建失败: {e}"
            }
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        total_modules = len(self.registry.modules)
        active_modules = len([m for m in self.registry.modules.values() 
                             if m.status == ModuleStatus.ACTIVE])
        
        status_by_type = {}
        for module_type in ModuleType:
            modules = self.registry.get_modules_by_type(module_type)
            status_by_type[module_type.value] = {
                "total": len(modules),
                "active": len([m for m in modules if m.status == ModuleStatus.ACTIVE]),
                "inactive": len([m for m in modules if m.status == ModuleStatus.INACTIVE]),
                "error": len([m for m in modules if m.status == ModuleStatus.ERROR])
            }
        
        return {
            "total_modules": total_modules,
            "active_modules": active_modules,
            "module_paths": self.loader.module_paths,
            "status_by_type": status_by_type,
            "config_path": self.config_path
        }


# 全局实例
_modular_architecture = None

def get_modular_architecture() -> ModularArchitecture:
    """获取全局模块化架构实例"""
    global _modular_architecture
    if _modular_architecture is None:
        _modular_architecture = ModularArchitecture()
    return _modular_architecture


if __name__ == "__main__":
    # 测试模块化架构
    arch = ModularArchitecture()
    
    # 初始化默认模块
    arch.initialize_default_modules()
    
    # 获取系统状态
    status = arch.get_system_status()
    print("系统状态:", json.dumps(status, indent=2, ensure_ascii=False))
    
    # 保存配置
    arch.save_config()
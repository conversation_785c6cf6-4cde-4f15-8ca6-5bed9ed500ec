from fastapi import HTTPException, Request
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import logging
from typing import Any, Dict, Optional

class OCRException(HTTPException):
    """OCR 处理异常基类"""
    def __init__(
        self,
        status_code: int,
        detail: Any = None,
        headers: Optional[Dict[str, str]] = None,
        error_code: str = "OCR_ERROR"
    ):
        super().__init__(status_code=status_code, detail=detail)
        self.headers = headers
        self.error_code = error_code

class FileUploadError(OCRException):
    """文件上传错误"""
    def __init__(self, detail: str = "文件上传失败"):
        super().__init__(
            status_code=400,
            detail=detail,
            error_code="FILE_UPLOAD_ERROR"
        )

class FileTypeError(OCRException):
    """文件类型错误"""
    def __init__(self, detail: str = "不支持的文件类型"):
        super().__init__(
            status_code=400,
            detail=detail,
            error_code="FILE_TYPE_ERROR"
        )

class FileSizeError(OCRException):
    """文件大小错误"""
    def __init__(self, detail: str = "文件大小超过限制"):
        super().__init__(
            status_code=400,
            detail=detail,
            error_code="FILE_SIZE_ERROR"
        )

class OCRProcessError(OCRException):
    """OCR 处理错误"""
    def __init__(self, detail: str = "OCR 处理失败"):
        super().__init__(
            status_code=500,
            detail=detail,
            error_code="OCR_PROCESS_ERROR"
        )

async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """HTTP 异常处理器"""
    status_code = exc.status_code
    detail = str(exc.detail)
    error_code = getattr(exc, "error_code", "HTTP_ERROR")
    
    logging.error(f"HTTP 错误 - 状态码: {status_code} - 错误码: {error_code} - 详情: {detail}")
    
    return JSONResponse(
        status_code=status_code,
        content={
            "error": True,
            "code": error_code,
            "message": detail,
            "request_id": getattr(request.state, "request_id", None)
        }
    )

async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理器"""
    status_code = 422
    detail = exc.errors()
    
    logging.error(f"验证错误 - 详情: {detail}")
    
    return JSONResponse(
        status_code=status_code,
        content={
            "error": True,
            "code": "VALIDATION_ERROR",
            "message": "请求参数验证失败",
            "detail": detail,
            "request_id": getattr(request.state, "request_id", None)
        }
    )

async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    status_code = 500
    detail = str(exc)
    
    logging.exception("未处理的异常")
    
    return JSONResponse(
        status_code=status_code,
        content={
            "error": True,
            "code": "INTERNAL_ERROR",
            "message": "服务器内部错误",
            "detail": detail if request.app.debug else None,
            "request_id": getattr(request.state, "request_id", None)
        }
    ) 
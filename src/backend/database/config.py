"""
数据库配置文件
"""

import os
from typing import Dict, Any, Optional
from pydantic_settings import BaseSettings
from functools import lru_cache

class DatabaseSettings(BaseSettings):
    """数据库配置类"""
    
    # MongoDB 配置
    MONGODB_URL: str = "mongodb://localhost:55142/"
    MONGODB_DATABASE: str = "agentdb_test"
    MONGODB_USERNAME: Optional[str] = None
    MONGODB_PASSWORD: Optional[str] = None
    
    # 连接池配置
    MONGODB_MAX_POOL_SIZE: int = 100
    MONGODB_MIN_POOL_SIZE: int = 10
    MONGODB_MAX_IDLE_TIME_MS: int = 30000
    MONGODB_CONNECT_TIMEOUT_MS: int = 10000
    MONGODB_SERVER_SELECTION_TIMEOUT_MS: int = 5000
    
    # 集合名称配置
    OCR_FILES_COLLECTION: str = "ocr_files"
    OCR_RESULTS_COLLECTION: str = "ocr_results"
    OCR_ANNOTATIONS_COLLECTION: str = "ocr_annotations"
    OCR_TRAINING_SAMPLES_COLLECTION: str = "ocr_training_samples"
    OCR_PREPROCESSING_EFFECTIVENESS_COLLECTION: str = "ocr_preprocessing_effectiveness"
    OCR_ERROR_PATTERNS_COLLECTION: str = "ocr_error_patterns"
    OCR_LEARNING_REPORTS_COLLECTION: str = "ocr_learning_reports"
    
    # Redis配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    
    # MinIO配置
    MINIO_ENDPOINT: str = "localhost:9000"
    MINIO_ACCESS_KEY: str = "minioadmin"
    MINIO_SECRET_KEY: str = "minioadmin"
    MINIO_BUCKET: str = "sake"
    MINIO_SECURE: bool = False
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    def get_mongodb_url(self) -> str:
        """获取完整的MongoDB连接URL"""
        if self.MONGODB_USERNAME and self.MONGODB_PASSWORD:
            # 包含认证信息的URL
            base_url = self.MONGODB_URL.replace("mongodb://", "")
            return f"mongodb://{self.MONGODB_USERNAME}:{self.MONGODB_PASSWORD}@{base_url}"
        return self.MONGODB_URL

# 全局数据库设置实例
settings = DatabaseSettings()

@lru_cache()
def get_config() -> DatabaseSettings:
    """获取配置实例（带缓存）"""
    return DatabaseSettings()

def get_mongodb_settings() -> Dict[str, Any]:
    """获取MongoDB配置"""
    return {
        "uri": settings.get_mongodb_url(),
        "database": settings.MONGODB_DATABASE
    }

def get_redis_settings() -> Dict[str, Any]:
    """获取Redis配置"""
    return {
        "host": settings.REDIS_HOST,
        "port": settings.REDIS_PORT,
        "db": settings.REDIS_DB,
        "password": settings.REDIS_PASSWORD
    }

def get_minio_settings() -> Dict[str, Any]:
    """获取MinIO配置"""
    return {
        "endpoint": settings.MINIO_ENDPOINT,
        "access_key": settings.MINIO_ACCESS_KEY,
        "secret_key": settings.MINIO_SECRET_KEY,
        "bucket": settings.MINIO_BUCKET,
        "secure": settings.MINIO_SECURE
    } 
from motor.motor_asyncio import AsyncIOMotorClient
from typing import Optional
import logging
from src.backend.config import settings

logger = logging.getLogger(__name__)

_db = None

async def get_database():
    """获取MongoDB数据库连接"""
    global _db
    if _db is None:
        try:
            client = AsyncIOMotorClient(settings.MONGODB_URL)
            _db = client[settings.MONGODB_DATABASE]
            logger.info(f"Successfully connected to MongoDB: {settings.MONGODB_URL}")
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {str(e)}")
            raise
    return _db

async def close_database():
    """关闭MongoDB连接"""
    global _db
    if _db is not None:
        try:
            _db.client.close()
            _db = None
            logger.info("MongoDB connection closed")
        except Exception as e:
            logger.error(f"Error closing MongoDB connection: {str(e)}")
            raise 
"""
共享数据库连接模块
提供MongoDB、Redis等数据库的连接管理
"""

from typing import Optional
from motor.motor_asyncio import AsyncIOMotorClient
from redis import Redis
from redis.asyncio import Redis as AsyncRedis
from minio import Minio
from .config import settings, get_mongodb_settings, get_redis_settings, get_minio_settings
from src.backend.logger import get_logger

logger = get_logger("database")

class Database:
    """数据库连接管理类"""
    
    _mongodb_client: Optional[AsyncIOMotorClient] = None
    _redis_client: Optional[Redis] = None
    _async_redis_client: Optional[AsyncRedis] = None
    _minio_client: Optional[Minio] = None
    
    @classmethod
    async def get_mongodb(cls) -> AsyncIOMotorClient:
        """获取MongoDB客户端连接"""
        if cls._mongodb_client is None:
            mongodb_settings = get_mongodb_settings()
            try:
                cls._mongodb_client = AsyncIOMotorClient(mongodb_settings["uri"])
                # 测试连接
                await cls._mongodb_client.admin.command('ping')
                logger.info("Successfully connected to MongoDB")
            except Exception as e:
                logger.error(f"Failed to connect to MongoDB: {str(e)}")
                raise
        return cls._mongodb_client
    
    @classmethod
    def get_redis(cls) -> Redis:
        """获取Redis客户端连接（同步）"""
        if cls._redis_client is None:
            redis_settings = get_redis_settings()
            try:
                cls._redis_client = Redis(
                    host=redis_settings["host"],
                    port=redis_settings["port"],
                    db=redis_settings["db"],
                    password=redis_settings["password"],
                    decode_responses=True
                )
                # 测试连接
                cls._redis_client.ping()
                logger.info("Successfully connected to Redis")
            except Exception as e:
                logger.error(f"Failed to connect to Redis: {str(e)}")
                raise
        return cls._redis_client
    
    @classmethod
    async def get_async_redis(cls) -> AsyncRedis:
        """获取Redis客户端连接（异步）"""
        if cls._async_redis_client is None:
            redis_settings = get_redis_settings()
            try:
                cls._async_redis_client = AsyncRedis(
                    host=redis_settings["host"],
                    port=redis_settings["port"],
                    db=redis_settings["db"],
                    password=redis_settings["password"],
                    decode_responses=True
                )
                # 测试连接
                await cls._async_redis_client.ping()
                logger.info("Successfully connected to Redis (async)")
            except Exception as e:
                logger.error(f"Failed to connect to Redis (async): {str(e)}")
                raise
        return cls._async_redis_client
    
    @classmethod
    def get_minio(cls) -> Minio:
        """获取MinIO客户端连接"""
        if cls._minio_client is None:
            minio_settings = get_minio_settings()
            try:
                cls._minio_client = Minio(
                    minio_settings["endpoint"],
                    access_key=minio_settings["access_key"],
                    secret_key=minio_settings["secret_key"],
                    secure=False  # 开发环境使用HTTP
                )
                # 确保bucket存在
                if not cls._minio_client.bucket_exists(minio_settings["bucket"]):
                    cls._minio_client.make_bucket(minio_settings["bucket"])
                logger.info("Successfully connected to MinIO")
            except Exception as e:
                logger.error(f"Failed to connect to MinIO: {str(e)}")
                raise
        return cls._minio_client
    
    @classmethod
    async def close_connections(cls):
        """关闭所有数据库连接"""
        if cls._mongodb_client:
            cls._mongodb_client.close()
            logger.info("Closed MongoDB connection")
        
        if cls._redis_client:
            cls._redis_client.close()
            logger.info("Closed Redis connection")
        
        if cls._async_redis_client:
            await cls._async_redis_client.close()
            logger.info("Closed Redis (async) connection")
        
        # MinIO客户端不需要显式关闭

# 数据库依赖项
async def get_mongodb():
    """MongoDB依赖项"""
    try:
        client = await Database.get_mongodb()
        db = client[settings.MONGODB_DATABASE]
        logger.info(f"Connected to MongoDB database: {settings.MONGODB_DATABASE}")
        return db
    except Exception as e:
        logger.error(f"Failed to get MongoDB database: {str(e)}")
        raise

async def get_redis():
    """Redis依赖项（同步）"""
    try:
        client = Database.get_redis()
        yield client
    finally:
        pass

async def get_async_redis():
    """Redis依赖项（异步）"""
    try:
        client = await Database.get_async_redis()
        yield client
    finally:
        pass

def get_minio():
    """MinIO依赖项"""
    try:
        client = Database.get_minio()
        yield client
    finally:
        pass 
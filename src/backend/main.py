from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import logging

import sys
from pathlib import Path

# 添加当前目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from src.backend.config import settings
from src.backend.core.errors import (
    http_exception_handler,
    validation_exception_handler,
    general_exception_handler
)
from src.backend.middleware.middleware import request_middleware

# 导入路由模块
from src.backend.routers.ocr import router as ocr_router
from src.backend.routers.annotation_api import router as annotation_router
from src.backend.routers.document_conversion import router as conversion_router
from src.backend.routers.document_output import router as output_router
from src.backend.routers.document_style import router as style_router
from src.backend.routers.markdown_conversion import router as markdown_router
from src.backend.routers.monitoring_api import router as monitoring_router
from src.backend.routers.training_api import router as training_router
from src.backend.routers.analysis_api import router as analysis_router
from src.backend.routers.model_optimization_api import router as optimization_router
from src.backend.routers.load_testing_api import router as load_testing_router
from src.backend.routers.integration_api import router as integration_router
from src.backend.routers.file_processing_api import router as file_processing_router
from src.backend.routers.db_optimization_api import router as db_optimization_router
from .routers import ocr_label
from .database import connect_to_mongo, close_mongo_connection

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建应用
app = FastAPI(
    title="OCR标注服务",
    version=settings.APP_VERSION,
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加中间件
@app.middleware("http")
async def add_middleware(request: Request, call_next):
    return await request_middleware(request, call_next)

# 注册异常处理器
app.add_exception_handler(StarletteHTTPException, http_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# 注册路由
app.include_router(ocr_router, prefix=settings.API_PREFIX)
app.include_router(annotation_router, prefix=settings.API_PREFIX)
app.include_router(conversion_router, prefix=settings.API_PREFIX)
app.include_router(output_router, prefix=settings.API_PREFIX)
app.include_router(style_router, prefix=settings.API_PREFIX)
app.include_router(markdown_router, prefix=settings.API_PREFIX)
app.include_router(monitoring_router, prefix=settings.API_PREFIX)
app.include_router(training_router, prefix=settings.API_PREFIX)
app.include_router(analysis_router, prefix=settings.API_PREFIX)
app.include_router(optimization_router, prefix=settings.API_PREFIX)
app.include_router(load_testing_router, prefix=settings.API_PREFIX)
app.include_router(integration_router, prefix=settings.API_PREFIX)
app.include_router(file_processing_router, prefix=settings.API_PREFIX)
app.include_router(db_optimization_router, prefix=settings.API_PREFIX)
app.include_router(ocr_label.router)

# 创建必要的目录
for dir_path in [
    settings.DATA_DIR,
    settings.UPLOAD_DIR,
    settings.OUTPUT_DIR,
    settings.TASK_DIR,
    settings.MODEL_DIR,
    settings.PROCESSED_DIR
]:
    dir_path.mkdir(parents=True, exist_ok=True)

# 数据库事件处理
@app.on_event("startup")
async def startup_db_client():
    """数据库初始化"""
    try:
        # 运行数据库初始化脚本
        from src.backend.scripts.init_mongodb import init_mongodb
        await init_mongodb()
        
        # 连接数据库
        await connect_to_mongo()
        
        # 初始化文件服务
        file_service = FileProcessingService()
        await file_service.initialize_db()
        
        logger.info("数据库服务初始化成功")
    except Exception as e:
        logger.error(f"数据库服务初始化失败: {str(e)}")
        raise

@app.on_event("shutdown")
async def shutdown_db_client():
    """关闭数据库连接"""
    try:
        await close_mongo_connection()
        logger.info("数据库连接已关闭")
    except Exception as e:
        logger.error(f"关闭数据库连接失败: {str(e)}")

# 根路由
@app.get("/")
async def root():
    """API根路径"""
    return {
        "message": "OCR标注服务已启动",
        "version": settings.APP_VERSION,
        "docs_url": "/docs"
    }

# 健康检查
@app.get("/health")
async def health():
    """健康检查"""
    return {
        "status": "healthy",
        "version": settings.APP_VERSION
    } 
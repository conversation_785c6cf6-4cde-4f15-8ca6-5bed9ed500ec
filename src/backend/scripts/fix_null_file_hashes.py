"""
修复数据库中file_hash为null的记录
"""

import asyncio
from datetime import datetime
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo import UpdateOne
import logging
import hashlib
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# MongoDB连接配置
MONGODB_URL = "mongodb://localhost:27017"
DATABASE_NAME = "agentdb_test"
COLLECTION_NAME = "ocr_files"

async def fix_null_file_hashes():
    """修复file_hash为null的记录"""
    try:
        # 连接MongoDB
        client = AsyncIOMotorClient(MONGODB_URL)
        db = client[DATABASE_NAME]
        collection = db[COLLECTION_NAME]
        
        # 查找file_hash为null的记录
        cursor = collection.find({"file_hash": None})
        updates = []
        
        async for doc in cursor:
            try:
                # 读取文件内容并计算哈希值
                file_path = Path(doc["path"])
                if file_path.exists():
                    with open(file_path, "rb") as f:
                        content = f.read()
                        file_hash = hashlib.sha256(content).hexdigest()
                else:
                    # 如果文件不存在，生成一个基于文件名和大小的哈希值
                    content = f"{doc['filename']}_{doc.get('file_size', 0)}".encode()
                    file_hash = hashlib.sha256(content).hexdigest()
                
                logger.info("准备更新记录 %s, 新file_hash: %s", doc["_id"], file_hash)
                
                # 创建更新操作
                update = UpdateOne(
                    {"_id": doc["_id"]},
                    {
                        "$set": {
                            "file_hash": file_hash,
                            "updated_at": datetime.utcnow()
                        }
                    }
                )
                updates.append(update)
                
            except Exception as e:
                logger.error("处理文件 %s 时出错: %s", doc.get("filename", "unknown"), str(e))
                continue
        
        if updates:
            # 批量执行更新
            result = await collection.bulk_write(updates)
            logger.info("成功更新 %d 条记录", result.modified_count)
        else:
            logger.info("没有需要更新的记录")
            
    except Exception as e:
        logger.error("修复过程出错: %s", str(e))
    finally:
        client.close()

if __name__ == "__main__":
    asyncio.run(fix_null_file_hashes()) 
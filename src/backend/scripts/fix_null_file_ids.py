"""
修复数据库中file_id为null的记录
"""

import asyncio
from datetime import datetime
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo import UpdateOne
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# MongoDB连接配置
MONGODB_URL = "mongodb://localhost:27017"
DATABASE_NAME = "agentdb_test"
COLLECTION_NAME = "ocr_files"

async def fix_null_file_ids():
    """修复file_id为null的记录"""
    try:
        # 连接MongoDB
        client = AsyncIOMotorClient(MONGODB_URL)
        db = client[DATABASE_NAME]
        collection = db[COLLECTION_NAME]
        
        # 查找file_id为null的记录
        cursor = collection.find({"file_id": None})
        updates = []
        
        async for doc in cursor:
            # 为每个记录生成新的file_id
            new_file_id = str(ObjectId())
            
            # 创建更新操作
            update = UpdateOne(
                {"_id": doc["_id"]},
                {
                    "$set": {
                        "file_id": new_file_id,
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            updates.append(update)
            logger.info(f"准备更新记录 {doc['_id']}, 新file_id: {new_file_id}")
        
        if updates:
            # 批量执行更新
            result = await collection.bulk_write(updates)
            logger.info(f"成功更新 {result.modified_count} 条记录")
        else:
            logger.info("没有找到需要修复的记录")
            
    except Exception as e:
        logger.error(f"修复file_id失败: {str(e)}", exc_info=True)
        raise
    finally:
        # 关闭数据库连接
        client.close()

if __name__ == "__main__":
    # 运行修复脚本
    asyncio.run(fix_null_file_ids()) 
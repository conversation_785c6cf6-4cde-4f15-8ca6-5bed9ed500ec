#!/usr/bin/env python3
"""
MongoDB初始化脚本
用于创建集合和索引，并更新历史文档的删除状态
"""

from pymongo import MongoClient, ASCENDING
from pymongo.errors import CollectionInvalid
import sys
import os
from datetime import datetime
import uuid
import hashlib

# 添加父目录到系统路径以导入config
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from src.backend.config import settings

def generate_file_hash(filename: str) -> str:
    """生成文件的唯一哈希值"""
    return hashlib.md5(f"{filename}-{uuid.uuid4()}".encode()).hexdigest()

def init_mongodb():
    """初始化MongoDB数据库"""
    try:
        # 连接MongoDB
        client = MongoClient(settings.get_mongodb_url())
        db = client[settings.MONGODB_DATABASE]

        try:
            # 创建ocr_files集合
            db.create_collection(settings.COLLECTION_OCR_FILES)
        except CollectionInvalid:
            print(f"{settings.COLLECTION_OCR_FILES}集合已存在")

        # 获取ocr_files集合
        collection = db[settings.COLLECTION_OCR_FILES]

        # 清空现有数据
        collection.delete_many({})

        # 插入测试数据
        test_data = [
            {
                "file_id": str(uuid.uuid4()),
                "file_hash": generate_file_hash("test1.pdf"),
                "filename": "test1.pdf",
                "original_filename": "test1.pdf",
                "file_path": "/uploads/test1.pdf",
                "file_size": 1024,
                "upload_time": datetime.now(),
                "status": "completed",
                "deleted": False,
                "deletedAt": None,
                "ocr_text": "测试文档1的内容",
                "processing_status": "completed"
            },
            {
                "file_id": str(uuid.uuid4()),
                "file_hash": generate_file_hash("test2.pdf"),
                "filename": "test2.pdf",
                "original_filename": "test2.pdf",
                "file_path": "/uploads/test2.pdf",
                "file_size": 2048,
                "upload_time": datetime.now(),
                "status": "completed",
                "deleted": False,
                "deletedAt": None,
                "ocr_text": "测试文档2的内容",
                "processing_status": "completed"
            },
            {
                "file_id": str(uuid.uuid4()),
                "file_hash": generate_file_hash("test3.pdf"),
                "filename": "test3.pdf",
                "original_filename": "test3.pdf",
                "file_path": "/uploads/test3.pdf",
                "file_size": 3072,
                "upload_time": datetime.now(),
                "status": "completed",
                "deleted": False,
                "deletedAt": None,
                "ocr_text": "测试文档3的内容",
                "processing_status": "completed"
            }
        ]
        
        collection.insert_many(test_data)
        print(f"已插入 {len(test_data)} 条测试数据")

        # 创建索引
        collection.create_index([("deleted", ASCENDING)])
        collection.create_index([("upload_time", ASCENDING)])
        collection.create_index([("file_id", ASCENDING)], unique=True)
        collection.create_index([("file_hash", ASCENDING)], unique=True)
        print("索引创建完成")

    except Exception as e:
        print(f"初始化MongoDB时出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    init_mongodb() 
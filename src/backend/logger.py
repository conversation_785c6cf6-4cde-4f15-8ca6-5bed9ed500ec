"""
日志配置模块
提供统一的日志记录功能
"""

import logging
import logging.handlers
import os
from pathlib import Path
from typing import Optional
import sys
import json
from datetime import datetime

from src.backend.config import settings

# 日志格式
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

# 日志目录
LOG_DIR = settings.PROJECT_ROOT / "logs"
if not LOG_DIR.exists():
    LOG_DIR.mkdir(parents=True)

def get_logger(name: str) -> logging.Logger:
    """
    获取配置好的日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    logger = logging.getLogger(name)
    
    # 如果已经有处理器，说明已经配置过，直接返回
    if logger.handlers:
        return logger
        
    logger.setLevel(logging.INFO)
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    file_handler = logging.handlers.RotatingFileHandler(
        log_dir / f"{name}.log",
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    def log_request_response(func):
        """记录请求和响应的装饰器"""
        def wrapper(*args, **kwargs):
            # 记录请求
            request_data = {
                "args": args,
                "kwargs": kwargs,
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"Request: {json.dumps(request_data, default=str)}")
            
            try:
                # 执行函数
                result = func(*args, **kwargs)
                # 记录响应
                logger.info(f"Response: {json.dumps(result, default=str)}")
                return result
            except Exception as e:
                # 记录错误
                logger.error(f"Error: {str(e)}", exc_info=True)
                raise
                
        return wrapper
    
    # 添加装饰器到logger
    logger.log_request_response = log_request_response
    
    return logger 
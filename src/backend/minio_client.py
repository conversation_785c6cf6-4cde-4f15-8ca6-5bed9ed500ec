"""
MinIO客户端封装模块
提供MinIO对象存储的统一接口
"""

import io
from typing import Optional, Dict, Any, List
from minio import Minio
from minio.error import S3Error
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.backend.config import settings
from src.backend.logger import get_logger

logger = get_logger("minio")

class MinioClient:
    """MinIO客户端封装类"""
    
    _instance: Optional["MinioClient"] = None
    _client: Optional[Minio] = None
    
    def __new__(cls) -> "MinioClient":
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化MinIO客户端"""
        if self._client is None:
            try:
                self._client = Minio(
                    settings.MINIO_ENDPOINT,
                    access_key=settings.MINIO_ACCESS_KEY,
                    secret_key=settings.MINIO_SECRET_KEY,
                    secure=False  # 开发环境使用HTTP
                )
                # 确保bucket存在
                if not self._client.bucket_exists(settings.MINIO_BUCKET):
                    self._client.make_bucket(settings.MINIO_BUCKET)
                logger.info("Successfully connected to MinIO")
            except Exception as e:
                logger.error(f"Failed to connect to MinIO: {str(e)}")
                raise
    
    def upload_file(self, file_path: str, object_name: str) -> Dict[str, Any]:
        """上传文件到MinIO
        
        Args:
            file_path: 本地文件路径
            object_name: 对象名称
            
        Returns:
            Dict[str, Any]: 上传结果
        """
        try:
            result = self._client.fput_object(
                settings.MINIO_BUCKET,
                object_name,
                file_path
            )
            return {
                "bucket": result.bucket_name,
                "object": result.object_name,
                "version_id": result.version_id,
                "etag": result.etag
            }
        except S3Error as e:
            logger.error(f"Failed to upload file to MinIO: {str(e)}")
            raise
    
    def upload_bytes(self, data: bytes, object_name: str) -> Dict[str, Any]:
        """上传字节数据到MinIO
        
        Args:
            data: 字节数据
            object_name: 对象名称
            
        Returns:
            Dict[str, Any]: 上传结果
        """
        try:
            result = self._client.put_object(
                settings.MINIO_BUCKET,
                object_name,
                io.BytesIO(data),
                len(data)
            )
            return {
                "bucket": result.bucket_name,
                "object": result.object_name,
                "version_id": result.version_id,
                "etag": result.etag
            }
        except S3Error as e:
            logger.error(f"Failed to upload bytes to MinIO: {str(e)}")
            raise
    
    def download_file(self, object_name: str, file_path: str) -> None:
        """从MinIO下载文件
        
        Args:
            object_name: 对象名称
            file_path: 本地文件路径
        """
        try:
            self._client.fget_object(
                settings.MINIO_BUCKET,
                object_name,
                file_path
            )
        except S3Error as e:
            logger.error(f"Failed to download file from MinIO: {str(e)}")
            raise
    
    def download_bytes(self, object_name: str) -> bytes:
        """从MinIO下载字节数据
        
        Args:
            object_name: 对象名称
            
        Returns:
            bytes: 字节数据
        """
        try:
            data = self._client.get_object(
                settings.MINIO_BUCKET,
                object_name
            )
            return data.read()
        except S3Error as e:
            logger.error(f"Failed to download bytes from MinIO: {str(e)}")
            raise
        finally:
            if "data" in locals():
                data.close()
                data.release_conn()
    
    def list_objects(self, prefix: str = "") -> List[Dict[str, Any]]:
        """列出MinIO中的对象
        
        Args:
            prefix: 对象名称前缀
            
        Returns:
            List[Dict[str, Any]]: 对象列表
        """
        try:
            objects = []
            for obj in self._client.list_objects(settings.MINIO_BUCKET, prefix=prefix):
                objects.append({
                    "object_name": obj.object_name,
                    "size": obj.size,
                    "last_modified": obj.last_modified,
                    "etag": obj.etag
                })
            return objects
        except S3Error as e:
            logger.error(f"Failed to list objects in MinIO: {str(e)}")
            raise
    
    def delete_object(self, object_name: str) -> None:
        """删除MinIO中的对象
        
        Args:
            object_name: 对象名称
        """
        try:
            self._client.remove_object(settings.MINIO_BUCKET, object_name)
        except S3Error as e:
            logger.error(f"Failed to delete object from MinIO: {str(e)}")
            raise
    
    def get_object_url(self, object_name: str, expires: int = 3600) -> str:
        """获取对象的预签名URL
        
        Args:
            object_name: 对象名称
            expires: URL过期时间（秒）
            
        Returns:
            str: 预签名URL
        """
        try:
            return self._client.presigned_get_object(
                settings.MINIO_BUCKET,
                object_name,
                expires=expires
            )
        except S3Error as e:
            logger.error(f"Failed to get object URL from MinIO: {str(e)}")
            raise 
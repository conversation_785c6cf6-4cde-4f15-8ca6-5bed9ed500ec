"""
共享配置模块
提供所有微服务共用的配置项
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from pydantic_settings import BaseSettings
from functools import lru_cache
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Settings(BaseSettings):
    """基础配置类"""
    
    # 应用配置
    APP_NAME: str = "OCR智能文档处理系统"
    APP_VERSION: str = "1.0.0"
    API_PREFIX: str = "/api"
    DEBUG: bool = True
    
    # CORS配置
    CORS_ORIGINS: List[str] = ["*"]
    CORS_CREDENTIALS: bool = True
    
    # 服务配置
    SERVICE_NAME: str = "ocr-service"
    
    # 路径配置
    PROJECT_ROOT: Path = Path(__file__).parent.parent.parent
    DATA_DIR: Path = PROJECT_ROOT / "data"
    UPLOAD_DIR: Path = DATA_DIR / "uploads"
    OUTPUT_DIR: Path = DATA_DIR / "outputs"
    TASK_DIR: Path = DATA_DIR / "tasks"
    MODEL_DIR: Path = DATA_DIR / "models"
    PROCESSED_DIR: Path = DATA_DIR / "processed"
    TRAINING_WORK_DIR: Path = DATA_DIR / "training"
    
    # MongoDB配置
    MONGODB_URL: str = os.getenv("MONGODB_URL", "mongodb://localhost:55142")
    MONGODB_DATABASE: str = os.getenv("MONGODB_DATABASE", "agentdb_test")
    MONGODB_USERNAME: Optional[str] = os.getenv("MONGODB_USERNAME")
    MONGODB_PASSWORD: Optional[str] = os.getenv("MONGODB_PASSWORD")
    MONGODB_MAX_POOL_SIZE: int = int(os.getenv("MONGODB_MAX_POOL_SIZE", "100"))
    MONGODB_MIN_POOL_SIZE: int = int(os.getenv("MONGODB_MIN_POOL_SIZE", "10"))
    MONGODB_MAX_IDLE_TIME_MS: int = int(os.getenv("MONGODB_MAX_IDLE_TIME_MS", "30000"))
    MONGODB_CONNECT_TIMEOUT_MS: int = int(os.getenv("MONGODB_CONNECT_TIMEOUT_MS", "10000"))
    MONGODB_SERVER_SELECTION_TIMEOUT_MS: int = int(os.getenv("MONGODB_SERVER_SELECTION_TIMEOUT_MS", "5000"))
    
    # MongoDB集合名称
    COLLECTION_OCR_FILES: str = os.getenv("COLLECTION_OCR_FILES", "ocr_files")
    COLLECTION_OCR_RESULTS: str = os.getenv("COLLECTION_OCR_RESULTS", "ocr_results")
    COLLECTION_OCR_ANNOTATIONS: str = os.getenv("COLLECTION_OCR_ANNOTATIONS", "ocr_annotations")
    COLLECTION_OCR_TRAINING_SAMPLES: str = os.getenv("COLLECTION_OCR_TRAINING_SAMPLES", "ocr_training_samples")
    COLLECTION_OCR_PREPROCESSING_EFFECTIVENESS: str = os.getenv("COLLECTION_OCR_PREPROCESSING_EFFECTIVENESS", "ocr_preprocessing_effectiveness")
    COLLECTION_OCR_ERROR_PATTERNS: str = os.getenv("COLLECTION_OCR_ERROR_PATTERNS", "ocr_error_patterns")
    COLLECTION_OCR_LEARNING_REPORTS: str = os.getenv("COLLECTION_OCR_LEARNING_REPORTS", "ocr_learning_reports")
    
    # Redis配置
    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_DB: int = int(os.getenv("REDIS_DB", "0"))
    REDIS_PASSWORD: Optional[str] = os.getenv("REDIS_PASSWORD")
    
    # MinIO配置
    MINIO_ENDPOINT: str = os.getenv("MINIO_ENDPOINT", "localhost:9000")
    MINIO_ACCESS_KEY: str = os.getenv("MINIO_ACCESS_KEY", "minioadmin")
    MINIO_SECRET_KEY: str = os.getenv("MINIO_SECRET_KEY", "minioadmin")
    MINIO_BUCKET: str = os.getenv("MINIO_BUCKET", "sake")
    MINIO_SECURE: bool = os.getenv("MINIO_SECURE", "false").lower() == "true"
    
    # RabbitMQ配置
    RABBITMQ_HOST: str = os.getenv("RABBITMQ_HOST", "localhost")
    RABBITMQ_PORT: int = int(os.getenv("RABBITMQ_PORT", "5672"))
    RABBITMQ_USERNAME: str = os.getenv("RABBITMQ_USERNAME", "guest")
    RABBITMQ_PASSWORD: str = os.getenv("RABBITMQ_PASSWORD", "guest")
    RABBITMQ_VHOST: str = os.getenv("RABBITMQ_VHOST", "/")
    
    # OCR配置
    OCR_MODEL_NAME: str = os.getenv("OCR_MODEL_NAME", "ch_PP-OCRv3")
    OCR_USE_GPU: bool = os.getenv("OCR_USE_GPU", "false").lower() == "true"
    OCR_GPU_MEM: int = int(os.getenv("OCR_GPU_MEM", "4096"))
    OCR_BATCH_SIZE: int = int(os.getenv("OCR_BATCH_SIZE", "8"))
    
    # 文件处理配置
    MAX_FILE_SIZE: int = int(os.getenv("MAX_FILE_SIZE", "10485760"))  # 10MB
    ALLOWED_EXTENSIONS: list = ["jpg", "jpeg", "png", "pdf", "doc", "docx",'heic']
    
    # 图像预处理配置
    IMAGE_MAX_SIZE: int = int(os.getenv("IMAGE_MAX_SIZE", "4096"))
    IMAGE_QUALITY: int = int(os.getenv("IMAGE_QUALITY", "95"))
    
    # 学习系统配置
    TRAINING_BATCH_SIZE: int = int(os.getenv("TRAINING_BATCH_SIZE", "32"))
    TRAINING_EPOCHS: int = int(os.getenv("TRAINING_EPOCHS", "10"))
    TRAINING_LEARNING_RATE: float = float(os.getenv("TRAINING_LEARNING_RATE", "0.001"))
    
    # 监控配置
    ENABLE_MONITORING: bool = os.getenv("ENABLE_MONITORING", "true").lower() == "true"
    PROMETHEUS_PORT: int = int(os.getenv("PROMETHEUS_PORT", "9090"))
    
    # 监控阈值配置
    CPU_THRESHOLD: float = float(os.getenv("CPU_THRESHOLD", "80.0"))
    MEMORY_THRESHOLD: float = float(os.getenv("MEMORY_THRESHOLD", "80.0"))
    DISK_THRESHOLD: float = float(os.getenv("DISK_THRESHOLD", "80.0"))
    
    # 模型优化配置
    MODEL_OPTIMIZATION_BATCH_SIZE: int = 32
    MODEL_OPTIMIZATION_EPOCHS: int = 10
    MODEL_OPTIMIZATION_LEARNING_RATE: float = 0.001
    
    # PaddleLabel配置
    PADDLELABEL_HOST: str = os.getenv("PADDLELABEL_HOST", "localhost")
    PADDLELABEL_PORT: int = int(os.getenv("PADDLELABEL_PORT", "8000"))
    PADDLELABEL_URL: str = f"http://{PADDLELABEL_HOST}:{PADDLELABEL_PORT}"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # 确保所需目录存在
        for dir_path in [
            self.DATA_DIR,
            self.UPLOAD_DIR,
            self.OUTPUT_DIR,
            self.TASK_DIR,
            self.MODEL_DIR,
            self.PROCESSED_DIR,
            self.TRAINING_WORK_DIR
        ]:
            dir_path.mkdir(parents=True, exist_ok=True)

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    def get_mongodb_url(self) -> str:
        """获取完整的MongoDB连接URL"""
        if self.MONGODB_USERNAME and self.MONGODB_PASSWORD:
            # 包含认证信息的URL
            base_url = self.MONGODB_URL.replace("mongodb://", "")
            return f"mongodb://{self.MONGODB_USERNAME}:{self.MONGODB_PASSWORD}@{base_url}"
        return self.MONGODB_URL

@lru_cache()
def get_settings() -> Settings:
    """获取配置单例"""
    return Settings()

# 创建配置实例
settings = get_settings()

# 文档状态常量
class DocumentStatus:
    ACTIVE = "active"
    DELETED = "deleted"

# 默认值
DEFAULT_VALUES = {
    "deleted": False,
    "deletedAt": None,
    "status": DocumentStatus.ACTIVE
}

def get_mongodb_settings() -> Dict[str, Any]:
    """获取MongoDB配置"""
    return {
        "uri": settings.get_mongodb_url(),
        "database": settings.MONGODB_DATABASE,
        "max_pool_size": settings.MONGODB_MAX_POOL_SIZE,
        "min_pool_size": settings.MONGODB_MIN_POOL_SIZE,
        "max_idle_time_ms": settings.MONGODB_MAX_IDLE_TIME_MS,
        "connect_timeout_ms": settings.MONGODB_CONNECT_TIMEOUT_MS,
        "server_selection_timeout_ms": settings.MONGODB_SERVER_SELECTION_TIMEOUT_MS
    }

def get_redis_settings() -> Dict[str, Any]:
    """获取Redis配置"""
    return {
        "host": settings.REDIS_HOST,
        "port": settings.REDIS_PORT,
        "db": settings.REDIS_DB,
        "password": settings.REDIS_PASSWORD
    }

def get_minio_settings() -> Dict[str, Any]:
    """获取MinIO配置"""
    return {
        "endpoint": settings.MINIO_ENDPOINT,
        "access_key": settings.MINIO_ACCESS_KEY,
        "secret_key": settings.MINIO_SECRET_KEY,
        "bucket": settings.MINIO_BUCKET,
        "secure": settings.MINIO_SECURE
    }

def get_rabbitmq_settings() -> Dict[str, Any]:
    """获取RabbitMQ配置"""
    return {
        "host": settings.RABBITMQ_HOST,
        "port": settings.RABBITMQ_PORT,
        "username": settings.RABBITMQ_USERNAME,
        "password": settings.RABBITMQ_PASSWORD,
        "vhost": settings.RABBITMQ_VHOST
    }

def get_service_url(service_name: str) -> str:
    """获取服务URL"""
    service_ports = {
        "gateway": 8000,
        "upload": 8001,
        "file-management": 8002,
        "ocr": 8003,
        "quality-check": 8004,
        "batch-process": 8005
    }
    return f"http://{service_name}:{service_ports[service_name]}"

# 兼容旧的配置获取方式
def get_config_dict() -> Dict:
    """获取配置字典(兼容旧接口)"""
    settings = get_settings()
    return {
        "data_dir": str(settings.DATA_DIR),
        "upload_dir": str(settings.UPLOAD_DIR),
        "output_dir": str(settings.OUTPUT_DIR),
        "task_dir": str(settings.TASK_DIR),
        "model_dir": str(settings.MODEL_DIR),
        "processed_dir": str(settings.PROCESSED_DIR),
        "training_work_dir": str(settings.TRAINING_WORK_DIR),
        "minio": {
            "endpoint": settings.MINIO_ENDPOINT,
            "access_key": settings.MINIO_ACCESS_KEY,
            "secret_key": settings.MINIO_SECRET_KEY,
            "bucket": settings.MINIO_BUCKET,
            "secure": settings.MINIO_SECURE
        },
        "model_optimization": {
            "batch_size": settings.MODEL_OPTIMIZATION_BATCH_SIZE,
            "epochs": settings.MODEL_OPTIMIZATION_EPOCHS,
            "learning_rate": settings.MODEL_OPTIMIZATION_LEARNING_RATE
        }
    }

# 导出 MongoDB 配置
MONGODB = get_mongodb_settings() 
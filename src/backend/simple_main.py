"""
简化的FastAPI应用，只包含OCR功能
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from src.backend.routers.ocr import router as ocr_router
from src.backend.routers.documents import router as documents_router

app = FastAPI(
    title="OCR API",
    description="简化的OCR处理API",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(ocr_router, prefix="/api/ocr", tags=["OCR"])
app.include_router(documents_router, prefix="/api", tags=["Documents"])

@app.get("/")
async def root():
    return {"message": "OCR API is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "ocr-api"}

"""
缓存装饰器
提供方便的函数级缓存功能
"""

from functools import wraps
from typing import Optional, Callable, Any
import json
import hashlib
from datetime import datetime, timedelta
from pathlib import Path

def _get_cache_key(prefix: str, *args, **kwargs) -> str:
    """生成缓存键
    
    Args:
        prefix: 键前缀
        *args: 位置参数
        **kwargs: 关键字参数
        
    Returns:
        缓存键字符串
    """
    # 将参数转换为字符串
    args_str = json.dumps(args, sort_keys=True)
    kwargs_str = json.dumps(kwargs, sort_keys=True)
    
    # 生成哈希
    key = f"{prefix}:{args_str}:{kwargs_str}"
    return hashlib.md5(key.encode()).hexdigest()

def cache(prefix: str = "", ttl: int = 3600):
    """缓存装饰器
    
    Args:
        prefix: 缓存键前缀
        ttl: 缓存过期时间（秒）
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = _get_cache_key(prefix, *args, **kwargs)
            
            # 检查缓存
            cache_file = Path(f"cache/{cache_key}.json")
            if cache_file.exists():
                with cache_file.open("r") as f:
                    cache_data = json.load(f)
                    
                # 检查是否过期
                timestamp = datetime.fromisoformat(cache_data["timestamp"])
                if datetime.now() - timestamp < timedelta(seconds=ttl):
                    return cache_data["data"]
            
            # 执行原函数
            result = await func(*args, **kwargs)
            
            # 保存到缓存
            cache_file.parent.mkdir(parents=True, exist_ok=True)
            with cache_file.open("w") as f:
                json.dump({
                    "timestamp": datetime.now().isoformat(),
                    "data": result
                }, f)
            
            return result
        return wrapper
    return decorator

def invalidate_cache(prefix: str = ""):
    """缓存失效装饰器
    
    Args:
        prefix: 缓存键前缀
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 执行原函数
            result = await func(*args, **kwargs)
            
            # 删除匹配前缀的缓存文件
            cache_dir = Path("cache")
            if cache_dir.exists():
                for cache_file in cache_dir.glob("*.json"):
                    if prefix and not cache_file.stem.startswith(prefix):
                        continue
                    cache_file.unlink()
            
            return result
        return wrapper
    return decorator 
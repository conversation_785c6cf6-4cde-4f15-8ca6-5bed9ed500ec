from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import logging
import shutil
import asyncio
import aiohttp
from fastapi import UploadFile, HTTPException
from datetime import datetime
import json
import os
from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId
import json
from bson.json_util import dumps, loads
from pymongo.collection import Collection
from pymongo.database import Database
import hashlib
import uuid
from PIL import Image
import io
import base64
from minio import Minio
import magic
import aiofiles
from concurrent.futures import ThreadPoolExecutor
from functools import partial

from src.backend.services.document_conversion_service import DocumentConversionService
from src.backend.services.document_style_service import DocumentStyleService
from src.backend.services.ocr.processor import OCRProcessor
from src.backend.config import settings
from ..models.mongodb import MongoDBManager
from src.backend.models.file_model import (
    FileUploadResponse,
    FileMetadata,
    ProcessingResult
)
from src.backend.database import get_database
from src.backend.utils.file_utils import get_file_hash, create_thumbnail
from ..logger import get_logger
from ..config import Settings

logger = get_logger(__name__)

THUMBNAIL_SIZE = (200, 200)  # 缩略图尺寸

class FileProcessingService:
    def __init__(self):
        """初始化文件处理服务"""
        self.upload_dir = settings.UPLOAD_DIR
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化MinIO客户端
        self.minio_client = Minio(
            settings.MINIO_ENDPOINT,
            access_key=settings.MINIO_ACCESS_KEY,
            secret_key=settings.MINIO_SECRET_KEY,
            secure=settings.MINIO_SECURE
        )
        
        # 确保bucket存在
        if not self.minio_client.bucket_exists(settings.MINIO_BUCKET):
            self.minio_client.make_bucket(settings.MINIO_BUCKET)
            
        # 创建线程池
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        logger.info("FileProcessingService initialized successfully")
        
    async def initialize_db(self):
        """初始化数据库连接"""
        try:
            if not hasattr(self, 'db') or self.db is None:
                self.db = await get_database()
                if self.db is None:
                    raise Exception("Failed to get database connection")
                self.files_collection = self.db[settings.COLLECTION_OCR_FILES]
                logger.info("数据库连接初始化成功")
                # 验证集合是否存在
                collections = await self.db.list_collection_names()
                if settings.COLLECTION_OCR_FILES not in collections:
                    logger.warning(f"Collection {settings.COLLECTION_OCR_FILES} does not exist, creating...")
                    await self.db.create_collection(settings.COLLECTION_OCR_FILES)
        except Exception as e:
            logger.error(f"数据库连接初始化失败: {str(e)}")
            raise
        
    async def save_uploaded_file(self, file: UploadFile) -> Path:
        """保存上传的文件
        
        Args:
            file: 上传的文件对象
            
        Returns:
            Path: 保存的文件路径
        """
        await self.initialize_db()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{file.filename}"
        file_path = self.upload_dir / filename
        
        try:
            with open(file_path, "wb") as f:
                shutil.copyfileobj(file.file, f)
            return file_path
        finally:
            file.file.close()
            
    async def get_documents(self) -> List[Dict[str, Any]]:
        """获取所有文档列表"""
        try:
            await self.initialize_db()
            logger.info("开始查询文档列表")
            
            # 验证数据库连接
            if not hasattr(self, 'files_collection') or self.files_collection is None:
                logger.error("数据库集合未初始化")
                return []
                
            # 验证集合是否存在
            collections = await self.db.list_collection_names()
            if settings.COLLECTION_OCR_FILES not in collections:
                logger.warning(f"集合 {settings.COLLECTION_OCR_FILES} 不存在，正在创建...")
                await self.db.create_collection(settings.COLLECTION_OCR_FILES)
                return []
            
            try:
                # 查询文档并设置超时
                cursor = self.files_collection.find()
                cursor.max_time_ms(5000)  # 设置查询超时为5秒
                documents = await cursor.to_list(length=None)
            except Exception as e:
                logger.error(f"查询文档失败: {str(e)}", exc_info=True)
                return []
            
            # 转换 ObjectId 为字符串并处理日期时间
            result = []
            for doc in documents:
                try:
                    doc["_id"] = str(doc["_id"])
                    # 转换日期时间对象为字符串
                    for key, value in doc.items():
                        if isinstance(value, datetime):
                            doc[key] = value.isoformat()
                    result.append(doc)
                except Exception as e:
                    logger.error(f"处理文档失败: {str(e)}", exc_info=True)
                    continue
            
            logger.info(f"成功获取文档列表，共 {len(result)} 条记录")
            return result
        except Exception as e:
            logger.error(f"获取文档列表失败: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail={
                    "message": "获取文档列表失败",
                    "error": str(e)
                }
            )

    @staticmethod
    async def upload_document(file: UploadFile) -> Dict[str, Any]:
        """上传新文档"""
        try:
            # 检查文件类型
            if not file.content_type in ["application/pdf", "application/msword", "text/plain"]:
                raise HTTPException(
                    status_code=400,
                    detail="不支持的文件类型，仅支持 PDF、Word 和 TXT 文件"
                )

            # 生成唯一文件名
            file_id = str(ObjectId())
            original_filename = file.filename
            file_extension = os.path.splitext(original_filename)[1]
            new_filename = f"{file_id}{file_extension}"

            # 保存文件
            upload_dir = os.path.join(Settings.UPLOAD_DIR, "documents")
            os.makedirs(upload_dir, exist_ok=True)
            file_path = os.path.join(upload_dir, new_filename)

            async with aiofiles.open(file_path, 'wb') as f:
                content = await file.read()
                await f.write(content)

            # 创建文档记录
            document = {
                "_id": file_id,
                "original_filename": original_filename,
                "file_type": file.content_type,
                "file_size": len(content),
                "upload_time": datetime.utcnow().isoformat(),
                "status": "processing"
            }

            # TODO: 保存到数据库
            
            return document
        except HTTPException as e:
            logger.error(f"文件上传失败: {str(e.detail)}")
            raise
        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def get_document(self, document_id: str) -> Optional[Dict[str, Any]]:
        """获取单个文档详情"""
        try:
            await self.initialize_db()
            logger.info(f"开始查询文档详情: {document_id}")

            # 验证数据库连接
            if not hasattr(self, 'files_collection') or self.files_collection is None:
                logger.error("数据库集合未初始化")
                return None

            # 查询文档
            try:
                # 将字符串ID转换为ObjectId
                from bson import ObjectId
                try:
                    object_id = ObjectId(document_id)
                except Exception:
                    # 如果不是有效的ObjectId格式，直接使用字符串
                    object_id = document_id

                document = await self.files_collection.find_one({"_id": object_id})
                if document:
                    logger.info(f"成功获取文档详情: {document_id}")
                    # 转换ObjectId为字符串
                    if '_id' in document:
                        document['_id'] = str(document['_id'])

                    # 转换datetime对象为字符串
                    for key, value in document.items():
                        if isinstance(value, datetime):
                            document[key] = value.isoformat()

                    return document
                else:
                    logger.warning(f"文档不存在: {document_id}")
                    return None
            except Exception as e:
                logger.error(f"查询文档详情失败: {str(e)}", exc_info=True)
                return None

        except Exception as e:
            logger.error(f"获取文档失败: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    @staticmethod
    async def delete_document(document_id: str) -> bool:
        """删除文档"""
        try:
            # TODO: 从数据库删除文档
            return True
        except Exception as e:
            logger.error(f"删除文档失败: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def add_document(self, file: UploadFile) -> Dict[str, Any]:
        """添加新文档"""
        try:
            await self.initialize_db()
            logger.info("开始处理文件上传: %s", file.filename)
            
            # 确保上传目录存在
            self.upload_dir.mkdir(parents=True, exist_ok=True)
            logger.info("上传目录: %s", self.upload_dir.absolute())
            
            # 生成唯一的文件ID和哈希值
            file_id = str(ObjectId())
            content = await file.read()
            file_hash = hashlib.sha256(content).hexdigest()
            file_size = len(content)
            
            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            original_filename = file.filename
            filename = f"{timestamp}_{original_filename}"
            file_path = self.upload_dir / filename
            logger.info("文件将保存到: %s", file_path.absolute())
            
            # 保存文件
            with open(file_path, "wb") as f:
                f.write(content)
            
            # 创建文档记录
            doc = {
                "file_id": file_id,
                "file_hash": file_hash,
                "original_filename": original_filename,
                "filename": filename,
                "file_path": str(file_path.absolute()),
                "file_type": file.content_type,
                "file_size": file_size,
                "upload_time": datetime.now().isoformat(),
                "status": "uploading",
                "metadata": {}
            }
            
            # 保存到数据库
            result = await self.db.ocr_files.insert_one(doc)
            doc["_id"] = str(result.inserted_id)
            
            # 异步处理文档
            asyncio.create_task(self._process_document(str(result.inserted_id), file_path))
            
            return doc
            
        except Exception as e:
            logger.error("添加文档失败: %s", str(e))
            raise
            
    async def _process_document(self, doc_id: str, file_path: Path) -> None:
        """异步处理文档"""
        try:
            # 更新状态为处理中
            await self.db.ocr_files.update_one(
                {"_id": ObjectId(doc_id)},
                {"$set": {"status": "processing"}}
            )
            
            # 处理文档（OCR等）
            # TODO: 添加实际的文档处理逻辑
            
            # 更新状态为处理完成
            await self.db.ocr_files.update_one(
                {"_id": ObjectId(doc_id)},
                {"$set": {"status": "processed"}}
            )
            
        except Exception as e:
            logger.error("处理文档失败: %s", str(e))
            # 更新状态为处理失败
            await self.db.ocr_files.update_one(
                {"_id": ObjectId(doc_id)},
                {"$set": {"status": "error", "error_message": str(e)}}
            )

    async def update_document_status(self, document_id: str, status: str) -> bool:
        """更新文档状态"""
        result = await self.files_collection.update_one(
            {"_id": document_id},
            {
                "$set": {
                    "status": status,
                    "updated_at": datetime.utcnow()
                }
            }
        )
        return result.modified_count > 0

    async def close(self):
        """关闭MongoDB连接"""
        if self.db:
            self.db.close()

    async def _save_document_record(self, document: Dict[str, Any]):
        """保存文档记录
        
        Args:
            document: 文档记录
        """
        try:
            await self.files_collection.insert_one(document)
        except Exception as e:
            logger.error(f"Failed to save document record to MongoDB: {e}")

    async def process_file(
        self,
        file_path: Union[str, Path],
        output_format: str = "json",
        apply_style: bool = True
    ) -> Dict[str, Any]:
        """处理单个文件
        
        Args:
            file_path: 要处理的文件路径
            output_format: 输出格式
            apply_style: 是否应用样式
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        file_path = Path(file_path)
        
        # OCR处理
        ocr_result = await self.ocr_processor.process_image(str(file_path))
        
        # 保存文档记录
        document_record = {
            "file_id": str(file_path.stem),
            "original_filename": file_path.name,
            "file_type": file_path.suffix[1:],
            "file_size": file_path.stat().st_size,
            "upload_time": datetime.now().isoformat(),
            "status": "completed",
            "deleted": False,
            "metadata": {
                "width": ocr_result.get("width"),
                "height": ocr_result.get("height")
            }
        }
        await self._save_document_record(document_record)
        
        # 应用样式（如果需要）
        if apply_style:
            ocr_result = self.style_service.apply_style(ocr_result)
            
        # 转换格式
        output_path = self.output_dir / f"{file_path.stem}.{output_format}"
        if output_format == "json":
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(ocr_result, f, ensure_ascii=False, indent=2)
        else:
            if output_format == "docx":
                doc = self.conversion_service.convert_to_docx(ocr_result)
                doc.save(output_path)
            elif output_format == "pdf":
                self.conversion_service.convert_to_pdf(ocr_result, str(output_path))
            elif output_format == "html":
                html_content = self.conversion_service.convert_to_html(ocr_result)
                with open(output_path, "w", encoding="utf-8") as f:
                    f.write(html_content)
            elif output_format == "excel":
                self.conversion_service.convert_to_excel(ocr_result, str(output_path))
                
        return {
            "input_file": str(file_path),
            "output_file": str(output_path),
            "format": output_format,
            "status": "success"
        }
        
    async def process_files(
        self,
        file_paths: List[Union[str, Path]],
        output_format: str = "json",
        apply_style: bool = True,
        merge_output: bool = False
    ) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """批量处理多个文件
        
        Args:
            file_paths: 要处理的文件路径列表
            output_format: 输出格式
            apply_style: 是否应用样式
            merge_output: 是否合并输出
            
        Returns:
            Union[Dict[str, Any], List[Dict[str, Any]]]: 处理结果
        """
        # 并行处理所有文件
        tasks = [
            self.process_file(file_path, output_format, apply_style)
            for file_path in file_paths
        ]
        results = await asyncio.gather(*tasks)
        
        # 如果需要合并输出
        if merge_output and len(results) > 0:
            # 收集所有OCR结果
            ocr_results = []
            for file_path in file_paths:
                result = await self.ocr_processor.process_image(str(file_path))
                if apply_style:
                    result = self.style_service.apply_style(result)
                ocr_results.append(result)
                
            # 合并并转换
            merged_output_path = self.output_dir / f"merged.{output_format}"
            merged_content = self.conversion_service.merge_documents(ocr_results, output_format)
            
            if output_format == "docx":
                merged_content.save(merged_output_path)
            elif output_format == "pdf":
                self.conversion_service.convert_to_pdf(merged_content, str(merged_output_path))
            elif output_format == "html":
                with open(merged_output_path, "w", encoding="utf-8") as f:
                    f.write(merged_content)
            elif output_format == "excel":
                self.conversion_service.convert_to_excel(merged_content, str(merged_output_path))
            elif output_format == "json":
                with open(merged_output_path, "w", encoding="utf-8") as f:
                    json.dump(merged_content, f, ensure_ascii=False, indent=2)
                    
            return {
                "input_files": [str(path) for path in file_paths],
                "output_file": str(merged_output_path),
                "format": output_format,
                "status": "success"
            }
            
        return results
        
    async def cleanup_old_files(self, max_age_days: int = 7):
        """清理旧文件
        
        Args:
            max_age_days: 文件最大保留天数
        """
        current_time = datetime.now()
        
        # 清理上传目录
        for file_path in self.upload_dir.glob("*"):
            if file_path.is_file():
                file_age = current_time - datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_age.days > max_age_days:
                    file_path.unlink()
                    
        # 清理输出目录
        for file_path in self.output_dir.glob("*"):
            if file_path.is_file():
                file_age = current_time - datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_age.days > max_age_days:
                    file_path.unlink()

    async def save_annotations(self, file_id: str, annotations: List[Dict[str, Any]]) -> bool:
        try:
            result = await self.files_collection.update_one(
                {"_id": ObjectId(file_id)},
                {"$set": {
                    "annotations": annotations,
                    "updated_at": datetime.utcnow()
                }}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"Error saving annotations: {e}")
            return False

    async def get_annotations(self, file_id: str) -> List[Dict[str, Any]]:
        try:
            doc = await self.files_collection.find_one(
                {"_id": ObjectId(file_id)},
                {"annotations": 1}
            )
            return doc.get("annotations", []) if doc else []
        except Exception as e:
            print(f"Error getting annotations: {e}")
            return []

    async def save_file(self, file_id: str, file_content: bytes, original_filename: str, file_type: str) -> bool:
        try:
            # 保存文件到磁盘
            file_path = self.upload_dir / file_id
            with open(file_path, 'wb') as f:
                f.write(file_content)

            # 生成缩略图
            thumbnail_data = None
            if file_type.startswith('image/'):
                with Image.open(file_path) as img:
                    # 获取原始尺寸
                    width, height = img.size
                    
                    # 生成缩略图
                    img.thumbnail(THUMBNAIL_SIZE)
                    thumbnail_buffer = io.BytesIO()
                    img.save(thumbnail_buffer, format='JPEG')
                    thumbnail_data = base64.b64encode(thumbnail_buffer.getvalue()).decode('utf-8')

            # 保存文件信息到数据库
            file_info = {
                "_id": file_id,
                "original_filename": original_filename,
                "file_type": file_type,
                "file_size": len(file_content),
                "upload_time": datetime.now(),
                "status": "processing",
                "metadata": {
                    "width": width if file_type.startswith('image/') else None,
                    "height": height if file_type.startswith('image/') else None,
                    "thumbnail": f"data:image/jpeg;base64,{thumbnail_data}" if thumbnail_data else None
                }
            }
            await self.db.documents.insert_one(file_info)
            return True
        except Exception as e:
            print(f"Error saving file: {e}")
            return False

    async def get_file_info(self, file_id: str) -> Optional[Dict[str, Any]]:
        try:
            file_info = await self.db.documents.find_one({"_id": file_id})
            if file_info:
                return file_info
            return None
        except Exception as e:
            print(f"Error getting file info: {e}")
            return None

    async def update_file_status(self, file_id: str, status: str) -> bool:
        try:
            result = await self.db.documents.update_one(
                {"_id": file_id},
                {"$set": {"status": status}}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"Error updating file status: {e}")
            return False

    async def delete_file(self, file_id: str) -> bool:
        try:
            # 删除文件
            file_path = self.upload_dir / file_id
            if file_path.exists():
                file_path.unlink()

            # 删除数据库记录
            result = await self.db.documents.delete_one({"_id": file_id})
            return result.deleted_count > 0
        except Exception as e:
            print(f"Error deleting file: {e}")
            return False

    async def list_files(self) -> List[Dict[str, Any]]:
        try:
            cursor = self.db.documents.find()
            files = await cursor.to_list(length=None)
            return files
        except Exception as e:
            print(f"Error listing files: {e}")
            return []

    async def save_annotation(self, file_id: str, annotation: Dict[str, Any]) -> bool:
        try:
            # 检查文件是否存在
            file_info = await self.get_file_info(file_id)
            if not file_info:
                return False

            # 保存标注
            result = await self.db.annotations.insert_one({
                "_id": annotation["id"],
                "file_id": file_id,
                "content": annotation["content"],
                "created_at": annotation["created_at"]
            })
            return True
        except Exception as e:
            print(f"Error saving annotation: {e}")
            return False

    async def delete_annotation(self, file_id: str, annotation_id: str) -> bool:
        try:
            result = await self.db.annotations.delete_one({
                "_id": annotation_id,
                "file_id": file_id
            })
            return result.deleted_count > 0
        except Exception as e:
            print(f"Error deleting annotation: {e}")
            return False 
from typing import List, Dict, Optional, Any
from datetime import datetime
import asyncio
import json
from pathlib import Path
import shutil
from fastapi import UploadFile
import uuid
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.backend.logger import get_logger
from src.backend.config import settings

class DocumentOutputService:
    """文档输出服务"""
    
    def __init__(self, 
                 upload_dir: str = settings.UPLOAD_DIR,
                 output_dir: str = settings.OUTPUT_DIR,
                 task_dir: str = settings.TASK_DIR):
        # 创建必要的目录
        self.upload_dir = Path(upload_dir)
        self.output_dir = Path(output_dir)
        self.task_dir = Path(task_dir)
        
        for directory in [self.upload_dir, self.output_dir, self.task_dir]:
            directory.mkdir(parents=True, exist_ok=True)
            
        self.logger = get_logger(__name__)
        self.tasks: Dict[str, Dict] = {}
        
    async def save_uploaded_file(self, file: UploadFile) -> str:
        """保存上传的文件
        
        Args:
            file: 上传的文件对象
            
        Returns:
            文件ID
        """
        # 生成文件ID
        file_id = str(uuid.uuid4())
        
        # 保存文件
        file_path = self.upload_dir / f"{file_id}_{file.filename}"
        with file_path.open("wb") as f:
            shutil.copyfileobj(file.file, f)
            
        self.logger.info(f"保存上传文件：{file.filename} -> {file_id}")
        return file_id
        
    async def create_batch_task(self,
                              file_ids: List[str],
                              output_format: str,
                              merge_files: bool,
                              style_template: Optional[str]) -> str:
        """创建批处理任务
        
        Args:
            file_ids: 文件ID列表
            output_format: 输出格式
            merge_files: 是否合并文件
            style_template: 样式模板
            
        Returns:
            任务ID
        """
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 创建任务信息
        task = {
            "task_id": task_id,
            "file_ids": file_ids,
            "output_format": output_format,
            "merge_files": merge_files,
            "style_template": style_template,
            "status": "pending",
            "progress": 0.0,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "error_message": None
        }
        
        # 保存任务信息
        task_file = self.task_dir / f"task_{task_id}.json"
        with task_file.open("w", encoding="utf-8") as f:
            json.dump(task, f, ensure_ascii=False, indent=2)
            
        self.tasks[task_id] = task
        self.logger.info(f"创建批处理任务：{task_id}")
        return task_id
        
    async def process_batch_task(self, task_id: str):
        """处理批处理任务
        
        Args:
            task_id: 任务ID
        """
        task = self.tasks.get(task_id)
        if not task:
            self.logger.error(f"任务不存在：{task_id}")
            return
            
        try:
            # 更新任务状态
            task["status"] = "processing"
            task["updated_at"] = datetime.now().isoformat()
            await self._save_task(task)
            
            # 处理每个文件
            total_files = len(task["file_ids"])
            for i, file_id in enumerate(task["file_ids"]):
                # 更新进度
                task["progress"] = (i + 1) / total_files * 100
                task["updated_at"] = datetime.now().isoformat()
                await self._save_task(task)
                
                # TODO: 实现实际的文件处理逻辑
                await asyncio.sleep(1)  # 模拟处理时间
                
            # 完成处理
            task["status"] = "completed"
            task["progress"] = 100.0
            task["updated_at"] = datetime.now().isoformat()
            await self._save_task(task)
            
        except Exception as e:
            self.logger.error(f"处理任务失败：{task_id} - {str(e)}")
            task["status"] = "failed"
            task["error_message"] = str(e)
            task["updated_at"] = datetime.now().isoformat()
            await self._save_task(task)
            
    async def get_task_status(self, task_id: str) -> Dict:
        """获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态信息
        """
        task = self.tasks.get(task_id)
        if not task:
            # 尝试从文件加载
            task_file = self.task_dir / f"task_{task_id}.json"
            if not task_file.exists():
                raise ValueError(f"任务不存在：{task_id}")
                
            with task_file.open("r", encoding="utf-8") as f:
                task = json.load(f)
                self.tasks[task_id] = task
                
        return task
        
    async def cancel_task(self, task_id: str):
        """取消任务
        
        Args:
            task_id: 任务ID
        """
        task = await self.get_task_status(task_id)
        if task["status"] not in ["pending", "processing"]:
            raise ValueError(f"无法取消任务：{task_id} (状态: {task['status']})")
            
        task["status"] = "cancelled"
        task["updated_at"] = datetime.now().isoformat()
        await self._save_task(task)
        
    async def get_processed_files(self, task_id: str) -> Dict[str, Any]:
        """获取处理后的文件
        
        Args:
            task_id: 任务ID
            
        Returns:
            处理结果信息
        """
        task = await self.get_task_status(task_id)
        if task["status"] != "completed":
            raise ValueError(f"任务未完成：{task_id} (状态: {task['status']})")
            
        # TODO: 实现获取处理后文件的逻辑
        return {
            "task_id": task_id,
            "files": []  # 返回文件列表
        }
        
    async def _save_task(self, task: Dict):
        """保存任务信息
        
        Args:
            task: 任务信息
        """
        task_file = self.task_dir / f"task_{task['task_id']}.json"
        with task_file.open("w", encoding="utf-8") as f:
            json.dump(task, f, ensure_ascii=False, indent=2) 
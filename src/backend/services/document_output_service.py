from typing import List, Optional, Dict
from fastapi import UploadFile, HTTPException
from datetime import datetime
import asyncio
import aiofiles
import json
import uuid
from pathlib import Path
import motor.motor_asyncio
from bson import ObjectId

from src.backend.config import get_settings

class DocumentOutputService:
    def __init__(self):
        self.settings = get_settings()
        self.client = motor.motor_asyncio.AsyncIOMotorClient(self.settings.MONGODB_URL)
        self.db = self.client[self.settings.MONGODB_DATABASE]
        self.tasks_collection = self.db["document_output_tasks"]
        self.files_collection = self.db["document_output_files"]
        
        # 创建必要的目录
        self.upload_dir = Path(self.settings.document_output.upload_dir)
        self.output_dir = Path(self.settings.document_output.output_dir)
        self.style_templates_dir = Path(self.settings.document_output.style_templates_dir)
        
        for directory in [self.upload_dir, self.output_dir, self.style_templates_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # 任务状态缓存
        self._tasks: Dict[str, dict] = {}

    async def save_uploaded_file(self, file: UploadFile) -> str:
        """保存上传的文件并返回文件ID"""
        if file.size > self.settings.document_output.max_file_size:
            raise HTTPException(status_code=413, detail="文件大小超过限制")
            
        file_id = str(ObjectId())
        file_path = self.upload_dir / f"{file_id}_{file.filename}"
        
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        file_doc = {
            "_id": ObjectId(file_id),
            "filename": file.filename,
            "path": str(file_path),
            "size": len(content),
            "uploaded_at": datetime.utcnow()
        }
        
        await self.files_collection.insert_one(file_doc)
        return file_id
    
    async def create_batch_task(self, file_ids: List[str], output_format: str, merge_files: bool, style_template: Optional[str] = None) -> str:
        """创建批量处理任务"""
        if output_format not in self.settings.document_output.allowed_formats:
            raise HTTPException(status_code=400, detail=f"不支持的输出格式: {output_format}")
        
        task_id = str(ObjectId())
        task_doc = {
            "_id": ObjectId(task_id),
            "file_ids": [ObjectId(fid) for fid in file_ids],
            "output_format": output_format,
            "merge_files": merge_files,
            "style_template": style_template,
            "status": "pending",
            "progress": 0,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "error_message": None
        }
        
        await self.tasks_collection.insert_one(task_doc)
        return task_id
    
    async def get_task_status(self, task_id: str) -> Dict:
        """获取任务状态"""
        task = await self.tasks_collection.find_one({"_id": ObjectId(task_id)})
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        task["_id"] = str(task["_id"])
        task["file_ids"] = [str(fid) for fid in task["file_ids"]]
        return task
    
    async def update_task_status(self, task_id: str, status: str, progress: float, error_message: Optional[str] = None):
        """更新任务状态"""
        update_doc = {
            "status": status,
            "progress": progress,
            "updated_at": datetime.utcnow()
        }
        if error_message:
            update_doc["error_message"] = error_message
            
        await self.tasks_collection.update_one(
            {"_id": ObjectId(task_id)},
            {"$set": update_doc}
        )
        
        if task_id in self._tasks:
            self._tasks[task_id].update(update_doc)

    async def process_batch_task(self, task_id: str):
        """处理批量任务"""
        task = await self.get_task_status(task_id)
        
        try:
            await self.update_task_status(task_id, "processing", 0)
            
            # 获取所有文件信息
            files = []
            for file_id in task["file_ids"]:
                file_doc = await self.files_collection.find_one({"_id": ObjectId(file_id)})
                if not file_doc:
                    raise HTTPException(status_code=404, detail=f"文件不存在: {file_id}")
                files.append(file_doc)
            
            total_files = len(files)
            for i, file in enumerate(files, 1):
                # TODO: 实现具体的文件处理逻辑
                progress = (i / total_files) * 100
                await self.update_task_status(task_id, "processing", progress)
                
            await self.update_task_status(task_id, "completed", 100)
            
        except Exception as e:
            await self.update_task_status(task_id, "failed", 0, str(e))
            raise

    async def cancel_task(self, task_id: str):
        """取消处理任务"""
        task = await self.tasks_collection.find_one({"task_id": task_id})
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
            
        if task["status"] in ["completed", "failed"]:
            raise HTTPException(status_code=400, detail="Task already finished")
            
        await self._update_task_status(task_id, "cancelled", task["progress"])

    async def get_processed_files(self, task_id: str):
        """获取处理完成的文件"""
        task = await self.tasks_collection.find_one({"task_id": task_id})
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
            
        if task["status"] != "completed":
            raise HTTPException(status_code=400, detail="Task not completed")
            
        # TODO: 实现文件下载逻辑
        # 1. 检查输出目录中的文件
        # 2. 返回文件或文件列表的下载链接
        raise HTTPException(status_code=501, detail="Not implemented yet") 
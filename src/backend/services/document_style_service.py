from typing import Dict, Any, List, Optional
import yaml
from pathlib import Path
import json
import logging
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
import seaborn as sns
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import Table, TableStyle
from src.backend.config import settings

logger = logging.getLogger(__name__)

class DocumentStyleService:
    def __init__(self):
        """初始化文档样式服务"""
        # 设置样式模板目录
        self.style_templates_dir = Path("data/style_templates")
        
        # 确保模板目录存在
        self.style_templates_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化样式模板
        self._initialize_style_templates()
        
    def _initialize_style_templates(self):
        """初始化默认样式模板"""
        default_templates = {
            "modern": {
                "table": {
                    "header_color": "#4A90E2",
                    "header_text_color": "#FFFFFF",
                    "row_colors": ["#F5F5F5", "#FFFFFF"],
                    "border_color": "#E0E0E0",
                    "font_family": "Helvetica",
                    "header_font_size": 12,
                    "body_font_size": 10,
                    "padding": 8
                },
                "chart": {
                    "palette": "deep",
                    "style": "whitegrid",
                    "title_font_size": 14,
                    "axis_font_size": 10,
                    "legend_font_size": 10,
                    "figure_size": (10, 6)
                }
            },
            "classic": {
                "table": {
                    "header_color": "#2C3E50",
                    "header_text_color": "#FFFFFF",
                    "row_colors": ["#FFFFFF", "#F9F9F9"],
                    "border_color": "#DDE4E9",
                    "font_family": "Times New Roman",
                    "header_font_size": 12,
                    "body_font_size": 11,
                    "padding": 6
                },
                "chart": {
                    "palette": "muted",
                    "style": "ticks",
                    "title_font_size": 16,
                    "axis_font_size": 11,
                    "legend_font_size": 11,
                    "figure_size": (8, 5)
                }
            }
        }
        
        # 保存默认模板
        for name, template in default_templates.items():
            template_path = self.style_templates_dir / f"{name}.json"
            if not template_path.exists():
                with open(template_path, 'w', encoding='utf-8') as f:
                    json.dump(template, f, indent=2)
                    
    def get_available_templates(self) -> List[str]:
        """获取所有可用的样式模板"""
        return [f.stem for f in self.style_templates_dir.glob("*.json")]
        
    def load_template(self, template_name: str) -> Dict[str, Any]:
        """加载指定的样式模板"""
        template_path = self.style_templates_dir / f"{template_name}.json"
        if not template_path.exists():
            logger.warning(f"Template {template_name} not found, using default template")
            template_name = "modern"
            template_path = self.style_templates_dir / f"{template_name}.json"
            
        with open(template_path, 'r', encoding='utf-8') as f:
            return json.load(f)
            
    def apply_table_style(self, data: List[List[str]], template_name: str = "modern") -> Table:
        """应用表格样式"""
        template = self.load_template(template_name)
        table_style = template["table"]
        
        # 创建表格样式
        style = TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), table_style["header_color"]),
            ('TEXTCOLOR', (0, 0), (-1, 0), table_style["header_text_color"]),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), table_style["font_family"]),
            ('FONTSIZE', (0, 0), (-1, 0), table_style["header_font_size"]),
            ('BOTTOMPADDING', (0, 0), (-1, 0), table_style["padding"]),
            ('BACKGROUND', (0, 1), (-1, -1), table_style["row_colors"][0]),
            ('TEXTCOLOR', (0, 1), (-1, -1), '#000000'),
            ('FONTNAME', (0, 1), (-1, -1), table_style["font_family"]),
            ('FONTSIZE', (0, 1), (-1, -1), table_style["body_font_size"]),
            ('GRID', (0, 0), (-1, -1), 1, table_style["border_color"]),
            ('LINEBELOW', (0, 0), (-1, 0), 2, table_style["border_color"]),
            ('LINEAFTER', (0, 0), (-1, -1), 1, table_style["border_color"])
        ])
        
        # 为偶数行添加交替颜色
        for i in range(1, len(data), 2):
            style.add('BACKGROUND', (0, i), (-1, i), table_style["row_colors"][1])
            
        table = Table(data)
        table.setStyle(style)
        return table
        
    def apply_chart_style(self, template_name: str = "modern") -> None:
        """应用图表样式到当前的matplotlib图表"""
        template = self.load_template(template_name)
        chart_style = template["chart"]
        
        # 设置seaborn样式
        sns.set_style(chart_style["style"])
        sns.set_palette(chart_style["palette"])
        
        # 设置图表大小
        plt.figure(figsize=chart_style["figure_size"])
        
        # 设置字体大小
        plt.title(plt.gca().get_title(), fontsize=chart_style["title_font_size"])
        plt.xlabel(plt.gca().get_xlabel(), fontsize=chart_style["axis_font_size"])
        plt.ylabel(plt.gca().get_ylabel(), fontsize=chart_style["axis_font_size"])
        
        # 设置图例字体大小
        legend = plt.gca().get_legend()
        if legend:
            plt.setp(legend.get_texts(), fontsize=chart_style["legend_font_size"])
            
    def create_custom_template(self, template_name: str, template_data: Dict[str, Any]) -> None:
        """创建自定义样式模板"""
        template_path = self.style_templates_dir / f"{template_name}.json"
        with open(template_path, 'w', encoding='utf-8') as f:
            json.dump(template_data, f, indent=2)
            
    def update_template(self, template_name: str, template_data: Dict[str, Any]) -> None:
        """更新现有的样式模板"""
        template_path = self.style_templates_dir / f"{template_name}.json"
        if not template_path.exists():
            raise ValueError(f"Template {template_name} does not exist")
            
        with open(template_path, 'w', encoding='utf-8') as f:
            json.dump(template_data, f, indent=2)
            
    def delete_template(self, template_name: str) -> None:
        """删除样式模板"""
        if template_name in ["modern", "classic"]:
            raise ValueError("Cannot delete default templates")
            
        template_path = self.style_templates_dir / f"{template_name}.json"
        if template_path.exists():
            template_path.unlink()
        else:
            raise ValueError(f"Template {template_name} does not exist") 
"""
性能监控服务模块
提供系统性能监控和资源使用统计
"""

import os
import psutil
import time
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import logging
from prometheus_client import Counter, Gauge, Histogram, start_http_server

from src.backend.config import settings
from src.backend.logger import get_logger

logger = get_logger("monitor")

class PerformanceMonitorService:
    """性能监控服务类"""
    
    _instance: Optional["PerformanceMonitorService"] = None
    
    # Prometheus指标
    request_count = Counter(
        "http_requests_total",
        "Total number of HTTP requests",
        ["method", "endpoint", "status"]
    )
    
    request_latency = Histogram(
        "http_request_duration_seconds",
        "HTTP request duration in seconds",
        ["method", "endpoint"]
    )
    
    cpu_usage = Gauge(
        "system_cpu_usage",
        "Current CPU usage percentage"
    )
    
    memory_usage = Gauge(
        "system_memory_usage",
        "Current memory usage percentage"
    )
    
    disk_usage = Gauge(
        "system_disk_usage",
        "Current disk usage percentage"
    )
    
    def __new__(cls) -> "PerformanceMonitorService":
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """初始化监控服务"""
        if not self._initialized:
            self._initialized = True
            self._start_time = datetime.now()
            self._last_check = self._start_time
            self._check_interval = 60  # 60秒检查一次
            
            # 启动Prometheus服务器
            if settings.ENABLE_MONITORING:
                try:
                    start_http_server(settings.PROMETHEUS_PORT)
                    logger.info(f"Prometheus metrics server started on port {settings.PROMETHEUS_PORT}")
                except Exception as e:
                    logger.error(f"Failed to start Prometheus server: {str(e)}")
    
    def record_request(self, method: str, endpoint: str, status: int, duration: float):
        """记录HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: 请求端点
            status: 状态码
            duration: 请求耗时（秒）
        """
        self.request_count.labels(method=method, endpoint=endpoint, status=status).inc()
        self.request_latency.labels(method=method, endpoint=endpoint).observe(duration)
    
    def check_system_resources(self) -> Dict[str, float]:
        """检查系统资源使用情况
        
        Returns:
            Dict[str, float]: 资源使用情况
        """
        now = datetime.now()
        if (now - self._last_check).total_seconds() < self._check_interval:
            return {}
        
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_usage.set(cpu_percent)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self.memory_usage.set(memory_percent)
            
            # 磁盘使用率
            disk = psutil.disk_usage("/")
            disk_percent = disk.percent
            self.disk_usage.set(disk_percent)
            
            # 更新检查时间
            self._last_check = now
            
            # 检查是否超过阈值
            warnings = []
            if cpu_percent > settings.CPU_THRESHOLD:
                warnings.append(f"CPU usage ({cpu_percent}%) exceeds threshold ({settings.CPU_THRESHOLD}%)")
            if memory_percent > settings.MEMORY_THRESHOLD:
                warnings.append(f"Memory usage ({memory_percent}%) exceeds threshold ({settings.MEMORY_THRESHOLD}%)")
            if disk_percent > settings.DISK_THRESHOLD:
                warnings.append(f"Disk usage ({disk_percent}%) exceeds threshold ({settings.DISK_THRESHOLD}%)")
            
            if warnings:
                logger.warning("\n".join(warnings))
            
            return {
                "cpu_percent": cpu_percent,
                "memory_percent": memory_percent,
                "disk_percent": disk_percent
            }
        except Exception as e:
            logger.error(f"Failed to check system resources: {str(e)}")
            return {}
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息
        
        Returns:
            Dict[str, Any]: 系统信息
        """
        try:
            cpu_count = psutil.cpu_count()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage("/")
            
            return {
                "system": {
                    "platform": os.uname().sysname,
                    "release": os.uname().release,
                    "version": os.uname().version,
                    "machine": os.uname().machine
                },
                "cpu": {
                    "physical_cores": psutil.cpu_count(logical=False),
                    "total_cores": cpu_count,
                    "frequency": psutil.cpu_freq().current if hasattr(psutil.cpu_freq(), "current") else None
                },
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "percent": memory.percent
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "percent": disk.percent
                },
                "uptime": str(datetime.now() - self._start_time)
            }
        except Exception as e:
            logger.error(f"Failed to get system info: {str(e)}")
            return {}
    
    def get_process_info(self) -> Dict[str, Any]:
        """获取进程信息
        
        Returns:
            Dict[str, Any]: 进程信息
        """
        try:
            process = psutil.Process()
            
            return {
                "pid": process.pid,
                "name": process.name(),
                "status": process.status(),
                "cpu_percent": process.cpu_percent(),
                "memory_percent": process.memory_percent(),
                "memory_info": dict(process.memory_info()._asdict()),
                "create_time": datetime.fromtimestamp(process.create_time()).isoformat(),
                "threads": process.num_threads()
            }
        except Exception as e:
            logger.error(f"Failed to get process info: {str(e)}")
            return {} 
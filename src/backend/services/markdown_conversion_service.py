from typing import List, Dict, Any, Optional
import re
from pathlib import Path
import json
import logging
from PIL import Image
import pytesseract
from paddleocr import PaddleOCR
import pandas as pd
import numpy as np
from .document_style_service import DocumentStyleService

logger = logging.getLogger(__name__)

class MarkdownConversionService:
    def __init__(self, style_service: Optional[DocumentStyleService] = None):
        """初始化Markdown转换服务"""
        self.style_service = style_service or DocumentStyleService()
        
    def convert_to_markdown(self, document_data: Dict[str, Any]) -> str:
        """将文档数据转换为Markdown格式"""
        markdown_content = []
        
        # 处理标题
        if "title" in document_data:
            markdown_content.append(f"# {document_data['title']}\n")
            
        # 处理元数据
        if "metadata" in document_data:
            markdown_content.extend(self._convert_metadata(document_data["metadata"]))
            
        # 处理正文内容
        if "content" in document_data:
            markdown_content.extend(self._convert_content(document_data["content"]))
            
        return "\n".join(markdown_content)
        
    def _convert_metadata(self, metadata: Dict[str, Any]) -> List[str]:
        """转换元数据为Markdown格式"""
        markdown_lines = []
        markdown_lines.append("---")
        for key, value in metadata.items():
            markdown_lines.append(f"{key}: {value}")
        markdown_lines.append("---\n")
        return markdown_lines
        
    def _convert_content(self, content: List[Dict[str, Any]]) -> List[str]:
        """转换文档内容为Markdown格式"""
        markdown_lines = []
        
        for block in content:
            block_type = block.get("type", "text")
            
            if block_type == "heading":
                level = block.get("level", 1)
                text = block.get("text", "")
                markdown_lines.append(f"{'#' * level} {text}\n")
                
            elif block_type == "paragraph":
                text = block.get("text", "")
                markdown_lines.append(f"{text}\n")
                
            elif block_type == "table":
                markdown_lines.extend(self._convert_table(block))
                
            elif block_type == "list":
                markdown_lines.extend(self._convert_list(block))
                
            elif block_type == "code":
                markdown_lines.extend(self._convert_code(block))
                
            elif block_type == "image":
                markdown_lines.extend(self._convert_image(block))
                
            elif block_type == "chart":
                markdown_lines.extend(self._convert_chart(block))
                
        return markdown_lines
        
    def _convert_table(self, table_block: Dict[str, Any]) -> List[str]:
        """转换表格为Markdown格式"""
        markdown_lines = []
        
        # 获取表格数据
        headers = table_block.get("headers", [])
        rows = table_block.get("rows", [])
        
        if not headers or not rows:
            return markdown_lines
            
        # 构建表头
        header_line = "| " + " | ".join(headers) + " |"
        markdown_lines.append(header_line)
        
        # 构建分隔线
        separator_line = "| " + " | ".join(["---" for _ in headers]) + " |"
        markdown_lines.append(separator_line)
        
        # 构建数据行
        for row in rows:
            row_line = "| " + " | ".join(str(cell) for cell in row) + " |"
            markdown_lines.append(row_line)
            
        markdown_lines.append("")  # 添加空行
        return markdown_lines
        
    def _convert_list(self, list_block: Dict[str, Any]) -> List[str]:
        """转换列表为Markdown格式"""
        markdown_lines = []
        items = list_block.get("items", [])
        list_type = list_block.get("type", "unordered")
        
        for index, item in enumerate(items, 1):
            if list_type == "ordered":
                markdown_lines.append(f"{index}. {item}")
            else:
                markdown_lines.append(f"- {item}")
                
        markdown_lines.append("")  # 添加空行
        return markdown_lines
        
    def _convert_code(self, code_block: Dict[str, Any]) -> List[str]:
        """转换代码块为Markdown格式"""
        markdown_lines = []
        code = code_block.get("code", "")
        language = code_block.get("language", "")
        
        markdown_lines.append(f"```{language}")
        markdown_lines.append(code)
        markdown_lines.append("```")
        markdown_lines.append("")  # 添加空行
        return markdown_lines
        
    def _convert_image(self, image_block: Dict[str, Any]) -> List[str]:
        """转换图片为Markdown格式"""
        markdown_lines = []
        path = image_block.get("path", "")
        alt = image_block.get("alt", "")
        title = image_block.get("title", "")
        
        if title:
            markdown_lines.append(f"![{alt}]({path} \"{title}\")")
        else:
            markdown_lines.append(f"![{alt}]({path})")
            
        markdown_lines.append("")  # 添加空行
        return markdown_lines
        
    def _convert_chart(self, chart_block: Dict[str, Any]) -> List[str]:
        """转换图表为Markdown格式"""
        markdown_lines = []
        chart_type = chart_block.get("type", "")
        data = chart_block.get("data", {})
        
        # 将图表转换为Mermaid图表
        if chart_type == "flowchart":
            markdown_lines.append("```mermaid")
            markdown_lines.append("graph TD")
            for node in data.get("nodes", []):
                markdown_lines.append(f"    {node['id']}[{node['label']}]")
            for edge in data.get("edges", []):
                markdown_lines.append(f"    {edge['from']} --> {edge['to']}")
            markdown_lines.append("```")
            
        elif chart_type == "sequence":
            markdown_lines.append("```mermaid")
            markdown_lines.append("sequenceDiagram")
            for message in data.get("messages", []):
                markdown_lines.append(f"    {message['from']}->{message['to']}: {message['text']}")
            markdown_lines.append("```")
            
        elif chart_type == "pie":
            markdown_lines.append("```mermaid")
            markdown_lines.append("pie")
            for item in data.get("items", []):
                markdown_lines.append(f"    \"{item['label']}\" : {item['value']}")
            markdown_lines.append("```")
            
        markdown_lines.append("")  # 添加空行
        return markdown_lines
        
    def batch_convert_to_markdown(self, documents: List[Dict[str, Any]]) -> List[str]:
        """批量转换文档为Markdown格式"""
        return [self.convert_to_markdown(doc) for doc in documents]
        
    def save_markdown(self, markdown_content: str, output_path: str) -> None:
        """保存Markdown内容到文件"""
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
            
    def batch_save_markdown(self, markdown_contents: List[str], output_dir: str, 
                          filenames: Optional[List[str]] = None) -> List[str]:
        """批量保存Markdown内容到文件"""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        saved_paths = []
        for i, content in enumerate(markdown_contents):
            if filenames and i < len(filenames):
                filename = filenames[i]
            else:
                filename = f"document_{i + 1}.md"
                
            output_path = output_dir / filename
            self.save_markdown(content, output_path)
            saved_paths.append(str(output_path))
            
        return saved_paths 
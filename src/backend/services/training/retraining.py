"""
模型自动重训练服务
用于自动化模型重训练流程，包括数据准备、训练触发和模型评估
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import os
import json
import shutil
from pathlib import Path
import subprocess
import tempfile
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.backend.logger import get_logger
from src.backend.config import settings
from src.backend.services.analysis.error import ErrorAnalysisService
from src.backend.services.data_transfer.export import DataExportService
from src.backend.services.models.onnx import ONNXModelManager

logger = get_logger(__name__)

class ModelRetrainingService:
    """模型自动重训练服务类"""
    
    def __init__(self):
        """初始化重训练服务"""
        self.error_analysis = ErrorAnalysisService()
        self.data_export = DataExportService()
        self.model_manager = ONNXModelManager()
        
        # 重训练触发条件
        self.retraining_triggers = {
            "error_rate_threshold": 0.1,  # 错误率阈值
            "sample_count_threshold": 1000,  # 新样本数量阈值
            "confidence_threshold": 0.8,  # 置信度阈值
            "min_days_between_training": 7  # 两次训练的最小间隔天数
        }
        
        # 确保工作目录存在
        self.work_dir = Path(settings.TRAINING_WORK_DIR)
        self.work_dir.mkdir(parents=True, exist_ok=True)
    
    async def check_retraining_needed(self) -> bool:
        """
        检查是否需要重新训练模型
        
        Returns:
            bool: 是否需要重训练
        """
        # 获取最近的错误分析报告
        report = self.error_analysis.analyze_errors(
            start_date=datetime.now() - timedelta(days=self.retraining_triggers["min_days_between_training"])
        )
        
        if not report or report["sample_count"] == 0:
            return False
        
        # 检查错误率
        if report["error_statistics"]["error_rate"] > self.retraining_triggers["error_rate_threshold"]:
            logger.info("Error rate exceeds threshold, retraining needed")
            return True
        
        # 检查样本数量
        if report["sample_count"] > self.retraining_triggers["sample_count_threshold"]:
            logger.info("New sample count exceeds threshold, retraining needed")
            return True
        
        # 检查低置信度样本
        low_conf_count = sum(
            dist["count"]
            for name, dist in report["confidence_analysis"].items()
            if name in ["very_low", "low"]
        )
        if low_conf_count > self.retraining_triggers["sample_count_threshold"] * 0.2:
            logger.info("Too many low confidence samples, retraining needed")
            return True
        
        return False
    
    async def prepare_training_data(self) -> Path:
        """
        准备训练数据
        
        Returns:
            Path: 训练数据目录路径
        """
        # 创建临时工作目录
        training_dir = self.work_dir / f"training_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        training_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            # 导出训练数据
            await self.data_export.export_training_data(
                output_dir=str(training_dir),
                include_images=True,
                format="paddleocr"
            )
            
            # 验证数据完整性
            if not self._validate_training_data(training_dir):
                raise ValueError("Training data validation failed")
            
            return training_dir
            
        except Exception as e:
            logger.error(f"Error preparing training data: {str(e)}")
            if training_dir.exists():
                shutil.rmtree(training_dir)
            raise
    
    def _validate_training_data(self, data_dir: Path) -> bool:
        """
        验证训练数据的完整性
        
        Args:
            data_dir: 训练数据目录
            
        Returns:
            bool: 数据是否有效
        """
        try:
            # 检查必要的文件和目录
            required_files = ["train.txt", "val.txt"]
            required_dirs = ["images", "labels"]
            
            for file in required_files:
                if not (data_dir / file).is_file():
                    logger.error(f"Missing required file: {file}")
                    return False
            
            for dir in required_dirs:
                if not (data_dir / dir).is_dir():
                    logger.error(f"Missing required directory: {dir}")
                    return False
            
            # 检查标注文件格式
            with open(data_dir / "train.txt") as f:
                for line in f:
                    parts = line.strip().split('\t')
                    if len(parts) != 2:
                        logger.error(f"Invalid annotation format: {line}")
                        return False
                    
                    image_path = data_dir / "images" / parts[0]
                    label_path = data_dir / "labels" / parts[1]
                    
                    if not image_path.is_file():
                        logger.error(f"Missing image file: {parts[0]}")
                        return False
                    if not label_path.is_file():
                        logger.error(f"Missing label file: {parts[1]}")
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating training data: {str(e)}")
            return False
    
    async def trigger_retraining(self, training_dir: Path) -> bool:
        """
        触发模型重训练
        
        Args:
            training_dir: 训练数据目录
            
        Returns:
            bool: 训练是否成功
        """
        try:
            # 准备训练配置
            config_path = self._prepare_training_config(training_dir)
            
            # 启动训练进程
            process = subprocess.Popen(
                [
                    "python", "-m", "paddleocr.tools.train",
                    "--config", str(config_path),
                    "--save_model_dir", str(training_dir / "output")
                ],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待训练完成
            stdout, stderr = process.communicate()
            
            if process.returncode != 0:
                logger.error(f"Training failed: {stderr.decode()}")
                return False
            
            # 验证训练结果
            if not self._validate_training_result(training_dir / "output"):
                return False
            
            # 转换为ONNX格式
            model_path = await self._convert_to_onnx(training_dir / "output")
            
            # 更新模型
            await self.model_manager.update_model(model_path)
            
            return True
            
        except Exception as e:
            logger.error(f"Error during model retraining: {str(e)}")
            return False
    
    def _prepare_training_config(self, training_dir: Path) -> Path:
        """
        准备训练配置文件
        
        Args:
            training_dir: 训练数据目录
            
        Returns:
            Path: 配置文件路径
        """
        config = {
            "Global": {
                "use_gpu": True,
                "epoch_num": 100,
                "log_smooth_window": 20,
                "print_batch_step": 10,
                "save_model_dir": str(training_dir / "output"),
                "save_epoch_step": 10,
                "eval_batch_step": [0, 2000],
                "cal_metric_during_train": True,
                "save_inference_dir": str(training_dir / "inference"),
                "use_visualdl": True,
                "seed": 2021
            },
            "Architecture": {
                "model_type": "det",
                "algorithm": "DB",
                "Transform": None,
                "Backbone": {
                    "name": "ResNet",
                    "layers": 50
                },
                "Neck": {
                    "name": "FPNC",
                    "out_channels": 256
                },
                "Head": {
                    "name": "DBHead",
                    "k": 50
                }
            },
            "Loss": {
                "name": "DBLoss",
                "balance_loss": True,
                "main_loss_type": "DiceLoss",
                "alpha": 5,
                "beta": 10,
                "ohem_ratio": 3
            },
            "Optimizer": {
                "name": "Adam",
                "beta1": 0.9,
                "beta2": 0.999,
                "lr": {
                    "name": "Cosine",
                    "learning_rate": 0.001,
                    "warmup_epoch": 2
                },
                "regularizer": {
                    "name": "L2",
                    "factor": 0.00001
                }
            },
            "Train": {
                "dataset": {
                    "name": "SimpleDataSet",
                    "data_dir": str(training_dir),
                    "label_file_list": [str(training_dir / "train.txt")],
                    "ratio_list": [1.0]
                },
                "loader": {
                    "shuffle": True,
                    "drop_last": False,
                    "batch_size_per_card": 16,
                    "num_workers": 8
                }
            },
            "Eval": {
                "dataset": {
                    "name": "SimpleDataSet",
                    "data_dir": str(training_dir),
                    "label_file_list": [str(training_dir / "val.txt")],
                    "ratio_list": [1.0]
                },
                "loader": {
                    "shuffle": False,
                    "drop_last": False,
                    "batch_size_per_card": 16,
                    "num_workers": 8
                }
            }
        }
        
        config_path = training_dir / "config.yml"
        with open(config_path, "w") as f:
            json.dump(config, f, indent=2)
        
        return config_path
    
    def _validate_training_result(self, output_dir: Path) -> bool:
        """
        验证训练结果
        
        Args:
            output_dir: 训练输出目录
            
        Returns:
            bool: 训练结果是否有效
        """
        try:
            # 检查必要的文件
            required_files = [
                "best_accuracy.pdparams",
                "config.yml",
                "train.log"
            ]
            
            for file in required_files:
                if not (output_dir / file).is_file():
                    logger.error(f"Missing required output file: {file}")
                    return False
            
            # 检查训练日志
            with open(output_dir / "train.log") as f:
                log_content = f.read()
                
                # 检查是否完成训练
                if "Training finished" not in log_content:
                    logger.error("Training did not complete successfully")
                    return False
                
                # 检查准确率
                if "best_accuracy" not in log_content:
                    logger.error("No accuracy information in training log")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating training result: {str(e)}")
            return False
    
    async def _convert_to_onnx(self, model_dir: Path) -> Path:
        """
        将PaddleOCR模型转换为ONNX格式
        
        Args:
            model_dir: 模型目录
            
        Returns:
            Path: ONNX模型路径
        """
        try:
            # 创建临时目录
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_dir = Path(temp_dir)
                
                # 转换模型
                process = subprocess.Popen(
                    [
                        "paddle2onnx",
                        "--model_dir", str(model_dir),
                        "--model_filename", "inference.pdmodel",
                        "--params_filename", "inference.pdiparams",
                        "--save_file", str(temp_dir / "model.onnx"),
                        "--opset_version", "11",
                        "--enable_onnx_checker", "True"
                    ],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                
                stdout, stderr = process.communicate()
                
                if process.returncode != 0:
                    raise RuntimeError(f"Model conversion failed: {stderr.decode()}")
                
                # 复制到目标位置
                target_path = self.work_dir / f"model_{datetime.now().strftime('%Y%m%d_%H%M%S')}.onnx"
                shutil.copy2(temp_dir / "model.onnx", target_path)
                
                return target_path
                
        except Exception as e:
            logger.error(f"Error converting model to ONNX: {str(e)}")
            raise
    
    async def run_retraining_pipeline(self) -> bool:
        """
        运行完整的重训练流程
        
        Returns:
            bool: 重训练是否成功
        """
        try:
            # 检查是否需要重训练
            if not await self.check_retraining_needed():
                logger.info("Retraining not needed at this time")
                return False
            
            # 准备训练数据
            logger.info("Preparing training data...")
            training_dir = await self.prepare_training_data()
            
            # 触发重训练
            logger.info("Starting model retraining...")
            success = await self.trigger_retraining(training_dir)
            
            if success:
                logger.info("Model retraining completed successfully")
            else:
                logger.error("Model retraining failed")
            
            # 清理工作目录
            if training_dir.exists():
                shutil.rmtree(training_dir)
            
            return success
            
        except Exception as e:
            logger.error(f"Error in retraining pipeline: {str(e)}")
            return False 
"""
负载测试服务
提供系统负载测试功能
"""

from typing import List, Dict, Any, Optional
import asyncio
import time
import aiohttp
import numpy as np
from PIL import Image
import io
import json
from datetime import datetime

from src.backend.logger import get_logger
from src.backend.config import settings

logger = get_logger(__name__)

class LoadTestingService:
    """负载测试服务类"""
    
    def __init__(self):
        """初始化服务"""
        self.config = settings
        # 使用默认值，因为这些是负载测试特定的配置
        self.base_url = f"http://localhost:{self.config.PROMETHEUS_PORT}"
        
        # 测试配置 - 使用默认值
        self.num_requests = 1000
        self.concurrent_users = 10
        self.ramp_up_time = 60
        
        # 测试结果
        self.results = []
        
    async def _generate_test_image(self) -> bytes:
        """
        生成测试图片
        
        Returns:
            bytes: 图片数据
        """
        # 创建示例图片
        image = Image.new("RGB", (640, 640))
        
        # 转换为字节
        buffer = io.BytesIO()
        image.save(buffer, format="PNG")
        
        return buffer.getvalue()
        
    async def _make_request(
        self,
        session: aiohttp.ClientSession,
        endpoint: str,
        method: str = "GET",
        data: Optional[Dict[str, Any]] = None,
        files: Optional[Dict[str, bytes]] = None
    ) -> Dict[str, Any]:
        """
        发送请求
        
        Args:
            session: HTTP会话
            endpoint: API端点
            method: 请求方法
            data: 请求数据
            files: 上传文件
            
        Returns:
            Dict[str, Any]: 响应结果
        """
        start_time = time.time()
        
        try:
            url = f"{self.base_url}{endpoint}"
            
            if files:
                # 构建multipart表单
                form = aiohttp.FormData()
                for name, content in files.items():
                    form.add_field(
                        name,
                        content,
                        filename=f"{name}.png",
                        content_type="image/png"
                    )
                    
                async with session.request(
                    method,
                    url,
                    data=form
                ) as response:
                    response_time = time.time() - start_time
                    status = response.status
                    response_data = await response.json()
                    
            else:
                async with session.request(
                    method,
                    url,
                    json=data
                ) as response:
                    response_time = time.time() - start_time
                    status = response.status
                    response_data = await response.json()
                    
            return {
                "success": True,
                "response_time": response_time,
                "status": status,
                "data": response_data
            }
            
        except Exception as e:
            return {
                "success": False,
                "response_time": time.time() - start_time,
                "error": str(e)
            }
            
    async def _run_user(
        self,
        user_id: int,
        delay: float
    ):
        """
        运行单个用户的测试
        
        Args:
            user_id: 用户ID
            delay: 启动延迟
        """
        # 等待启动延迟
        await asyncio.sleep(delay)
        
        async with aiohttp.ClientSession() as session:
            for i in range(self.num_requests // self.concurrent_users):
                # 生成测试图片
                image_data = await self._generate_test_image()
                
                # 测试OCR API
                ocr_result = await self._make_request(
                    session,
                    "/api/ocr/process",
                    "POST",
                    files={"file": image_data}
                )
                self.results.append({
                    "user_id": user_id,
                    "request_id": i,
                    "endpoint": "/api/ocr/process",
                    "timestamp": datetime.now().isoformat(),
                    **ocr_result
                })
                
                # 测试数据库API
                db_result = await self._make_request(
                    session,
                    "/api/db/stats/documents",
                    "GET"
                )
                self.results.append({
                    "user_id": user_id,
                    "request_id": i,
                    "endpoint": "/api/db/stats/documents",
                    "timestamp": datetime.now().isoformat(),
                    **db_result
                })
                
                # 测试模型API
                model_result = await self._make_request(
                    session,
                    "/api/model/stats",
                    "GET"
                )
                self.results.append({
                    "user_id": user_id,
                    "request_id": i,
                    "endpoint": "/api/model/stats",
                    "timestamp": datetime.now().isoformat(),
                    **model_result
                })
                
    async def run_test(self) -> Dict[str, Any]:
        """
        运行负载测试
        
        Returns:
            Dict[str, Any]: 测试结果
        """
        try:
            # 清空结果
            self.results = []
            
            # 计算每个用户的启动延迟
            delays = np.linspace(
                0,
                self.ramp_up_time,
                self.concurrent_users
            )
            
            # 创建用户任务
            tasks = [
                self._run_user(i, delay)
                for i, delay in enumerate(delays)
            ]
            
            # 运行测试
            start_time = time.time()
            await asyncio.gather(*tasks)
            total_time = time.time() - start_time
            
            # 分析结果
            response_times = [
                r["response_time"]
                for r in self.results
                if r["success"]
            ]
            
            success_rate = len([
                r for r in self.results
                if r["success"]
            ]) / len(self.results)
            
            return {
                "total_requests": len(self.results),
                "total_time": total_time,
                "requests_per_second": len(self.results) / total_time,
                "success_rate": success_rate,
                "response_times": {
                    "min": min(response_times),
                    "max": max(response_times),
                    "mean": np.mean(response_times),
                    "p50": np.percentile(response_times, 50),
                    "p90": np.percentile(response_times, 90),
                    "p95": np.percentile(response_times, 95),
                    "p99": np.percentile(response_times, 99)
                },
                "results": self.results
            }
            
        except Exception as e:
            logger.error(f"Load test failed: {str(e)}")
            raise 
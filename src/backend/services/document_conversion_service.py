from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import logging
import json
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
import pdfkit
import markdown
import mammoth
from bs4 import BeautifulSoup
import pandas as pd
from .document_style_service import DocumentStyleService

logger = logging.getLogger(__name__)

class DocumentConversionService:
    def __init__(self, style_service: Optional[DocumentStyleService] = None):
        """初始化文档转换服务"""
        self.style_service = style_service or DocumentStyleService()
        
    def merge_documents(self, documents: List[Dict[str, Any]], output_format: str = "json") -> Union[str, bytes, Dict]:
        """合并多个文档
        
        Args:
            documents: 要合并的文档列表
            output_format: 输出格式 ("json", "markdown", "html", "pdf", "docx")
            
        Returns:
            合并后的文档内容
        """
        # 合并文档内容
        merged_doc = {
            "title": "Merged Document",
            "metadata": self._merge_metadata([doc.get("metadata", {}) for doc in documents]),
            "content": []
        }
        
        for doc in documents:
            # 添加文档标题作为一级标题
            if "title" in doc:
                merged_doc["content"].append({
                    "type": "heading",
                    "level": 1,
                    "text": doc["title"]
                })
            
            # 添加文档内容
            merged_doc["content"].extend(doc.get("content", []))
            
            # 添加分隔符
            merged_doc["content"].append({
                "type": "horizontal_rule"
            })
        
        # 根据输出格式转换
        if output_format == "json":
            return merged_doc
        elif output_format == "markdown":
            return self.convert_to_markdown(merged_doc)
        elif output_format == "html":
            return self.convert_to_html(merged_doc)
        elif output_format == "pdf":
            return self.convert_to_pdf(merged_doc)
        elif output_format == "docx":
            return self.convert_to_docx(merged_doc)
        else:
            raise ValueError(f"Unsupported output format: {output_format}")
    
    def _merge_metadata(self, metadata_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """合并元数据"""
        merged = {}
        for metadata in metadata_list:
            for key, value in metadata.items():
                if key not in merged:
                    merged[key] = value
                elif isinstance(merged[key], list):
                    if isinstance(value, list):
                        merged[key].extend(value)
                    else:
                        merged[key].append(value)
                else:
                    merged[key] = [merged[key], value]
        return merged
    
    def convert_to_markdown(self, document: Dict[str, Any]) -> str:
        """转换为Markdown格式"""
        from .markdown_conversion_service import MarkdownConversionService
        markdown_service = MarkdownConversionService(self.style_service)
        return markdown_service.convert_to_markdown(document)
    
    def convert_to_html(self, document: Dict[str, Any]) -> str:
        """转换为HTML格式"""
        # 先转换为Markdown
        markdown_content = self.convert_to_markdown(document)
        
        # 转换为HTML
        html_content = markdown.markdown(
            markdown_content,
            extensions=['tables', 'fenced_code', 'codehilite']
        )
        
        # 添加样式
        template = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>{title}</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                table {{
                    border-collapse: collapse;
                    width: 100%;
                    margin: 20px 0;
                }}
                th, td {{
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }}
                th {{
                    background-color: #f5f5f5;
                }}
                code {{
                    background-color: #f8f8f8;
                    padding: 2px 4px;
                    border-radius: 4px;
                }}
                pre {{
                    background-color: #f8f8f8;
                    padding: 15px;
                    border-radius: 4px;
                    overflow-x: auto;
                }}
            </style>
        </head>
        <body>
            {content}
        </body>
        </html>
        """
        
        return template.format(
            title=document.get("title", "Document"),
            content=html_content
        )
    
    def convert_to_pdf(self, document: Dict[str, Any]) -> bytes:
        """转换为PDF格式"""
        # 先转换为HTML
        html_content = self.convert_to_html(document)
        
        # 转换为PDF
        pdf_content = pdfkit.from_string(
            html_content,
            False,  # 不保存到文件
            options={
                'encoding': 'UTF-8',
                'page-size': 'A4',
                'margin-top': '20mm',
                'margin-right': '20mm',
                'margin-bottom': '20mm',
                'margin-left': '20mm'
            }
        )
        
        return pdf_content
    
    def convert_to_docx(self, document: Dict[str, Any]) -> Document:
        """转换为Word格式"""
        doc = Document()
        
        # 设置文档标题
        if "title" in document:
            title = doc.add_heading(document["title"], 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加元数据
        if "metadata" in document:
            doc.add_heading("Metadata", level=1)
            for key, value in document["metadata"].items():
                p = doc.add_paragraph()
                p.add_run(f"{key}: ").bold = True
                p.add_run(str(value))
        
        # 处理内容
        for block in document.get("content", []):
            block_type = block.get("type", "text")
            
            if block_type == "heading":
                level = block.get("level", 1)
                text = block.get("text", "")
                doc.add_heading(text, level=level)
                
            elif block_type == "paragraph":
                text = block.get("text", "")
                doc.add_paragraph(text)
                
            elif block_type == "table":
                self._add_table_to_docx(doc, block)
                
            elif block_type == "list":
                self._add_list_to_docx(doc, block)
                
            elif block_type == "code":
                self._add_code_to_docx(doc, block)
                
            elif block_type == "image":
                self._add_image_to_docx(doc, block)
                
            elif block_type == "horizontal_rule":
                doc.add_paragraph("").add_run("─" * 50)
        
        return doc
    
    def _add_table_to_docx(self, doc: Document, table_block: Dict[str, Any]) -> None:
        """添加表格到Word文档"""
        headers = table_block.get("headers", [])
        rows = table_block.get("rows", [])
        
        if not headers or not rows:
            return
            
        # 创建表格
        table = doc.add_table(rows=1, cols=len(headers))
        table.style = "Table Grid"
        
        # 添加表头
        header_cells = table.rows[0].cells
        for i, header in enumerate(headers):
            header_cells[i].text = str(header)
            
        # 添加数据行
        for row_data in rows:
            row_cells = table.add_row().cells
            for i, cell in enumerate(row_data):
                row_cells[i].text = str(cell)
                
        doc.add_paragraph()  # 添加空行
    
    def _add_list_to_docx(self, doc: Document, list_block: Dict[str, Any]) -> None:
        """添加列表到Word文档"""
        items = list_block.get("items", [])
        list_type = list_block.get("type", "unordered")
        
        for index, item in enumerate(items, 1):
            p = doc.add_paragraph()
            if list_type == "ordered":
                p.add_run(f"{index}. ")
            else:
                p.add_run("• ")
            p.add_run(str(item))
    
    def _add_code_to_docx(self, doc: Document, code_block: Dict[str, Any]) -> None:
        """添加代码块到Word文档"""
        code = code_block.get("code", "")
        language = code_block.get("language", "")
        
        p = doc.add_paragraph()
        if language:
            p.add_run(f"Language: {language}\n").italic = True
        
        code_run = p.add_run(code)
        code_run.font.name = "Courier New"
        code_run.font.size = Pt(10)
        
        doc.add_paragraph()  # 添加空行
    
    def _add_image_to_docx(self, doc: Document, image_block: Dict[str, Any]) -> None:
        """添加图片到Word文档"""
        path = image_block.get("path", "")
        if not path:
            return
            
        try:
            doc.add_picture(path, width=Inches(6))
            
            # 添加图片标题
            if "title" in image_block:
                title_paragraph = doc.add_paragraph()
                title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                title_run = title_paragraph.add_run(image_block["title"])
                title_run.italic = True
                
            doc.add_paragraph()  # 添加空行
        except Exception as e:
            logger.error(f"Failed to add image {path}: {e}")
    
    def save_document(self, document: Union[str, bytes, Document], output_path: str) -> None:
        """保存文档到文件"""
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        if isinstance(document, str):
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(document)
        elif isinstance(document, bytes):
            with open(output_path, 'wb') as f:
                f.write(document)
        elif isinstance(document, Document):
            document.save(output_path)
        else:
            raise ValueError(f"Unsupported document type: {type(document)}")
    
    def batch_convert(self, documents: List[Dict[str, Any]], output_format: str,
                     output_dir: str, merge: bool = False) -> List[str]:
        """批量转换文档
        
        Args:
            documents: 要转换的文档列表
            output_format: 输出格式
            output_dir: 输出目录
            merge: 是否合并文档
            
        Returns:
            输出文件路径列表
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if merge:
            # 合并并转换
            merged_doc = self.merge_documents(documents, output_format)
            output_path = output_dir / f"merged.{output_format}"
            self.save_document(merged_doc, output_path)
            return [str(output_path)]
        else:
            # 单独转换每个文档
            output_paths = []
            for i, doc in enumerate(documents):
                converted_doc = self.merge_documents([doc], output_format)
                output_path = output_dir / f"document_{i + 1}.{output_format}"
                self.save_document(converted_doc, output_path)
                output_paths.append(str(output_path))
            return output_paths

    def convert_to_docx(self, content: Dict[str, Any]) -> Document:
        """将内容转换为Word文档格式
        
        Args:
            content: 要转换的内容
            
        Returns:
            Document: Word文档对象
        """
        doc = Document()
        
        # 添加标题
        if "title" in content:
            title = doc.add_heading(content["title"], 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
        # 处理内容
        for item in content["content"]:
            if item["type"] == "heading":
                doc.add_heading(item["text"], item.get("level", 1))
            elif item["type"] == "paragraph":
                doc.add_paragraph(item["text"])
            elif item["type"] == "table":
                table_data = item["data"]
                table = doc.add_table(rows=1, cols=len(table_data[0]))
                table.style = "Table Grid"
                
                # 添加表头
                for i, header in enumerate(table_data[0]):
                    table.cell(0, i).text = str(header)
                    
                # 添加数据行
                for row_data in table_data[1:]:
                    row_cells = table.add_row().cells
                    for i, cell_data in enumerate(row_data):
                        row_cells[i].text = str(cell_data)
                        
        return doc
        
    def convert_to_pdf(self, content: Dict[str, Any], output_path: str) -> str:
        """将内容转换为PDF格式
        
        Args:
            content: 要转换的内容
            output_path: PDF文件输出路径
            
        Returns:
            str: 生成的PDF文件路径
        """
        # 先转换为HTML
        html_content = self.convert_to_html(content)
        
        # 配置PDF转换选项
        options = {
            'page-size': 'A4',
            'margin-top': '0.75in',
            'margin-right': '0.75in',
            'margin-bottom': '0.75in',
            'margin-left': '0.75in',
            'encoding': "UTF-8",
        }
        
        # 转换为PDF
        pdfkit.from_string(html_content, output_path, options=options)
        return output_path
        
    def convert_to_html(self, content: Dict[str, Any]) -> str:
        """将内容转换为HTML格式
        
        Args:
            content: 要转换的内容
            
        Returns:
            str: HTML字符串
        """
        html = ["<!DOCTYPE html><html><head><meta charset='utf-8'></head><body>"]
        
        # 添加标题
        if "title" in content:
            html.append(f"<h1 style='text-align: center'>{content['title']}</h1>")
            
        # 处理内容
        for item in content["content"]:
            if item["type"] == "heading":
                level = item.get("level", 1)
                html.append(f"<h{level}>{item['text']}</h{level}>")
            elif item["type"] == "paragraph":
                html.append(f"<p>{item['text']}</p>")
            elif item["type"] == "table":
                table_data = item["data"]
                html.append("<table border='1' style='width:100%; border-collapse: collapse;'>")
                
                # 添加表头
                html.append("<thead><tr>")
                for header in table_data[0]:
                    html.append(f"<th style='padding: 8px'>{header}</th>")
                html.append("</tr></thead>")
                
                # 添加数据行
                html.append("<tbody>")
                for row_data in table_data[1:]:
                    html.append("<tr>")
                    for cell_data in row_data:
                        html.append(f"<td style='padding: 8px'>{cell_data}</td>")
                    html.append("</tr>")
                html.append("</tbody></table>")
                
        html.append("</body></html>")
        return "\n".join(html)
        
    def convert_to_excel(self, content: Dict[str, Any], output_path: str) -> str:
        """将内容转换为Excel格式
        
        Args:
            content: 要转换的内容
            output_path: Excel文件输出路径
            
        Returns:
            str: 生成的Excel文件路径
        """
        # 创建Excel写入器
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # 处理内容
            current_row = 0
            
            # 写入标题
            if "title" in content:
                df = pd.DataFrame([[content["title"]]])
                df.to_excel(writer, startrow=current_row, index=False, header=False)
                current_row += 2
                
            # 处理内容
            for item in content["content"]:
                if item["type"] == "table":
                    table_data = item["data"]
                    df = pd.DataFrame(table_data[1:], columns=table_data[0])
                    df.to_excel(writer, startrow=current_row, index=False)
                    current_row += len(table_data) + 2
                elif item["type"] in ["heading", "paragraph"]:
                    df = pd.DataFrame([[item["text"]]])
                    df.to_excel(writer, startrow=current_row, index=False, header=False)
                    current_row += 2
                    
        return output_path 
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from pathlib import Path
import json
import cv2
from PIL import Image
import pytesseract
from paddleocr import PaddleOCR
from sklearn.cluster import DBSCAN
from collections import defaultdict
import re

class DocumentPostprocessingService:
    def __init__(self):
        self.ocr = PaddleOCR(use_angle_cls=True, lang='ch')
        # 配置参数
        self.confidence_threshold = 0.5  # 置信度阈值
        self.merge_distance_threshold = 20  # 合并距离阈值
        self.angle_threshold = 5  # 倾斜角度阈值
        self.vertical_tolerance = 0.02  # 垂直位置容差（相对于图像高度）
        
    def optimize_detection_results(self, image_path: str, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        优化OCR检测结果
        
        1. 移除低置信度结果
        2. 合并相近文本框
        3. 修正倾斜文本
        4. 过滤重复检测
        5. 优化边界框
        6. 文本对齐校正
        """
        # 读取图像
        image = cv2.imread(str(image_path))
        height, width = image.shape[:2]
        
        # 移除低置信度结果
        filtered_results = [r for r in results if r['confidence'] > self.confidence_threshold]
        
        # 合并相近文本框
        merged_results = self._merge_nearby_boxes(filtered_results)
        
        # 修正倾斜文本
        corrected_results = self._correct_text_orientation(image, merged_results)
        
        # 过滤重复检测
        deduped_results = self._remove_duplicates(corrected_results)
        
        # 优化边界框
        optimized_results = self._optimize_bounding_boxes(image, deduped_results)
        
        # 文本对齐校正
        final_results = self._align_text(optimized_results, (height, width))
        
        return final_results
    
    def analyze_reading_order(self, results: List[Dict[str, Any]], image_size: tuple) -> List[Dict[str, Any]]:
        """
        分析文本的阅读顺序
        
        1. 基于位置关系进行排序
        2. 考虑文档结构（标题、段落等）
        3. 处理多列布局
        4. 识别阅读流程
        5. 处理表格和列表
        6. 考虑文本方向
        """
        img_height, img_width = image_size
        
        # 将结果按垂直位置分组
        tolerance = img_height * self.vertical_tolerance
        vertical_groups = self._group_by_vertical_position(results, tolerance)
        
        # 识别文档结构
        structure = self._identify_document_structure(vertical_groups)
        
        # 处理每个结构元素
        ordered_results = []
        for element in structure:
            element_type = element['type']
            content = element['content']
            
            if element_type == 'title':
                ordered_results.extend(content)
            elif element_type == 'paragraph':
                # 检查是否为多列布局
                if self._is_multi_column_layout(content, img_width):
                    columns = self._split_into_columns(content, img_width)
                    for column in columns:
                        ordered_results.extend(self._order_paragraph_text(column))
                else:
                    ordered_results.extend(self._order_paragraph_text(content))
            elif element_type == 'table':
                ordered_results.extend(self._order_table_content(content))
            elif element_type == 'list':
                ordered_results.extend(self._order_list_items(content))
        
        return ordered_results
    
    def organize_text_content(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        组织文本内容
        
        1. 识别文档结构（标题、段落、列表等）
        2. 提取关键信息
        3. 格式化文本
        4. 语义分析
        5. 内容分类
        6. 关键信息提取
        """
        document_structure = {
            'title': None,
            'headings': [],
            'paragraphs': [],
            'lists': [],
            'tables': [],
            'metadata': {},
            'key_info': {},
            'semantic_blocks': []
        }
        
        # 初始化处理状态
        current_block = {
            'type': None,
            'content': [],
            'metadata': {}
        }
        
        for result in results:
            text = result['text']
            confidence = result['confidence']
            box = result['box']
            
            # 识别文本块类型
            block_type = self._identify_text_block_type(result, results)
            
            # 处理不同类型的文本块
            if block_type != current_block['type'] and current_block['content']:
                # 保存当前块并开始新块
                self._save_text_block(document_structure, current_block)
                current_block = {'type': block_type, 'content': [], 'metadata': {}}
            
            # 更新当前块
            current_block['type'] = block_type
            current_block['content'].append(result)
            
            # 提取关键信息
            key_info = self._extract_key_info(text)
            if key_info:
                document_structure['key_info'].update(key_info)
        
        # 保存最后一个块
        if current_block['content']:
            self._save_text_block(document_structure, current_block)
        
        # 进行语义分析
        document_structure['semantic_blocks'] = self._analyze_semantics(document_structure)
        
        return document_structure
    
    def _optimize_bounding_boxes(self, image: np.ndarray, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """优化文本框边界"""
        optimized_results = []
        
        for result in results:
            box = np.array(result['box'])
            
            # 使用轮廓检测优化边界
            mask = np.zeros(image.shape[:2], dtype=np.uint8)
            cv2.fillPoly(mask, [box.astype(np.int32)], 255)
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if contours:
                # 获取最小外接矩形
                rect = cv2.minAreaRect(contours[0])
                optimized_box = cv2.boxPoints(rect)
                
                # 更新结果
                result['box'] = optimized_box.tolist()
                optimized_results.append(result)
        
        return optimized_results
    
    def _align_text(self, results: List[Dict[str, Any]], image_size: Tuple[int, int]) -> List[Dict[str, Any]]:
        """文本对齐校正"""
        height, width = image_size
        aligned_results = []
        
        # 使用DBSCAN聚类找出文本行
        points = np.array([result['box'][0] for result in results])
        clustering = DBSCAN(eps=height*0.02, min_samples=2).fit(points)
        
        # 按行处理文本
        lines = defaultdict(list)
        for label, result in zip(clustering.labels_, results):
            if label >= 0:  # 忽略噪声点
                lines[label].append(result)
        
        # 对每行文本进行对齐
        for line in lines.values():
            # 计算行的平均y坐标
            avg_y = np.mean([np.mean([p[1] for p in r['box']]) for r in line])
            
            # 对齐到平均y坐标
            for result in line:
                box = np.array(result['box'])
                box[:, 1] = avg_y
                result['box'] = box.tolist()
                aligned_results.append(result)
        
        # 处理未分组的文本
        noise_points = [results[i] for i, label in enumerate(clustering.labels_) if label == -1]
        aligned_results.extend(noise_points)
        
        return aligned_results
    
    def _identify_document_structure(self, vertical_groups: List[List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """识别文档结构"""
        structure = []
        
        for group in vertical_groups:
            # 分析组内文本特征
            avg_height = np.mean([self._get_text_height(r['box']) for r in group])
            avg_width = np.mean([self._get_text_width(r['box']) for r in group])
            text_density = len(''.join([r['text'] for r in group])) / (avg_width * avg_height)
            
            # 基于特征识别结构类型
            if self._is_title(group[0], vertical_groups[0]):
                structure.append({'type': 'title', 'content': group})
            elif self._is_table_content(group[0], group):
                structure.append({'type': 'table', 'content': group})
            elif self._is_list_group(group):
                structure.append({'type': 'list', 'content': group})
            else:
                structure.append({'type': 'paragraph', 'content': group})
        
        return structure
    
    def _identify_text_block_type(self, result: Dict[str, Any], all_results: List[Dict[str, Any]]) -> str:
        """识别文本块类型"""
        text = result['text']
        box = result['box']
        
        # 计算文本特征
        text_height = self._get_text_height(box)
        text_width = self._get_text_width(box)
        char_density = len(text) / (text_width * text_height)
        
        # 识别标题
        if self._is_title(result, all_results):
            return 'title'
        
        # 识别列表项
        if self._is_list_item(text):
            return 'list_item'
        
        # 识别表格内容
        if self._is_table_content(result, all_results):
            return 'table_cell'
        
        # 识别段落文本
        if char_density > 0.1:  # 文本密度阈值
            return 'paragraph'
        
        return 'other'
    
    def _extract_key_info(self, text: str) -> Dict[str, Any]:
        """提取关键信息"""
        key_info = {}
        
        # 日期匹配
        date_pattern = r'\d{4}[-/年]\d{1,2}[-/月]\d{1,2}日?'
        dates = re.findall(date_pattern, text)
        if dates:
            key_info['dates'] = dates
        
        # 金额匹配
        amount_pattern = r'¥?\d+(\.\d{2})?元?'
        amounts = re.findall(amount_pattern, text)
        if amounts:
            key_info['amounts'] = amounts
        
        # 编号匹配
        id_pattern = r'[A-Z0-9]{5,}'
        ids = re.findall(id_pattern, text)
        if ids:
            key_info['ids'] = ids
        
        return key_info
    
    def _analyze_semantics(self, document_structure: Dict[str, Any]) -> List[Dict[str, Any]]:
        """分析文本语义"""
        semantic_blocks = []
        
        # 处理每个文本块
        for block_type in ['paragraphs', 'lists', 'tables']:
            for content in document_structure[block_type]:
                if isinstance(content, str):
                    # 分析文本语义
                    sentiment = self._analyze_sentiment(content)
                    keywords = self._extract_keywords(content)
                    
                    semantic_blocks.append({
                        'type': block_type.rstrip('s'),
                        'content': content,
                        'sentiment': sentiment,
                        'keywords': keywords
                    })
                elif isinstance(content, list):
                    # 处理列表或表格
                    combined_text = ' '.join([item for item in content if isinstance(item, str)])
                    sentiment = self._analyze_sentiment(combined_text)
                    keywords = self._extract_keywords(combined_text)
                    
                    semantic_blocks.append({
                        'type': block_type.rstrip('s'),
                        'content': content,
                        'sentiment': sentiment,
                        'keywords': keywords
                    })
        
        return semantic_blocks
    
    def _analyze_sentiment(self, text: str) -> Dict[str, float]:
        """分析文本情感"""
        # 简单的情感分析实现
        positive_words = {'优秀', '良好', '满意', '同意', '批准'}
        negative_words = {'差', '不满', '拒绝', '否决', '问题'}
        
        text_chars = set(text)
        positive_score = len(text_chars & positive_words) / len(text)
        negative_score = len(text_chars & negative_words) / len(text)
        
        return {
            'positive': positive_score,
            'negative': negative_score,
            'neutral': 1 - (positive_score + negative_score)
        }
    
    def _extract_keywords(self, text: str, top_k: int = 5) -> List[str]:
        """提取关键词"""
        # 简单的关键词提取实现
        # 在实际项目中可以使用更复杂的算法，如TF-IDF或TextRank
        words = text.split()
        word_freq = defaultdict(int)
        
        for word in words:
            if len(word) > 1:  # 忽略单字符
                word_freq[word] += 1
        
        # 返回出现频率最高的top_k个词
        return sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:top_k]
    
    def _get_text_height(self, box: List[List[float]]) -> float:
        """计算文本高度"""
        box = np.array(box)
        return np.max(box[:, 1]) - np.min(box[:, 1])
    
    def _get_text_width(self, box: List[List[float]]) -> float:
        """计算文本宽度"""
        box = np.array(box)
        return np.max(box[:, 0]) - np.min(box[:, 0])
    
    def _is_list_group(self, group: List[Dict[str, Any]]) -> bool:
        """判断是否为列表组"""
        # 检查组内文本是否符合列表特征
        list_item_count = sum(1 for r in group if self._is_list_item(r['text']))
        return list_item_count > len(group) * 0.5  # 超过半数项符合列表特征
    
    def _merge_nearby_boxes(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """合并相近的文本框"""
        if not results:
            return results
            
        merged = []
        used = set()
        
        for i, result1 in enumerate(results):
            if i in used:
                continue
                
            current_group = [result1]
            used.add(i)
            
            for j, result2 in enumerate(results[i+1:], i+1):
                if j in used:
                    continue
                    
                # 检查两个文本框是否足够近
                if self._are_boxes_nearby(result1['box'], result2['box']):
                    current_group.append(result2)
                    used.add(j)
            
            # 合并当前组的文本框
            if len(current_group) > 1:
                merged.append(self._merge_text_boxes(current_group))
            else:
                merged.append(result1)
        
        return merged
    
    def _correct_text_orientation(self, image: np.ndarray, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """修正文本方向"""
        corrected_results = []
        
        for result in results:
            box = np.array(result['box'])
            
            # 计算文本框的倾斜角度
            angle = self._calculate_text_angle(box)
            
            # 如果倾斜角度超过阈值，进行修正
            if abs(angle) > 5:  # 5度阈值
                # 获取文本框区域
                rect = cv2.minAreaRect(box)
                box_w, box_h = rect[1]
                box_angle = rect[2]
                
                # 旋转文本框区域
                M = cv2.getRotationMatrix2D(rect[0], box_angle, 1)
                rotated_box = cv2.transform(np.array([box]), M)[0]
                
                # 更新结果
                result['box'] = rotated_box.tolist()
            
            corrected_results.append(result)
        
        return corrected_results
    
    def _remove_duplicates(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """移除重复的检测结果"""
        unique_results = []
        seen_texts = set()
        
        for result in results:
            text = result['text'].strip()
            if text and text not in seen_texts:
                seen_texts.add(text)
                unique_results.append(result)
        
        return unique_results
    
    def _group_by_vertical_position(self, results: List[Dict[str, Any]], tolerance: float) -> List[List[Dict[str, Any]]]:
        """按垂直位置分组"""
        if not results:
            return []
            
        # 按y坐标排序
        sorted_results = sorted(results, key=lambda x: x['box'][0][1])
        
        groups = []
        current_group = [sorted_results[0]]
        current_y = sorted_results[0]['box'][0][1]
        
        for result in sorted_results[1:]:
            y = result['box'][0][1]
            
            # 如果y坐标在容差范围内，加入当前组
            if abs(y - current_y) <= tolerance:
                current_group.append(result)
            else:
                # 开始新的一组
                groups.append(current_group)
                current_group = [result]
                current_y = y
        
        # 添加最后一组
        if current_group:
            groups.append(current_group)
        
        return groups
    
    def _is_multi_column_layout(self, group: List[Dict[str, Any]], image_width: int) -> bool:
        """检测是否为多列布局"""
        if len(group) < 2:
            return False
            
        # 计算文本框的水平间距
        x_gaps = []
        sorted_boxes = sorted(group, key=lambda x: x['box'][0][0])
        
        for i in range(len(sorted_boxes) - 1):
            current_box = sorted_boxes[i]['box']
            next_box = sorted_boxes[i + 1]['box']
            gap = next_box[0][0] - current_box[1][0]
            x_gaps.append(gap)
        
        if not x_gaps:
            return False
            
        # 如果存在明显的间隔（大于平均间隔的2倍），认为是多列布局
        avg_gap = sum(x_gaps) / len(x_gaps)
        return any(gap > avg_gap * 2 for gap in x_gaps)
    
    def _split_into_columns(self, group: List[Dict[str, Any]], image_width: int) -> List[List[Dict[str, Any]]]:
        """将文本框分割成多列"""
        if not group:
            return []
            
        # 按x坐标排序
        sorted_boxes = sorted(group, key=lambda x: x['box'][0][0])
        
        columns = []
        current_column = [sorted_boxes[0]]
        last_x = sorted_boxes[0]['box'][1][0]
        
        for box in sorted_boxes[1:]:
            current_x = box['box'][0][0]
            
            # 如果与上一个文本框的距离过大，开始新的一列
            if current_x - last_x > image_width * 0.2:  # 20%图像宽度作为列间距阈值
                columns.append(current_column)
                current_column = []
            
            current_column.append(box)
            last_x = box['box'][1][0]
        
        # 添加最后一列
        if current_column:
            columns.append(current_column)
        
        return columns
    
    def _is_title(self, result: Dict[str, Any], all_results: List[Dict[str, Any]]) -> bool:
        """判断文本是否为标题"""
        # 基于以下特征判断：
        # 1. 位置靠上
        # 2. 字体较大
        # 3. 居中对齐
        # 4. 没有其他文本在其上方
        
        box = result['box']
        text = result['text']
        
        # 检查是否靠近顶部
        y_position = box[0][1]
        is_top = y_position < all_results[0]['box'][0][1] * 1.5
        
        # 检查是否居中
        center_x = (box[0][0] + box[1][0]) / 2
        text_width = box[1][0] - box[0][0]
        is_centered = abs(center_x - (all_results[-1]['box'][1][0] / 2)) < text_width * 0.2
        
        # 检查字体大小（通过文本框高度估算）
        text_height = box[2][1] - box[0][1]
        avg_height = sum(r['box'][2][1] - r['box'][0][1] for r in all_results) / len(all_results)
        is_large = text_height > avg_height * 1.2
        
        return (is_top and is_centered) or (is_large and len(text) < 50)
    
    def _is_list_item(self, text: str) -> bool:
        """判断文本是否为列表项"""
        # 检查常见的列表标记
        list_markers = [
            r'^\d+\.',  # 数字编号
            r'^[a-zA-Z]\.',  # 字母编号
            r'^[-•\*]',  # 无序列表标记
            r'^[\(（]\d+[\)）]',  # 带括号的数字
            r'^第[一二三四五六七八九十]+[条章节]',  # 中文编号
        ]
        
        import re
        return any(re.match(pattern, text.strip()) for pattern in list_markers)
    
    def _is_table_content(self, result: Dict[str, Any], all_results: List[Dict[str, Any]]) -> bool:
        """判断文本是否为表格内容"""
        # 基于以下特征判断：
        # 1. 文本框对齐
        # 2. 固定间距
        # 3. 网格结构
        
        box = result['box']
        
        # 查找周围的文本框
        nearby_boxes = []
        for other in all_results:
            if other != result:
                other_box = other['box']
                # 检查是否在同一行或同一列
                if (abs(box[0][1] - other_box[0][1]) < 10 or  # 同一行
                    abs(box[0][0] - other_box[0][0]) < 10):   # 同一列
                    nearby_boxes.append(other_box)
        
        if len(nearby_boxes) < 2:
            return False
            
        # 检查对齐和间距
        x_coords = [b[0][0] for b in nearby_boxes]
        y_coords = [b[0][1] for b in nearby_boxes]
        
        # 计算间距标准差
        x_diffs = np.diff(sorted(x_coords))
        y_diffs = np.diff(sorted(y_coords))
        
        # 如果间距比较均匀（标准差小），可能是表格
        return (len(x_diffs) > 0 and np.std(x_diffs) < 5) or (len(y_diffs) > 0 and np.std(y_diffs) < 5)
    
    def _are_boxes_nearby(self, box1: List[List[float]], box2: List[List[float]], threshold: float = 20) -> bool:
        """判断两个文本框是否相近"""
        # 计算两个框的中心点
        center1 = np.mean(box1, axis=0)
        center2 = np.mean(box2, axis=0)
        
        # 计算距离
        distance = np.linalg.norm(center1 - center2)
        
        return distance < threshold
    
    def _merge_text_boxes(self, boxes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """合并多个文本框"""
        if not boxes:
            return None
            
        # 合并文本
        texts = [box['text'] for box in boxes]
        merged_text = ' '.join(texts)
        
        # 合并置信度（取平均值）
        confidences = [box['confidence'] for box in boxes]
        merged_confidence = sum(confidences) / len(confidences)
        
        # 合并边界框（取最外层边界）
        all_points = np.vstack([np.array(box['box']) for box in boxes])
        min_x = np.min(all_points[:, 0])
        min_y = np.min(all_points[:, 1])
        max_x = np.max(all_points[:, 0])
        max_y = np.max(all_points[:, 1])
        
        merged_box = [
            [min_x, min_y],  # 左上
            [max_x, min_y],  # 右上
            [max_x, max_y],  # 右下
            [min_x, max_y]   # 左下
        ]
        
        return {
            'text': merged_text,
            'confidence': merged_confidence,
            'box': merged_box
        }
    
    def _calculate_text_angle(self, box: np.ndarray) -> float:
        """计算文本框的倾斜角度"""
        # 计算文本框的主方向
        rect = cv2.minAreaRect(box)
        angle = rect[2]
        
        # 标准化角度到 -45 到 45 度之间
        if angle < -45:
            angle += 90
        elif angle > 45:
            angle -= 90
            
        return angle 
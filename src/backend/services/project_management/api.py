#!/usr/bin/env python3
"""
项目管理API接口
提供完整的项目管理RESTful API
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
from datetime import datetime

from .project_management_ui import get_project_management_ui

router = APIRouter(prefix="/api/project-management", tags=["项目管理"])

# Pydantic模型
class ProjectCreateRequest(BaseModel):
    """项目创建请求"""
    name: str = Field(..., description="项目名称")
    description: str = Field("", description="项目描述")
    type: str = Field("detection", description="项目类型")
    template_id: Optional[str] = Field(None, description="项目模板ID")
    members: List[str] = Field([], description="项目成员ID列表")
    collaborators: List[str] = Field([], description="协作者ID列表")
    settings: Dict[str, Any] = Field({}, description="项目设置")
    workflow: Dict[str, Any] = Field({}, description="工作流配置")

class ProjectUpdateRequest(BaseModel):
    """项目更新请求"""
    name: Optional[str] = Field(None, description="项目名称")
    description: Optional[str] = Field(None, description="项目描述")
    type: Optional[str] = Field(None, description="项目类型")
    status: Optional[str] = Field(None, description="项目状态")
    members: Optional[List[str]] = Field(None, description="项目成员ID列表")
    collaborators: Optional[List[str]] = Field(None, description="协作者ID列表")
    settings: Optional[Dict[str, Any]] = Field(None, description="项目设置")
    workflow: Optional[Dict[str, Any]] = Field(None, description="工作流配置")

class DashboardResponse(BaseModel):
    """仪表板响应"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    message: Optional[str] = None

class ProjectListResponse(BaseModel):
    """项目列表响应"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    message: Optional[str] = None

class ProjectDetailsResponse(BaseModel):
    """项目详情响应"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    message: Optional[str] = None

class StandardResponse(BaseModel):
    """标准响应"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

@router.get("/dashboard", response_model=DashboardResponse)
async def get_dashboard():
    """
    获取仪表板数据
    
    返回用户的仪表板数据，包括：
    - 用户信息
    - 项目统计
    - 最近活动
    - 用户统计
    """
    try:
        ui = get_project_management_ui()
        result = ui.get_dashboard_data()
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        return DashboardResponse(**result)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取仪表板数据失败: {str(e)}")

@router.get("/projects", response_model=ProjectListResponse)
async def get_projects(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(None, description="状态过滤")
):
    """
    获取项目列表
    
    支持分页和状态过滤
    """
    try:
        ui = get_project_management_ui()
        result = ui.get_project_list(
            page=page,
            page_size=page_size,
            status_filter=status
        )
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        return ProjectListResponse(**result)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目列表失败: {str(e)}")

@router.get("/projects/{project_id}", response_model=ProjectDetailsResponse)
async def get_project_details(project_id: str):
    """
    获取项目详情
    
    返回指定项目的详细信息，包括：
    - 基本信息
    - 统计数据
    - 成员信息
    - 最近活动
    """
    try:
        ui = get_project_management_ui()
        result = ui.get_project_details(project_id)
        
        if not result["success"]:
            if "不存在" in result["message"]:
                raise HTTPException(status_code=404, detail=result["message"])
            elif "无权限" in result["message"]:
                raise HTTPException(status_code=403, detail=result["message"])
            else:
                raise HTTPException(status_code=400, detail=result["message"])
        
        return ProjectDetailsResponse(**result)
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目详情失败: {str(e)}")

@router.post("/projects", response_model=StandardResponse)
async def create_project(project: ProjectCreateRequest):
    """
    创建新项目
    
    需要管理员或项目经理权限
    """
    try:
        ui = get_project_management_ui()
        result = ui.create_project(project.dict())
        
        if not result["success"]:
            if "无权限" in result["message"]:
                raise HTTPException(status_code=403, detail=result["message"])
            else:
                raise HTTPException(status_code=400, detail=result["message"])
        
        return StandardResponse(**result)
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建项目失败: {str(e)}")

@router.put("/projects/{project_id}", response_model=StandardResponse)
async def update_project(project_id: str, project: ProjectUpdateRequest):
    """
    更新项目
    
    需要管理员、项目经理或项目创建者权限
    """
    try:
        ui = get_project_management_ui()
        
        # 过滤掉None值
        update_dict = {k: v for k, v in project.dict().items() if v is not None}
        
        result = ui.update_project(project_id, update_dict)
        
        if not result["success"]:
            if "不存在" in result["message"]:
                raise HTTPException(status_code=404, detail=result["message"])
            elif "无权限" in result["message"]:
                raise HTTPException(status_code=403, detail=result["message"])
            else:
                raise HTTPException(status_code=400, detail=result["message"])
        
        return StandardResponse(**result)
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新项目失败: {str(e)}")

@router.delete("/projects/{project_id}", response_model=StandardResponse)
async def delete_project(project_id: str):
    """
    删除项目
    
    需要管理员权限或项目创建者权限
    警告：此操作将删除项目及其所有相关数据，不可恢复
    """
    try:
        ui = get_project_management_ui()
        result = ui.delete_project(project_id)
        
        if not result["success"]:
            if "不存在" in result["message"]:
                raise HTTPException(status_code=404, detail=result["message"])
            elif "无权限" in result["message"]:
                raise HTTPException(status_code=403, detail=result["message"])
            else:
                raise HTTPException(status_code=400, detail=result["message"])
        
        return StandardResponse(**result)
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除项目失败: {str(e)}")

@router.get("/projects/{project_id}/statistics")
async def get_project_statistics(project_id: str):
    """
    获取项目统计信息
    
    返回项目的详细统计数据
    """
    try:
        ui = get_project_management_ui()
        result = ui.get_project_details(project_id)
        
        if not result["success"]:
            if "不存在" in result["message"]:
                raise HTTPException(status_code=404, detail=result["message"])
            elif "无权限" in result["message"]:
                raise HTTPException(status_code=403, detail=result["message"])
            else:
                raise HTTPException(status_code=400, detail=result["message"])
        
        # 只返回统计信息
        statistics = result["data"]["statistics"]
        
        return {
            "success": True,
            "data": statistics
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目统计失败: {str(e)}")

@router.get("/projects/{project_id}/members")
async def get_project_members(project_id: str):
    """
    获取项目成员信息
    
    返回项目的所有成员及其统计信息
    """
    try:
        ui = get_project_management_ui()
        result = ui.get_project_details(project_id)
        
        if not result["success"]:
            if "不存在" in result["message"]:
                raise HTTPException(status_code=404, detail=result["message"])
            elif "无权限" in result["message"]:
                raise HTTPException(status_code=403, detail=result["message"])
            else:
                raise HTTPException(status_code=400, detail=result["message"])
        
        # 只返回成员信息
        members = result["data"]["members"]
        
        return {
            "success": True,
            "data": {
                "members": members,
                "total_count": len(members)
            }
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目成员失败: {str(e)}")

@router.get("/projects/{project_id}/activity")
async def get_project_activity(
    project_id: str,
    limit: int = Query(20, ge=1, le=100, description="返回数量限制")
):
    """
    获取项目活动记录
    
    返回项目的最近活动记录
    """
    try:
        ui = get_project_management_ui()
        result = ui.get_project_details(project_id)
        
        if not result["success"]:
            if "不存在" in result["message"]:
                raise HTTPException(status_code=404, detail=result["message"])
            elif "无权限" in result["message"]:
                raise HTTPException(status_code=403, detail=result["message"])
            else:
                raise HTTPException(status_code=400, detail=result["message"])
        
        # 只返回活动记录
        activity = result["data"]["recent_activity"][:limit]
        
        return {
            "success": True,
            "data": {
                "activities": activity,
                "count": len(activity)
            }
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目活动失败: {str(e)}")

@router.get("/user/statistics")
async def get_user_statistics():
    """
    获取当前用户的统计信息
    
    返回用户的个人统计数据
    """
    try:
        ui = get_project_management_ui()
        result = ui.get_dashboard_data()
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        # 只返回用户统计
        user_stats = result["data"]["user_stats"]
        
        return {
            "success": True,
            "data": user_stats
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户统计失败: {str(e)}")

@router.get("/overview")
async def get_system_overview():
    """
    获取系统概览
    
    返回系统级别的统计信息（仅管理员可访问）
    """
    try:
        # 检查管理员权限
        if not get_project_management_ui().check_admin_permission():
            raise HTTPException(status_code=403, detail="需要管理员权限")
        
        ui = get_project_management_ui()
        
        # 获取系统级统计
        from pymongo import MongoClient
        client = MongoClient("mongodb://localhost:27017/")
        db = client.agent_test
        
        total_users = db.users.count_documents({})
        total_projects = db.annotation_projects.count_documents({})
        total_images = db.annotation_images.count_documents({})
        
        # 按状态统计项目
        project_pipeline = [
            {"$group": {"_id": "$status", "count": {"$sum": 1}}}
        ]
        project_stats = {item["_id"]: item["count"] for item in db.annotation_projects.aggregate(project_pipeline)}
        
        # 按角色统计用户
        user_pipeline = [
            {"$group": {"_id": "$role", "count": {"$sum": 1}}}
        ]
        user_stats = {item["_id"]: item["count"] for item in db.users.aggregate(user_pipeline)}
        
        # 按状态统计图像
        image_pipeline = [
            {"$group": {"_id": "$status", "count": {"$sum": 1}}}
        ]
        image_stats = {item["_id"]: item["count"] for item in db.annotation_images.aggregate(image_pipeline)}
        
        return {
            "success": True,
            "data": {
                "totals": {
                    "users": total_users,
                    "projects": total_projects,
                    "images": total_images
                },
                "breakdowns": {
                    "projects_by_status": project_stats,
                    "users_by_role": user_stats,
                    "images_by_status": image_stats
                }
            }
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统概览失败: {str(e)}") 
#!/usr/bin/env python3
"""
项目模板管理系统
支持检测和分类标注项目的模板配置和工作流管理
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from enum import Enum
import json
from pathlib import Path

from pymongo import MongoClient

class ProjectType(Enum):
    """项目类型枚举"""
    DETECTION = "detection"
    CLASSIFICATION = "classification"
    DETECTION_CLASSIFICATION = "detection_classification"
    OCR = "ocr"
    CUSTOM = "custom"

class AnnotationWorkflow(Enum):
    """标注工作流枚举"""
    SIMPLE = "simple"  # 简单流程：标注 -> 完成
    REVIEW = "review"  # 审核流程：标注 -> 审核 -> 完成

class ProjectTemplateManager:
    """项目模板管理器"""
    
    def __init__(self):
        # 连接MongoDB
        self.client = MongoClient("mongodb://localhost:27017/")
        self.db = self.client.agent_test
        self._init_default_templates()
    
    def _init_default_templates(self):
        """初始化默认项目模板"""
        default_templates = [
            {
                "name": "文档检测项目",
                "type": ProjectType.DETECTION.value,
                "description": "用于文档中文字区域检测的标注项目",
                "workflow": AnnotationWorkflow.REVIEW.value,
                "categories": [
                    {"id": 1, "name": "text", "color": "#FF0000", "description": "文字区域"},
                    {"id": 2, "name": "table", "color": "#00FF00", "description": "表格区域"},
                    {"id": 3, "name": "image", "color": "#0000FF", "description": "图像区域"}
                ],
                "annotation_config": {
                    "tools": ["rectangle", "polygon"],
                    "min_box_size": 10,
                    "max_annotations_per_image": 100,
                    "required_attributes": ["category", "confidence"]
                },
                "quality_config": {
                    "min_confidence": 0.8,
                    "require_review": True,
                    "auto_accept_threshold": 0.95
                },
                "export_formats": ["paddleocr", "coco", "yolo"],
                "is_default": True,
                "created_at": datetime.now()
            },
            {
                "name": "文档分类项目",
                "type": ProjectType.CLASSIFICATION.value,
                "description": "用于文档类型分类的标注项目",
                "workflow": AnnotationWorkflow.SIMPLE.value,
                "categories": [
                    {"id": 1, "name": "invoice", "color": "#FF6B6B", "description": "发票"},
                    {"id": 2, "name": "contract", "color": "#4ECDC4", "description": "合同"},
                    {"id": 3, "name": "receipt", "color": "#45B7D1", "description": "收据"},
                    {"id": 4, "name": "certificate", "color": "#96CEB4", "description": "证书"},
                    {"id": 5, "name": "other", "color": "#FFEAA7", "description": "其他"}
                ],
                "annotation_config": {
                    "tools": ["classification"],
                    "multi_class": False,
                    "required_attributes": ["category", "confidence"]
                },
                "quality_config": {
                    "min_confidence": 0.9,
                    "require_review": False,
                    "auto_accept_threshold": 0.95
                },
                "export_formats": ["paddleocr", "csv", "json"],
                "is_default": True,
                "created_at": datetime.now()
            },
            {
                "name": "检测+分类联合项目",
                "type": ProjectType.DETECTION_CLASSIFICATION.value,
                "description": "同时进行文字检测和文档分类的综合标注项目",
                "workflow": AnnotationWorkflow.REVIEW.value,
                "categories": [
                    # 检测类别
                    {"id": 1, "name": "text_block", "color": "#FF0000", "description": "文字块", "type": "detection"},
                    {"id": 2, "name": "title", "color": "#FF8C00", "description": "标题", "type": "detection"},
                    {"id": 3, "name": "table", "color": "#00FF00", "description": "表格", "type": "detection"},
                    # 分类类别
                    {"id": 11, "name": "invoice", "color": "#FF6B6B", "description": "发票", "type": "classification"},
                    {"id": 12, "name": "contract", "color": "#4ECDC4", "description": "合同", "type": "classification"},
                    {"id": 13, "name": "receipt", "color": "#45B7D1", "description": "收据", "type": "classification"}
                ],
                "annotation_config": {
                    "tools": ["rectangle", "polygon", "classification"],
                    "detection_config": {
                        "min_box_size": 10,
                        "max_annotations_per_image": 50
                    },
                    "classification_config": {
                        "multi_class": False,
                        "require_detection_first": True
                    },
                    "required_attributes": ["category", "confidence", "type"]
                },
                "quality_config": {
                    "min_confidence": 0.85,
                    "require_review": True,
                    "auto_accept_threshold": 0.92,
                    "detection_quality_threshold": 0.8,
                    "classification_quality_threshold": 0.9
                },
                "export_formats": ["paddleocr", "coco", "yolo", "custom"],
                "is_default": True,
                "created_at": datetime.now()
            }
        ]
        
        # 检查并插入默认模板
        for template in default_templates:
            existing = self.db.project_templates.find_one({
                "name": template["name"],
                "type": template["type"]
            })
            if not existing:
                self.db.project_templates.insert_one(template)
    
    def get_templates(self, project_type: Optional[str] = None) -> List[Dict]:
        """获取项目模板列表"""
        query = {}
        if project_type:
            query["type"] = project_type
        
        templates = list(self.db.project_templates.find(query))
        for template in templates:
            template["_id"] = str(template["_id"])
        
        return templates
    
    def get_template(self, template_id: str) -> Optional[Dict]:
        """获取特定项目模板"""
        from bson import ObjectId
        try:
            template = self.db.project_templates.find_one({"_id": ObjectId(template_id)})
            if template:
                template["_id"] = str(template["_id"])
            return template
        except Exception:
            return None
    
    def create_template(self, template_data: Dict) -> Dict:
        """创建新的项目模板"""
        try:
            # 验证必需字段
            required_fields = ["name", "type", "description", "workflow", "categories"]
            for field in required_fields:
                if field not in template_data:
                    return {"success": False, "message": f"缺少必需字段: {field}"}
            
            # 检查模板名称是否已存在
            existing = self.db.project_templates.find_one({
                "name": template_data["name"],
                "type": template_data["type"]
            })
            if existing:
                return {"success": False, "message": "模板名称已存在"}
            
            # 设置默认值
            template_data.setdefault("annotation_config", {})
            template_data.setdefault("quality_config", {})
            template_data.setdefault("export_formats", ["paddleocr"])
            template_data.setdefault("is_default", False)
            template_data["created_at"] = datetime.now()
            
            # 插入模板
            result = self.db.project_templates.insert_one(template_data)
            
            return {
                "success": True,
                "message": "模板创建成功",
                "template_id": str(result.inserted_id)
            }
        
        except Exception as e:
            return {"success": False, "message": f"创建模板失败: {str(e)}"}
    
    def update_template(self, template_id: str, update_data: Dict) -> Dict:
        """更新项目模板"""
        from bson import ObjectId
        try:
            # 移除不允许更新的字段
            update_data.pop("_id", None)
            update_data.pop("created_at", None)
            update_data["updated_at"] = datetime.now()
            
            result = self.db.project_templates.update_one(
                {"_id": ObjectId(template_id)},
                {"$set": update_data}
            )
            
            if result.matched_count == 0:
                return {"success": False, "message": "模板不存在"}
            
            return {"success": True, "message": "模板更新成功"}
        
        except Exception as e:
            return {"success": False, "message": f"更新模板失败: {str(e)}"}
    
    def delete_template(self, template_id: str) -> Dict:
        """删除项目模板"""
        from bson import ObjectId
        try:
            # 检查是否为默认模板
            template = self.db.project_templates.find_one({"_id": ObjectId(template_id)})
            if not template:
                return {"success": False, "message": "模板不存在"}
            
            if template.get("is_default", False):
                return {"success": False, "message": "不能删除默认模板"}
            
            # 检查是否有项目在使用此模板
            projects_using = self.db.annotation_projects.count_documents({"template_id": template_id})
            if projects_using > 0:
                return {"success": False, "message": f"有 {projects_using} 个项目正在使用此模板，无法删除"}
            
            result = self.db.project_templates.delete_one({"_id": ObjectId(template_id)})
            
            return {"success": True, "message": "模板删除成功"}
        
        except Exception as e:
            return {"success": False, "message": f"删除模板失败: {str(e)}"}
    
    def create_project_from_template(self, template_id: str, project_data: Dict) -> Dict:
        """基于模板创建项目"""
        try:
            # 获取模板
            template = self.get_template(template_id)
            if not template:
                return {"success": False, "message": "模板不存在"}
            
            # 验证项目数据
            required_fields = ["name", "description", "created_by"]
            for field in required_fields:
                if field not in project_data:
                    return {"success": False, "message": f"缺少必需字段: {field}"}
            
            # 检查项目名称是否已存在
            existing = self.db.annotation_projects.find_one({"name": project_data["name"]})
            if existing:
                return {"success": False, "message": "项目名称已存在"}
            
            # 创建项目
            new_project = {
                "name": project_data["name"],
                "description": project_data["description"],
                "type": template["type"],
                "template_id": template_id,
                "workflow": template["workflow"],
                "categories": template["categories"],
                "annotation_config": template["annotation_config"],
                "quality_config": template["quality_config"],
                "export_formats": template["export_formats"],
                "created_by": project_data["created_by"],
                "created_at": datetime.now(),
                "status": "active",
                "statistics": {
                    "total_images": 0,
                    "annotated_images": 0,
                    "reviewed_images": 0,
                    "completed_images": 0
                }
            }
            
            # 添加可选字段
            optional_fields = ["deadline", "priority", "tags"]
            for field in optional_fields:
                if field in project_data:
                    new_project[field] = project_data[field]
            
            result = self.db.annotation_projects.insert_one(new_project)
            
            return {
                "success": True,
                "message": "项目创建成功",
                "project_id": str(result.inserted_id)
            }
        
        except Exception as e:
            return {"success": False, "message": f"创建项目失败: {str(e)}"}
    
    def get_workflow_config(self, workflow: str) -> Dict:
        """获取工作流配置"""
        workflows = {
            AnnotationWorkflow.SIMPLE.value: {
                "name": "simple",
                "title": "简单流程",
                "states": [
                    {"name": "annotation", "title": "标注"},
                    {"name": "completed", "title": "完成"}
                ],
                "transitions": [
                    {
                        "from": "annotation",
                        "to": "completed",
                        "conditions": ["annotation_complete"]
                    }
                ]
            },
            AnnotationWorkflow.REVIEW.value: {
                "name": "review",
                "title": "审核流程",
                "states": [
                    {"name": "annotation", "title": "标注"},
                    {"name": "review", "title": "审核"},
                    {"name": "completed", "title": "完成"}
                ],
                "transitions": [
                    {
                        "from": "annotation",
                        "to": "review",
                        "conditions": ["annotation_complete"]
                    },
                    {
                        "from": "review",
                        "to": "completed",
                        "conditions": ["review_approved"]
                    },
                    {
                        "from": "review",
                        "to": "annotation",
                        "conditions": ["review_rejected"]
                    }
                ]
            }
        }
        
        return workflows.get(workflow, workflows[AnnotationWorkflow.SIMPLE.value])

# 全局实例
_template_manager = None

def get_template_manager() -> ProjectTemplateManager:
    """获取模板管理器实例"""
    global _template_manager
    if _template_manager is None:
        _template_manager = ProjectTemplateManager()
    return _template_manager 
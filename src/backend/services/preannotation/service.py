#!/usr/bin/env python3
"""
智能预标注系统
将PaddleOCR结果自动导入到PaddleLabel项目中，实现智能预标注功能
"""

import os
import json
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from pathlib import Path
import cv2
from PIL import Image

from pymongo import MongoClient
from paddlelabel_converter import PaddleLabelConverter
from project_templates import get_template_manager

class IntelligentPreAnnotation:
    """智能预标注系统"""
    
    def __init__(self):
        # 连接MongoDB
        self.client = MongoClient("mongodb://localhost:27017/")
        self.db = self.client.agent_test
        
        # 初始化转换器和模板管理器
        self.converter = PaddleLabelConverter()
        self.template_manager = get_template_manager()
        
        # 初始化PaddleOCR（延迟加载）
        self._ocr = None
    
    @property
    def ocr(self):
        """延迟加载PaddleOCR"""
        if self._ocr is None:
            try:
                from paddleocr import PaddleOCR
                self._ocr = PaddleOCR(
                    use_angle_cls=True,
                    lang='ch',
                    show_log=False,
                    use_gpu=True if os.environ.get('CUDA_VISIBLE_DEVICES') else False
                )
            except ImportError:
                print("警告: PaddleOCR未安装，预标注功能将受限")
                self._ocr = None
        return self._ocr
    
    def run_ocr_on_image(self, image_path: str) -> List[Dict]:
        """对图像运行OCR识别"""
        try:
            if not self.ocr:
                return []
            
            # 运行OCR
            result = self.ocr.ocr(image_path, cls=True)
            
            # 转换结果格式
            ocr_results = []
            if result and result[0]:
                for line in result[0]:
                    if len(line) >= 2:
                        bbox = line[0]  # 边界框坐标
                        text_info = line[1]  # 文本和置信度
                        
                        if len(text_info) >= 2:
                            text = text_info[0]
                            confidence = text_info[1]
                            
                            # 转换坐标格式
                            x_coords = [point[0] for point in bbox]
                            y_coords = [point[1] for point in bbox]
                            
                            ocr_results.append({
                                "text": text,
                                "confidence": float(confidence),
                                "bbox": {
                                    "x_min": min(x_coords),
                                    "y_min": min(y_coords),
                                    "x_max": max(x_coords),
                                    "y_max": max(y_coords)
                                },
                                "polygon": [[float(p[0]), float(p[1])] for p in bbox]
                            })
            
            return ocr_results
        
        except Exception as e:
            print(f"OCR识别失败: {e}")
            return []
    
    def classify_document(self, image_path: str, ocr_results: List[Dict]) -> Dict:
        """基于OCR结果进行文档分类"""
        try:
            # 简单的基于关键词的分类逻辑
            all_text = " ".join([result["text"] for result in ocr_results]).lower()
            
            # 分类规则
            classification_rules = {
                "invoice": ["发票", "invoice", "税务", "金额", "税号", "开票"],
                "contract": ["合同", "contract", "协议", "甲方", "乙方", "签署"],
                "receipt": ["收据", "receipt", "收款", "付款", "凭证"],
                "certificate": ["证书", "certificate", "认证", "颁发", "授予"],
                "other": []
            }
            
            # 计算每个类别的匹配分数
            scores = {}
            for category, keywords in classification_rules.items():
                if category == "other":
                    scores[category] = 0.1  # 默认分数
                else:
                    score = sum(1 for keyword in keywords if keyword in all_text)
                    scores[category] = score / len(keywords) if keywords else 0
            
            # 选择最高分数的类别
            best_category = max(scores, key=scores.get)
            confidence = min(scores[best_category], 1.0)
            
            # 如果所有分数都很低，归类为other
            if confidence < 0.2:
                best_category = "other"
                confidence = 0.5
            
            return {
                "category": best_category,
                "confidence": confidence,
                "scores": scores
            }
        
        except Exception as e:
            print(f"文档分类失败: {e}")
            return {
                "category": "other",
                "confidence": 0.5,
                "scores": {}
            }
    
    def create_preannotation_data(self, image_path: str, project_id: str) -> Dict:
        """为图像创建预标注数据"""
        try:
            # 获取项目信息
            project = self.db.annotation_projects.find_one({"_id": project_id})
            if not project:
                return {"success": False, "message": "项目不存在"}
            
            project_type = project.get("type", "detection")
            
            # 运行OCR
            ocr_results = self.run_ocr_on_image(image_path)
            
            # 获取图像信息
            image = Image.open(image_path)
            image_width, image_height = image.size
            
            # 创建预标注数据
            preannotation = {
                "image_path": image_path,
                "image_name": os.path.basename(image_path),
                "image_width": image_width,
                "image_height": image_height,
                "project_id": project_id,
                "project_type": project_type,
                "created_at": datetime.now(),
                "ocr_results": ocr_results,
                "annotations": []
            }
            
            # 根据项目类型生成标注
            if project_type in ["detection", "detection_classification"]:
                # 检测标注
                for i, ocr_result in enumerate(ocr_results):
                    bbox = ocr_result["bbox"]
                    annotation = {
                        "id": i + 1,
                        "type": "detection",
                        "category": "text",
                        "bbox": [
                            bbox["x_min"],
                            bbox["y_min"],
                            bbox["x_max"] - bbox["x_min"],
                            bbox["y_max"] - bbox["y_min"]
                        ],
                        "polygon": ocr_result["polygon"],
                        "text": ocr_result["text"],
                        "confidence": ocr_result["confidence"],
                        "source": "paddleocr"
                    }
                    preannotation["annotations"].append(annotation)
            
            if project_type in ["classification", "detection_classification"]:
                # 分类标注
                classification = self.classify_document(image_path, ocr_results)
                classification_annotation = {
                    "id": len(preannotation["annotations"]) + 1,
                    "type": "classification",
                    "category": classification["category"],
                    "confidence": classification["confidence"],
                    "scores": classification["scores"],
                    "source": "auto_classification"
                }
                preannotation["annotations"].append(classification_annotation)
            
            return {
                "success": True,
                "data": preannotation
            }
        
        except Exception as e:
            return {
                "success": False,
                "message": f"创建预标注数据失败: {str(e)}"
            }
    
    def import_preannotation_to_project(self, preannotation_data: Dict, project_id: str) -> Dict:
        """将预标注数据导入到项目中"""
        try:
            from bson import ObjectId
            
            # 验证项目存在
            project = self.db.annotation_projects.find_one({"_id": ObjectId(project_id)})
            if not project:
                return {"success": False, "message": "项目不存在"}
            
            # 检查图像是否已存在
            existing_image = self.db.annotation_images.find_one({
                "project_id": project_id,
                "image_name": preannotation_data["image_name"]
            })
            
            if existing_image:
                return {"success": False, "message": "图像已存在于项目中"}
            
            # 创建图像记录
            image_record = {
                "project_id": project_id,
                "image_name": preannotation_data["image_name"],
                "image_path": preannotation_data["image_path"],
                "image_width": preannotation_data["image_width"],
                "image_height": preannotation_data["image_height"],
                "status": "preannotated",
                "created_at": datetime.now(),
                "annotations": preannotation_data["annotations"],
                "ocr_results": preannotation_data["ocr_results"],
                "annotation_count": len(preannotation_data["annotations"]),
                "has_preannotation": True
            }
            
            # 插入图像记录
            result = self.db.annotation_images.insert_one(image_record)
            image_id = str(result.inserted_id)
            
            # 更新项目统计
            self.db.annotation_projects.update_one(
                {"_id": ObjectId(project_id)},
                {
                    "$inc": {
                        "statistics.total_images": 1,
                        "statistics.preannotated_images": 1
                    }
                }
            )
            
            return {
                "success": True,
                "message": "预标注数据导入成功",
                "image_id": image_id,
                "annotation_count": len(preannotation_data["annotations"])
            }
        
        except Exception as e:
            return {
                "success": False,
                "message": f"导入预标注数据失败: {str(e)}"
            }
    
    def batch_preannotate_images(self, image_paths: List[str], project_id: str) -> Dict:
        """批量预标注图像"""
        try:
            results = {
                "success": True,
                "total_images": len(image_paths),
                "processed_images": 0,
                "failed_images": 0,
                "results": []
            }
            
            for image_path in image_paths:
                try:
                    # 创建预标注数据
                    preannotation_result = self.create_preannotation_data(image_path, project_id)
                    
                    if preannotation_result["success"]:
                        # 导入到项目
                        import_result = self.import_preannotation_to_project(
                            preannotation_result["data"], 
                            project_id
                        )
                        
                        if import_result["success"]:
                            results["processed_images"] += 1
                            results["results"].append({
                                "image_path": image_path,
                                "status": "success",
                                "annotation_count": import_result["annotation_count"]
                            })
                        else:
                            results["failed_images"] += 1
                            results["results"].append({
                                "image_path": image_path,
                                "status": "failed",
                                "error": import_result["message"]
                            })
                    else:
                        results["failed_images"] += 1
                        results["results"].append({
                            "image_path": image_path,
                            "status": "failed",
                            "error": preannotation_result["message"]
                        })
                
                except Exception as e:
                    results["failed_images"] += 1
                    results["results"].append({
                        "image_path": image_path,
                        "status": "failed",
                        "error": str(e)
                    })
            
            return results
        
        except Exception as e:
            return {
                "success": False,
                "message": f"批量预标注失败: {str(e)}"
            }
    
    def get_preannotation_statistics(self, project_id: str) -> Dict:
        """获取项目的预标注统计信息"""
        try:
            from bson import ObjectId
            
            # 获取项目信息
            project = self.db.annotation_projects.find_one({"_id": ObjectId(project_id)})
            if not project:
                return {"success": False, "message": "项目不存在"}
            
            # 统计预标注图像
            total_images = self.db.annotation_images.count_documents({"project_id": project_id})
            preannotated_images = self.db.annotation_images.count_documents({
                "project_id": project_id,
                "has_preannotation": True
            })
            
            # 统计标注数量
            pipeline = [
                {"$match": {"project_id": project_id}},
                {"$group": {
                    "_id": None,
                    "total_annotations": {"$sum": "$annotation_count"},
                    "avg_annotations_per_image": {"$avg": "$annotation_count"}
                }}
            ]
            
            annotation_stats = list(self.db.annotation_images.aggregate(pipeline))
            total_annotations = annotation_stats[0]["total_annotations"] if annotation_stats else 0
            avg_annotations = annotation_stats[0]["avg_annotations_per_image"] if annotation_stats else 0
            
            # 统计置信度分布
            confidence_pipeline = [
                {"$match": {"project_id": project_id, "has_preannotation": True}},
                {"$unwind": "$annotations"},
                {"$group": {
                    "_id": None,
                    "avg_confidence": {"$avg": "$annotations.confidence"},
                    "min_confidence": {"$min": "$annotations.confidence"},
                    "max_confidence": {"$max": "$annotations.confidence"}
                }}
            ]
            
            confidence_stats = list(self.db.annotation_images.aggregate(confidence_pipeline))
            
            return {
                "success": True,
                "data": {
                    "project_id": project_id,
                    "project_name": project.get("name", ""),
                    "total_images": total_images,
                    "preannotated_images": preannotated_images,
                    "preannotation_rate": preannotated_images / total_images if total_images > 0 else 0,
                    "total_annotations": total_annotations,
                    "avg_annotations_per_image": round(avg_annotations, 2) if avg_annotations else 0,
                    "confidence_stats": {
                        "avg_confidence": round(confidence_stats[0]["avg_confidence"], 3) if confidence_stats else 0,
                        "min_confidence": round(confidence_stats[0]["min_confidence"], 3) if confidence_stats else 0,
                        "max_confidence": round(confidence_stats[0]["max_confidence"], 3) if confidence_stats else 0
                    } if confidence_stats else {}
                }
            }
        
        except Exception as e:
            return {
                "success": False,
                "message": f"获取统计信息失败: {str(e)}"
            }
    
    def export_preannotations(self, project_id: str, export_format: str = "paddleocr") -> Dict:
        """导出项目的预标注数据"""
        try:
            from bson import ObjectId
            
            # 获取项目信息
            project = self.db.annotation_projects.find_one({"_id": ObjectId(project_id)})
            if not project:
                return {"success": False, "message": "项目不存在"}
            
            # 获取所有预标注图像
            images = list(self.db.annotation_images.find({
                "project_id": project_id,
                "has_preannotation": True
            }))
            
            if not images:
                return {"success": False, "message": "没有预标注数据可导出"}
            
            # 转换为指定格式
            export_data = []
            for image in images:
                image_data = {
                    "image_name": image["image_name"],
                    "image_path": image["image_path"],
                    "width": image["image_width"],
                    "height": image["image_height"],
                    "annotations": image["annotations"]
                }
                export_data.append(image_data)
            
            # 使用转换器转换格式
            if export_format != "paddleocr":
                converted_data = self.converter.convert_format(
                    export_data, 
                    "paddleocr", 
                    export_format
                )
                if not converted_data["success"]:
                    return converted_data
                export_data = converted_data["data"]
            
            return {
                "success": True,
                "data": export_data,
                "format": export_format,
                "image_count": len(images),
                "annotation_count": sum(img["annotation_count"] for img in images)
            }
        
        except Exception as e:
            return {
                "success": False,
                "message": f"导出预标注数据失败: {str(e)}"
            }

# 全局实例
_preannotation_system = None

def get_preannotation_system() -> IntelligentPreAnnotation:
    """获取智能预标注系统实例"""
    global _preannotation_system
    if _preannotation_system is None:
        _preannotation_system = IntelligentPreAnnotation()
    return _preannotation_system 
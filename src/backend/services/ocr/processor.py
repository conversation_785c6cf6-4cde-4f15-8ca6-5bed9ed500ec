from pathlib import Path
from typing import List, Dict, Union, Tuple, Optional, Any
from paddleocr import PaddleOCR
try:
    from paddleocr import PPStructure
except ImportError:
    PPStructure = None
import cv2
import numpy as np
from PIL import Image
import fitz  # PyMuPDF for PDF processing
import logging
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import time
from pillow_heif import register_heif_opener
from .integrator import OCRAnnotationIntegrator
from .types import OCRResult

# 导入新的版面分析器
from .layout_analyzer import LayoutAnalyzer as AdvancedLayoutAnalyzer, LayoutAnalysisResult

# 注册HEIF/HEIC格式支持
register_heif_opener()

class LayoutAnalyzer:
    """版面分析器"""
    
    @staticmethod
    def detect_columns(image: np.ndarray) -> List[List[int]]:
        """
        检测文档中的列
        
        Args:
            image: OpenCV 图像对象
            
        Returns:
            List[List[int]]: 列边界坐标列表 [[x1,y1,x2,y2], ...]
        """
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 二值化
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        
        # 形态学操作，连接文本区域
        kernel = np.ones((30, 30), np.uint8)
        dilated = cv2.dilate(binary, kernel, iterations=1)
        
        # 查找轮廓
        contours, _ = cv2.findContours(dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 获取列边界
        columns = []
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            # 过滤掉太小的区域
            if w > image.shape[1] * 0.1 and h > image.shape[0] * 0.3:
                columns.append([x, y, x + w, y + h])
        
        # 按 x 坐标排序
        columns.sort(key=lambda x: x[0])
        return columns

    @staticmethod
    def detect_tables(image: np.ndarray) -> List[List[int]]:
        """
        检测文档中的表格区域
        
        Args:
            image: OpenCV 图像对象
            
        Returns:
            List[List[int]]: 表格边界坐标列表 [[x1,y1,x2,y2], ...]
        """
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 自适应二值化
        binary = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2
        )
        
        # 检测水平和垂直线
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
        vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 40))
        
        horizontal_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, horizontal_kernel)
        vertical_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, vertical_kernel)
        
        # 合并线条
        table_mask = cv2.add(horizontal_lines, vertical_lines)
        
        # 查找表格轮廓
        contours, _ = cv2.findContours(table_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 获取表格边界
        tables = []
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            # 过滤掉太小的区域
            if w > 100 and h > 100:
                tables.append([x, y, x + w, y + h])
        
        return tables

class ImagePreprocessor:
    """图像预处理器"""
    
    @staticmethod
    def enhance_image(image: np.ndarray) -> np.ndarray:
        """
        增强图像质量
        
        Args:
            image: OpenCV 图像对象
            
        Returns:
            np.ndarray: 增强后的图像
        """
        # 去噪
        denoised = cv2.fastNlMeansDenoisingColored(image)
        
        # 自适应直方图均衡化
        lab = cv2.cvtColor(denoised, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l = clahe.apply(l)
        enhanced = cv2.merge((l, a, b))
        enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        
        return enhanced
    
    @staticmethod
    def deskew(image: np.ndarray) -> np.ndarray:
        """
        校正图像倾斜
        
        Args:
            image: OpenCV 图像对象
            
        Returns:
            np.ndarray: 校正后的图像
        """
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 二值化
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        
        # 获取文本角度
        coords = np.column_stack(np.where(binary > 0))
        angle = cv2.minAreaRect(coords.astype(np.float32))[-1]
        
        # 校正角度
        if angle < -45:
            angle = 90 + angle
        
        # 旋转图像
        (h, w) = image.shape[:2]
        center = (w // 2, h // 2)
        M = cv2.getRotationMatrix2D(center, angle, 1.0)
        rotated = cv2.warpAffine(
            image, M, (w, h), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE
        )
        
        return rotated

    @staticmethod
    def remove_background(image: np.ndarray) -> np.ndarray:
        """
        移除图像背景
        
        Args:
            image: OpenCV 图像对象
            
        Returns:
            np.ndarray: 移除背景后的图像
        """
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 自适应阈值
        binary = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # 形态学操作
        kernel = np.ones((3, 3), np.uint8)
        morph = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        # 应用掩码
        result = cv2.bitwise_and(image, image, mask=morph)
        
        return result

    @staticmethod
    def sharpen(image: np.ndarray) -> np.ndarray:
        """
        锐化图像
        
        Args:
            image: OpenCV 图像对象
            
        Returns:
            np.ndarray: 锐化后的图像
        """
        # 创建锐化核
        kernel = np.array([[-1,-1,-1],
                         [-1, 9,-1],
                         [-1,-1,-1]])
        
        # 应用锐化
        sharpened = cv2.filter2D(image, -1, kernel)
        
        return sharpened

    @staticmethod
    def adjust_contrast(image: np.ndarray, alpha: float = 1.5, beta: int = 0) -> np.ndarray:
        """
        调整图像对比度和亮度
        
        Args:
            image: OpenCV 图像对象
            alpha: 对比度调整因子
            beta: 亮度调整值
            
        Returns:
            np.ndarray: 调整后的图像
        """
        adjusted = cv2.convertScaleAbs(image, alpha=alpha, beta=beta)
        return adjusted

    @staticmethod
    def remove_shadows(image: np.ndarray) -> np.ndarray:
        """
        移除图像阴影
        
        Args:
            image: OpenCV 图像对象
            
        Returns:
            np.ndarray: 移除阴影后的图像
        """
        # 分离RGB通道
        rgb_planes = cv2.split(image)
        
        result_planes = []
        for plane in rgb_planes:
            # 对每个通道应用形态学操作
            dilated = cv2.dilate(plane, np.ones((7,7), np.uint8))
            bg_img = cv2.medianBlur(dilated, 21)
            
            # 减去背景
            diff_img = 255 - cv2.absdiff(plane, bg_img)
            
            # 归一化
            norm_img = cv2.normalize(diff_img, None, alpha=0, beta=255, norm_type=cv2.NORM_MINMAX)
            result_planes.append(norm_img)
        
        # 合并通道
        result = cv2.merge(result_planes)
        return result

    @staticmethod
    def binarize(image: np.ndarray, adaptive: bool = True) -> np.ndarray:
        """
        图像二值化
        
        Args:
            image: OpenCV 图像对象
            adaptive: 是否使用自适应阈值
            
        Returns:
            np.ndarray: 二值化后的图像
        """
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        if adaptive:
            # 自适应阈值
            binary = cv2.adaptiveThreshold(
                gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
        else:
            # OTSU阈值
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return binary

    @staticmethod
    def remove_noise(image: np.ndarray, method: str = 'gaussian') -> np.ndarray:
        """
        去除图像噪声
        
        Args:
            image: OpenCV 图像对象
            method: 去噪方法，可选 'gaussian', 'median', 'bilateral', 'nlmeans'
            
        Returns:
            np.ndarray: 去噪后的图像
        """
        if method == 'gaussian':
            denoised = cv2.GaussianBlur(image, (5, 5), 0)
        elif method == 'median':
            denoised = cv2.medianBlur(image, 5)
        elif method == 'bilateral':
            denoised = cv2.bilateralFilter(image, 9, 75, 75)
        elif method == 'nlmeans':
            denoised = cv2.fastNlMeansDenoisingColored(image)
        else:
            raise ValueError(f"不支持的去噪方法: {method}")
        
        return denoised

    @staticmethod
    def enhance_text(image: np.ndarray) -> np.ndarray:
        """
        增强文本区域
        
        Args:
            image: OpenCV 图像对象
            
        Returns:
            np.ndarray: 增强后的图像
        """
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 自适应直方图均衡化
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        
        # 锐化
        kernel = np.array([[-1,-1,-1],
                         [-1, 9,-1],
                         [-1,-1,-1]])
        sharpened = cv2.filter2D(enhanced, -1, kernel)
        
        # 二值化
        _, binary = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 形态学操作
        kernel = np.ones((2, 2), np.uint8)
        morph = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        # 转换回三通道
        result = cv2.cvtColor(morph, cv2.COLOR_GRAY2BGR)
        
        return result

    @staticmethod
    def process_image(image: np.ndarray, operations: List[str], params: Dict = None) -> np.ndarray:
        """
        应用一系列预处理操作
        
        Args:
            image: OpenCV 图像对象
            operations: 预处理操作列表
            params: 预处理参数
            
        Returns:
            np.ndarray: 处理后的图像
        """
        if params is None:
            params = {}
        
        result = image.copy()
        preprocessor = ImagePreprocessor()
        
        for op in operations:
            if op == 'deskew':
                result = preprocessor.deskew(result)
            elif op == 'enhance':
                result = preprocessor.enhance_image(result)
            elif op == 'remove_background':
                result = preprocessor.remove_background(result)
            elif op == 'sharpen':
                result = preprocessor.sharpen(result)
            elif op == 'adjust_contrast':
                alpha = params.get('contrast_alpha', 1.5)
                beta = params.get('contrast_beta', 0)
                result = preprocessor.adjust_contrast(result, alpha, beta)
            elif op == 'remove_shadows':
                result = preprocessor.remove_shadows(result)
            elif op == 'binarize':
                adaptive = params.get('adaptive_binarization', True)
                result = preprocessor.binarize(result, adaptive)
            elif op == 'remove_noise':
                method = params.get('denoise_method', 'gaussian')
                result = preprocessor.remove_noise(result, method)
            elif op == 'enhance_text':
                result = preprocessor.enhance_text(result)
            else:
                raise ValueError(f"不支持的预处理操作: {op}")
        
        return result

class OCRProcessor:
    def __init__(self, use_gpu: bool = False):
        """
        初始化 OCR 处理器，支持两种模型

        Args:
            use_gpu: 是否使用 GPU 加速
        """
        # 首先初始化logger
        self.logger = logging.getLogger(__name__)

        # PP-OCRv5 文本识别模型
        self.ocr_v5 = PaddleOCR(
            use_angle_cls=True,
            lang="ch",
            use_gpu=use_gpu,
            show_log=False
        )

        # PP-StructureV3 结构分析产线
        self.structure_v3 = None
        try:
            from paddleocr import PPStructureV3
            self.structure_v3 = PPStructureV3(
                use_doc_orientation_classify=False,
                use_doc_unwarping=False,
                use_textline_orientation=False,
                use_seal_recognition=False,  # 关闭印章识别以提高速度
                use_table_recognition=True,  # 保留表格识别
                use_formula_recognition=False,  # 关闭公式识别以提高速度
                use_chart_recognition=False,  # 关闭图表识别以提高速度
                device="gpu" if use_gpu else "cpu"
            )
            self.logger.info("PP-StructureV3 产线初始化成功")
        except Exception as e:
            self.logger.warning(f"PP-StructureV3 产线初始化失败: {e}")
            # 如果PP-StructureV3不可用，尝试使用旧版PPStructure
            if PPStructure is not None:
                try:
                    self.structure_v3 = PPStructure(
                        use_gpu=use_gpu,
                        show_log=False,
                        lang='ch'
                    )
                    self.logger.info("使用旧版 PPStructure 模型")
                except Exception as e2:
                    self.logger.warning(f"PPStructure 模型初始化失败: {e2}")
            else:
                self.logger.warning("PPStructure 不可用，将只使用 PP-OCRv5")

        self.layout_analyzer = LayoutAnalyzer()  # 保留简单版本用于兼容
        self.advanced_layout_analyzer = AdvancedLayoutAnalyzer()  # 新的高级版面分析器
        self.preprocessor = ImagePreprocessor()
        self.integrator = OCRAnnotationIntegrator()

        self.logger.info("OCR处理器初始化完成")
    
    def process_image(self, image_path: Union[str, Path]) -> Dict[str, Any]:
        """
        处理图片并返回OCR结果
        
        Args:
            image_path: 图片路径
            
        Returns:
            Dict: OCR结果，包含文本内容和位置信息
        """
        try:
            # 读取图片
            image_path = Path(image_path)
            
            if image_path.suffix.lower() in ['.heic', '.heif']:
                # 使用PIL读取HEIC格式
                pil_image = Image.open(image_path)
                if pil_image.mode != 'RGB':
                    pil_image = pil_image.convert('RGB')
                image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            else:
                # 使用OpenCV读取其他格式
                image = cv2.imread(str(image_path))
                if image is None:
                    raise ValueError(f"无法读取图像文件：{image_path}")
            
            # 进行OCR识别
            result = self.ocr_v5.ocr(image_path, cls=True)
            if not result or not result[0]:
                return {"error": "未识别到文本"}

            # 处理OCR结果
            regions = []
            for line in result[0]:
                box = line[0]
                text = line[1][0]
                confidence = line[1][1]
                
                # 计算区域类型
                region_type = self._determine_region_type(box, image.shape)
                
                regions.append({
                    "text": text,
                    "confidence": float(confidence),
                    "box": [[float(x), float(y)] for x, y in box],
                    "region_type": region_type
                })

            # 转换为Markdown格式
            markdown_text = self.integrator.convert_to_markdown({
                "regions": regions,
                "image_size": {
                    "width": image.shape[1],
                    "height": image.shape[0]
                }
            })

            return {
                "regions": regions,
                "image_size": {
                    "width": image.shape[1],
                    "height": image.shape[0]
                },
                "markdown_text": markdown_text
            }

        except Exception as e:
            self.logger.error(f"OCR处理失败: {str(e)}")
            return {"error": f"OCR处理失败: {str(e)}"}

    def process_image_dual_models(self, image_path: Union[str, Path]) -> Dict[str, Any]:
        """
        使用两种模型同时处理图片

        Args:
            image_path: 图片路径

        Returns:
            Dict: 包含两种模型结果的字典
        """
        try:
            image_path = Path(image_path)
            self.logger.info(f"开始双模型处理: {image_path}")

            # 读取图片
            if image_path.suffix.lower() in ['.heic', '.heif']:
                # 使用PIL读取HEIC格式
                pil_image = Image.open(image_path)
                if pil_image.mode != 'RGB':
                    pil_image = pil_image.convert('RGB')
                image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            else:
                # 使用OpenCV读取其他格式
                image = cv2.imread(str(image_path))
                if image is None:
                    raise ValueError(f"无法读取图像文件：{image_path}")

            results = {
                "image_size": {
                    "width": image.shape[1],
                    "height": image.shape[0]
                },
                "models": {}
            }

            # 1. PP-OCRv5 文本识别
            self.logger.info("开始 PP-OCRv5 处理...")
            ocr_v5_result = self.ocr_v5.ocr(str(image_path), cls=True)
            if ocr_v5_result and ocr_v5_result[0]:
                v5_regions = []
                for line in ocr_v5_result[0]:
                    box = line[0]
                    text = line[1][0]
                    confidence = line[1][1]

                    region_type = self._determine_region_type(box, image.shape)

                    v5_regions.append({
                        "text": text,
                        "confidence": float(confidence),
                        "box": [[float(x), float(y)] for x, y in box],
                        "region_type": region_type
                    })

                # 转换为Markdown格式
                v5_markdown = self.integrator.convert_to_markdown({
                    "regions": v5_regions,
                    "image_size": results["image_size"]
                })

                results["models"]["pp_ocrv5"] = {
                    "model_name": "PP-OCRv5",
                    "model_type": "text_recognition",
                    "regions": v5_regions,
                    "markdown_text": v5_markdown,
                    "total_regions": len(v5_regions)
                }
                self.logger.info(f"PP-OCRv5 处理完成，识别到 {len(v5_regions)} 个区域")
            else:
                results["models"]["pp_ocrv5"] = {
                    "model_name": "PP-OCRv5",
                    "model_type": "text_recognition",
                    "regions": [],
                    "markdown_text": "",
                    "total_regions": 0,
                    "error": "未识别到文本"
                }

            # 2. PP-StructureV3 结构分析
            if self.structure_v3 is not None:
                self.logger.info("开始 PP-StructureV3 处理...")
                try:
                    # 检查是否是新版PP-StructureV3产线
                    if hasattr(self.structure_v3, 'predict'):
                        # 使用新版PP-StructureV3的predict方法
                        structure_result = self.structure_v3.predict(str(image_path))
                        # PP-StructureV3返回的是结果列表，每个元素是一个结果对象
                        if structure_result and len(structure_result) > 0:
                            # 获取第一个结果（对于单张图片）
                            result_obj = structure_result[0]
                            # 从结果对象中提取parsing_res_list
                            if hasattr(result_obj, 'parsing_res_list'):
                                structure_data = result_obj.parsing_res_list
                            else:
                                # 如果没有parsing_res_list属性，尝试其他方式获取数据
                                structure_data = []
                                self.logger.warning("PP-StructureV3结果对象没有parsing_res_list属性")
                        else:
                            structure_data = []
                    else:
                        # 使用旧版PPStructure的直接调用方式
                        structure_data = self.structure_v3(str(image_path))

                    if structure_data:
                        structure_regions = []
                        for item in structure_data:
                            # 处理PP-StructureV3的结果格式
                            # 新版PP-StructureV3的结果格式可能包含block_bbox, block_label, block_content等字段
                            if isinstance(item, dict):
                                # 检查新版PP-StructureV3的字段
                                if 'block_bbox' in item and 'block_label' in item:
                                    # 新版PP-StructureV3格式
                                    bbox = item['block_bbox']
                                    item_type = item['block_label']
                                    content = item.get('block_content', '')

                                    region_data = {
                                        "text": content,
                                        "confidence": 1.0,  # PP-StructureV3可能不提供置信度
                                        "box": [[bbox[0], bbox[1]], [bbox[2], bbox[1]], [bbox[2], bbox[3]], [bbox[0], bbox[3]]],
                                        "region_type": item_type,
                                        "structure_type": item_type
                                    }

                                    # 如果是表格，添加表格特定信息
                                    if item_type == 'table' and 'block_content' in item:
                                        region_data["table_html"] = item.get('block_content', '')

                                    structure_regions.append(region_data)

                                # 检查旧版PPStructure的字段
                                elif 'type' in item and 'bbox' in item:
                                    # 旧版PPStructure格式
                                    bbox = item['bbox']
                                    item_type = item['type']

                                    region_data = {
                                        "text": item.get('res', {}).get('text', '') if 'res' in item else '',
                                        "confidence": float(item.get('res', {}).get('confidence', 0.0)) if 'res' in item else 0.0,
                                        "box": [[bbox[0], bbox[1]], [bbox[2], bbox[1]], [bbox[2], bbox[3]], [bbox[0], bbox[3]]],
                                        "region_type": item_type,
                                        "structure_type": item_type
                                    }

                                    # 如果是表格，添加表格特定信息
                                    if item_type == 'table' and 'res' in item:
                                        region_data["table_html"] = item['res'].get('html', '')

                                    structure_regions.append(region_data)

                        # 转换为Markdown格式
                        structure_markdown = self.integrator.convert_to_markdown({
                            "regions": structure_regions,
                            "image_size": results["image_size"]
                        })

                        results["models"]["pp_structurev3"] = {
                            "model_name": "PP-StructureV3",
                            "model_type": "structure_analysis",
                            "regions": structure_regions,
                            "markdown_text": structure_markdown,
                            "total_regions": len(structure_regions)
                        }
                        self.logger.info(f"PP-StructureV3 处理完成，识别到 {len(structure_regions)} 个结构元素")
                    else:
                        results["models"]["pp_structurev3"] = {
                            "model_name": "PP-StructureV3",
                            "model_type": "structure_analysis",
                            "regions": [],
                            "markdown_text": "",
                            "total_regions": 0,
                            "error": "未识别到结构元素"
                        }
                except Exception as e:
                    self.logger.error(f"PP-StructureV3 处理失败: {str(e)}")
                    results["models"]["pp_structurev3"] = {
                        "model_name": "PP-StructureV3",
                        "model_type": "structure_analysis",
                        "regions": [],
                        "markdown_text": "",
                        "total_regions": 0,
                        "error": f"处理失败: {str(e)}"
                    }
            else:
                results["models"]["pp_structurev3"] = {
                    "model_name": "PP-StructureV3",
                    "model_type": "structure_analysis",
                    "regions": [],
                    "markdown_text": "",
                    "total_regions": 0,
                    "error": "模型不可用"
                }

            self.logger.info("双模型处理完成")
            return results

        except Exception as e:
            import traceback
            error_msg = f"双模型OCR处理失败: {str(e)}"
            traceback_msg = traceback.format_exc()
            self.logger.error(f"{error_msg}\n{traceback_msg}")
            return {"error": error_msg}

    def _determine_region_type(self, box: List[List[float]], image_shape: tuple) -> str:
        """
        根据文本框的位置和大小确定区域类型
        
        Args:
            box: 文本框坐标
            image_shape: 图片尺寸
            
        Returns:
            str: 区域类型（title, heading, text等）
        """
        height = image_shape[0]
        width = image_shape[1]
        
        # 计算文本框的位置和大小特征
        box_np = np.array(box)
        min_x = np.min(box_np[:, 0])
        max_x = np.max(box_np[:, 0])
        min_y = np.min(box_np[:, 1])
        max_y = np.max(box_np[:, 1])
        
        box_width = max_x - min_x
        box_height = max_y - min_y
        relative_width = box_width / width
        relative_height = box_height / height
        relative_y = min_y / height
        
        # 根据位置和大小特征判断区域类型
        if relative_y < 0.2 and relative_width > 0.5:
            return "title"
        elif relative_height > 0.05 and relative_width > 0.4:
            return "heading"
        elif box_width < width * 0.2:
            return "annotation"
        else:
            return "text"

    def process_pdf(self, pdf_path: Union[str, Path]) -> List[OCRResult]:
        """
        处理 PDF 文件并提取文本
        
        Args:
            pdf_path: PDF 文件路径
            
        Returns:
            List[OCRResult]: OCR 识别结果列表
        """
        pdf_path = Path(pdf_path)
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF 文件不存在：{pdf_path}")
        
        results = []
        doc = fitz.open(str(pdf_path))
        
        # 使用线程池并行处理页面
        with ThreadPoolExecutor() as executor:
            futures = []
            for page_num in range(len(doc)):
                page = doc[page_num]
                # 将 PDF 页面转换为图像
                pix = page.get_pixmap()
                img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
                img_array = np.array(img)
                
                # 提交处理任务
                future = executor.submit(self._process_page, img_array, page_num)
                futures.append(future)
            
            # 收集结果
            for future in futures:
                page_results = future.result()
                results.extend(page_results)
        
        doc.close()
        return results
    
    def _process_page(self, image: np.ndarray, page_num: int) -> List[OCRResult]:
        """
        处理单个 PDF 页面
        
        Args:
            image: 页面图像
            page_num: 页码
            
        Returns:
            List[OCRResult]: 页面的 OCR 识别结果
        """
        # 图像预处理
        image = self.preprocessor.deskew(image)
        image = self.preprocessor.enhance_image(image)
        
        # 检测列和表格
        columns = self.layout_analyzer.detect_columns(image)
        tables = self.layout_analyzer.detect_tables(image)
        
        results = []
        
        # 处理表格区域
        for table_box in tables:
            table_image = image[table_box[1]:table_box[3], table_box[0]:table_box[2]]
            # 对表格区域应用特殊处理
            table_image = self.preprocessor.process_image(
                table_image,
                ['remove_background', 'enhance_text'],
                {'adaptive_binarization': True}
            )
            table_results = self.ocr_v5.ocr(table_image)
            if table_results:
                for line in table_results[0]:
                    box = [[p[0] + table_box[0], p[1] + table_box[1]] for p in line[0]]
                    results.append(OCRResult(
                        text=line[1][0],
                        confidence=line[1][1],
                        box=box,
                        page=page_num,
                        column=-1
                    ))
        
        # 处理每一列
        for col_idx, col_box in enumerate(columns):
            col_image = image[col_box[1]:col_box[3], col_box[0]:col_box[2]]
            # 对列区域应用特殊处理
            col_image = self.preprocessor.process_image(
                col_image,
                ['enhance_text', 'sharpen'],
                {'adaptive_binarization': True}
            )
            col_results = self.ocr_v5.ocr(col_image)
            if col_results and col_results[0]:
                for line in col_results[0]:
                    if line and len(line) >= 2:
                        box = [[p[0] + col_box[0], p[1] + col_box[1]] for p in line[0]]
                        results.append(OCRResult(
                            text=line[1][0],
                            confidence=line[1][1],
                            box=box,
                            page=page_num,
                            column=col_idx
                        ))
        
        # 如果没有检测到列，直接对整个图像进行OCR
        if not columns:
            ocr_results = self.ocr_v5.ocr(image)
            if ocr_results and ocr_results[0]:
                for line in ocr_results[0]:
                    if line and len(line) >= 2:
                        results.append(OCRResult(
                            text=line[1][0],
                            confidence=line[1][1],
                            box=line[0],
                            page=page_num,
                            column=0
                        ))
        
        # 按 y 坐标排序结果
        results.sort(key=lambda x: x.box[0][1])
        return results
        
    def analyze_layout(self, image_path: Union[str, Path]) -> Dict:
        """
        分析文档布局
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            Dict: 布局分析结果
        """
        # 读取图像，支持HEIC格式
        image_path = Path(image_path)
        
        if image_path.suffix.lower() in ['.heic', '.heif']:
            # 使用PIL读取HEIC格式
            pil_image = Image.open(image_path)
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            image_array = np.array(pil_image)
            image = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
        else:
            # 使用OpenCV读取其他格式
            image = cv2.imread(str(image_path))
            
        if image is None:
            raise ValueError(f"无法读取图像文件：{image_path}")
            
        # 获取图像尺寸
        height, width = image.shape[:2]
        
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 二值化
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        
        # 检测文本区域
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 分析布局
        layout = {
            'width': width,
            'height': height,
            'regions': []
        }
        
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            # 过滤掉太小的区域
            if w > width * 0.1 and h > height * 0.05:
                layout['regions'].append({
                    'x': x,
                    'y': y,
                    'width': w,
                    'height': h
                })
        
        return layout 
    
    def analyze_advanced_layout(self, image_path: Union[str, Path]) -> LayoutAnalysisResult:
        """
        高级版面分析
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            LayoutAnalysisResult: 详细的版面分析结果
        """
        # 读取图像
        image_path = Path(image_path)
        
        if image_path.suffix.lower() in ['.heic', '.heif']:
            # 处理HEIC格式
            pil_image = Image.open(image_path)
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            image_array = np.array(pil_image)
            image = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
        else:
            image = cv2.imread(str(image_path))
        
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # 使用高级版面分析器
        return self.advanced_layout_analyzer.analyze_layout(image)
    
    def process_image_with_layout(self, image_path: Union[str, Path]) -> Tuple[List[OCRResult], LayoutAnalysisResult]:
        """
        处理图像并进行版面分析
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            Tuple[List[OCRResult], LayoutAnalysisResult]: OCR结果和版面分析结果
        """
        # 读取图像
        image_path = Path(image_path)
        
        if image_path.suffix.lower() in ['.heic', '.heif']:
            # 处理HEIC格式
            pil_image = Image.open(image_path)
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            image_array = np.array(pil_image)
            image = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
        else:
            image = cv2.imread(str(image_path))
        
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # 进行版面分析
        layout_result = self.advanced_layout_analyzer.analyze_layout(image)
        
        # 图像预处理
        enhanced_image = self.preprocessor.enhance_image(image)
        deskewed_image = self.preprocessor.deskew(enhanced_image)
        
        # 基于版面分析结果进行OCR
        ocr_results = self._process_with_layout(deskewed_image, layout_result)
        
        return ocr_results, layout_result
    
    def _process_with_layout(self, image: np.ndarray, layout_result: LayoutAnalysisResult) -> List[OCRResult]:
        """
        基于版面分析结果进行OCR处理
        
        Args:
            image: 预处理后的图像
            layout_result: 版面分析结果
            
        Returns:
            List[OCRResult]: OCR识别结果
        """
        results = []
        
        # 按阅读顺序处理每个元素
        for element_id in layout_result.reading_order:
            # 找到对应的元素
            element = next((e for e in layout_result.elements if e.element_id == element_id), None)
            if element is None:
                continue
            
            # 提取元素区域
            bbox = element.bbox
            region_image = image[bbox.y:bbox.y2, bbox.x:bbox.x2]
            
            # 对区域进行OCR
            try:
                ocr_result = self.ocr_v5.ocr(region_image)
                if ocr_result and ocr_result[0]:
                    for line in ocr_result[0]:
                        if line and len(line) >= 2:
                            # 调整坐标到原图像坐标系
                            adjusted_box = [[p[0] + bbox.x, p[1] + bbox.y] for p in line[0]]
                            
                            # 确定列号
                            column_idx = 0
                            for i, column in enumerate(layout_result.columns):
                                if bbox.center_x >= column.x and bbox.center_x < column.x2:
                                    column_idx = i
                                    break
                            
                            results.append(OCRResult(
                                text=line[1][0],
                                confidence=line[1][1],
                                box=adjusted_box,
                                page=0,
                                column=column_idx,
                                element_type=element.element_type.value
                            ))
            except Exception as e:
                self.logger.warning(f"处理元素 {element_id} 时出错: {e}")
                continue
        
        return results
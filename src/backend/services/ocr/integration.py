#!/usr/bin/env python3
"""
OCR注释系统集成模块
实现OCR结果到注释界面的数据流转换和JSON格式标准化
"""

import sys
import os
from pathlib import Path
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Union, Any, Tuple
import logging
from dataclasses import dataclass, asdict
from enum import Enum

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.ocr.processor import OCRResult
from src.ocr.multilingual_processor import EnhancedOCRResult, CharacterResult, LineResult
from src.backend.annotation_service import AnnotationData, AnnotationTask, AnnotationType, AnnotationStatus
from src.database.mongodb_models import MongoDBManager

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class OCRAnnotationMapping:
    """OCR结果到注释的映射结构"""
    ocr_result_id: str
    annotation_id: str
    region_type: str
    confidence_threshold: float = 0.8
    requires_review: bool = False
    auto_approved: bool = False

@dataclass
class AnnotationWorkflowConfig:
    """注释工作流配置"""
    auto_create_tasks: bool = True
    confidence_threshold: float = 0.8
    require_manual_review: bool = True
    enable_batch_processing: bool = True
    max_tasks_per_batch: int = 50
    default_priority: int = 2

class OCRAnnotationIntegrator:
    """OCR注释系统集成器"""
    
    def __init__(self, db_manager: MongoDBManager = None, config: AnnotationWorkflowConfig = None):
        """
        初始化集成器
        
        Args:
            db_manager: MongoDB管理器
            config: 工作流配置
        """
        self.db_manager = db_manager or MongoDBManager()
        self.config = config or AnnotationWorkflowConfig()
        self.logger = logging.getLogger(__name__)
        
        # 确保数据库连接
        if not self.db_manager.connect():
            raise ConnectionError("无法连接到MongoDB数据库")
    
    def convert_ocr_to_annotation_format(
        self, 
        ocr_results: List[Union[OCRResult, EnhancedOCRResult]], 
        file_id: str,
        image_metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        将OCR结果转换为注释系统格式
        
        Args:
            ocr_results: OCR识别结果列表
            file_id: 文件ID
            image_metadata: 图像元数据
            
        Returns:
            Dict: 标准化的注释数据格式
        """
        self.logger.info(f"转换 {len(ocr_results)} 个OCR结果为注释格式")
        
        annotation_data = {
            "file_id": file_id,
            "created_at": datetime.now().isoformat(),
            "image_metadata": image_metadata or {},
            "total_regions": len(ocr_results),
            "regions": [],
            "statistics": {
                "avg_confidence": 0.0,
                "low_confidence_count": 0,
                "requires_review_count": 0
            }
        }
        
        total_confidence = 0.0
        low_confidence_count = 0
        
        for i, ocr_result in enumerate(ocr_results):
            region_data = self._convert_single_ocr_result(ocr_result, i)
            annotation_data["regions"].append(region_data)
            
            # 统计信息
            confidence = region_data.get("confidence", 0.0)
            total_confidence += confidence
            
            if confidence < self.config.confidence_threshold:
                low_confidence_count += 1
                region_data["requires_review"] = True
        
        # 计算统计信息
        if len(ocr_results) > 0:
            annotation_data["statistics"]["avg_confidence"] = total_confidence / len(ocr_results)
        annotation_data["statistics"]["low_confidence_count"] = low_confidence_count
        annotation_data["statistics"]["requires_review_count"] = low_confidence_count
        
        self.logger.info(f"转换完成，平均置信度: {annotation_data['statistics']['avg_confidence']:.3f}")
        return annotation_data
    
    def _convert_single_ocr_result(
        self, 
        ocr_result: Union[OCRResult, EnhancedOCRResult], 
        index: int
    ) -> Dict[str, Any]:
        """
        转换单个OCR结果
        
        Args:
            ocr_result: OCR结果
            index: 索引
            
        Returns:
            Dict: 转换后的区域数据
        """
        region_data = {
            "region_id": f"region_{index + 1}",
            "ocr_result_id": getattr(ocr_result, 'id', str(uuid.uuid4())),
            "text": ocr_result.text,
            "confidence": ocr_result.confidence,
            "bounding_box": self._normalize_bounding_box(ocr_result.box),
            "page": getattr(ocr_result, 'page', 0),
            "column": getattr(ocr_result, 'column', 0),
            "element_type": getattr(ocr_result, 'element_type', 'text'),
            "requires_review": ocr_result.confidence < self.config.confidence_threshold,
            "annotation_type": "text_correction",
            "status": "pending"
        }
        
        # 处理增强OCR结果的额外信息
        if isinstance(ocr_result, EnhancedOCRResult):
            region_data.update({
                "language": getattr(ocr_result, 'language', 'unknown'),
                "reading_direction": getattr(ocr_result, 'reading_direction', 'ltr'),
                "character_count": len(getattr(ocr_result, 'character_level_results', [])),
                "confidence_breakdown": getattr(ocr_result, 'confidence_breakdown', {}),
                "enhanced_features": True
            })
            
            # 添加字符级信息
            if hasattr(ocr_result, 'character_level_results') and ocr_result.character_level_results:
                region_data["character_level"] = [
                    {
                        "char": char.char,
                        "confidence": char.confidence,
                        "bbox": char.bbox,
                        "language": char.language
                    }
                    for char in ocr_result.character_level_results
                ]
        
        return region_data
    
    def _normalize_bounding_box(self, box: List) -> List[List[int]]:
        """
        标准化边界框格式
        
        Args:
            box: 原始边界框
            
        Returns:
            List: 标准化的边界框 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
        """
        if not box:
            return [[0, 0], [0, 0], [0, 0], [0, 0]]
        
        # 如果是4个点的格式，直接返回
        if len(box) == 4 and all(isinstance(point, list) and len(point) == 2 for point in box):
            return box
        
        # 如果是8个数字的格式，转换为4个点
        if len(box) == 8:
            return [
                [box[0], box[1]],
                [box[2], box[3]], 
                [box[4], box[5]],
                [box[6], box[7]]
            ]
        
        # 如果是[x, y, w, h]格式，转换为4个点
        if len(box) == 4 and all(isinstance(x, (int, float)) for x in box):
            x, y, w, h = box
            return [
                [x, y],
                [x + w, y],
                [x + w, y + h],
                [x, y + h]
            ]
        
        # 默认返回空边界框
        return [[0, 0], [0, 0], [0, 0], [0, 0]]
    
    def create_annotation_tasks_from_ocr(
        self, 
        ocr_results: List[Union[OCRResult, EnhancedOCRResult]], 
        file_id: str,
        batch_size: Optional[int] = None,
        priority: int = None,
        assigned_to: Optional[str] = None
    ) -> List[str]:
        """
        从OCR结果创建注释任务
        
        Args:
            ocr_results: OCR结果列表
            file_id: 文件ID
            batch_size: 批处理大小
            priority: 任务优先级
            assigned_to: 分配给的用户
            
        Returns:
            List[str]: 创建的任务ID列表
        """
        if not self.config.auto_create_tasks:
            self.logger.info("自动创建任务已禁用")
            return []
        
        batch_size = batch_size or self.config.max_tasks_per_batch
        priority = priority or self.config.default_priority
        
        # 按批次分组OCR结果
        batches = [
            ocr_results[i:i + batch_size] 
            for i in range(0, len(ocr_results), batch_size)
        ]
        
        task_ids = []
        
        for batch_index, batch in enumerate(batches):
            # 转换为注释格式
            annotation_data = self.convert_ocr_to_annotation_format(
                batch, file_id, {"batch_index": batch_index}
            )
            
            # 创建注释任务
            task_id = self._create_annotation_task(
                annotation_data, priority, assigned_to
            )
            
            if task_id:
                task_ids.append(task_id)
                self.logger.info(f"创建注释任务: {task_id} (批次 {batch_index + 1}/{len(batches)})")
        
        self.logger.info(f"总共创建了 {len(task_ids)} 个注释任务")
        return task_ids
    
    def _create_annotation_task(
        self, 
        annotation_data: Dict[str, Any], 
        priority: int,
        assigned_to: Optional[str]
    ) -> Optional[str]:
        """
        创建单个注释任务
        
        Args:
            annotation_data: 注释数据
            priority: 优先级
            assigned_to: 分配给的用户
            
        Returns:
            Optional[str]: 任务ID，失败时返回None
        """
        try:
            task_id = str(uuid.uuid4())
            
            # 构建任务数据
            task_data = {
                "task_id": task_id,
                "file_id": annotation_data["file_id"],
                "status": "pending",
                "priority": priority,
                "assigned_to": assigned_to,
                "created_at": datetime.now(),
                "annotation_data": annotation_data,
                "metadata": {
                    "total_regions": annotation_data["total_regions"],
                    "avg_confidence": annotation_data["statistics"]["avg_confidence"],
                    "requires_review_count": annotation_data["statistics"]["requires_review_count"]
                }
            }
            
            # 保存到数据库
            success = self.db_manager.save_annotation_task(task_data)
            
            if success:
                return task_id
            else:
                self.logger.error(f"保存注释任务失败: {task_id}")
                return None
                
        except Exception as e:
            self.logger.error(f"创建注释任务失败: {e}")
            return None
    
    def get_annotation_task_json(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取注释任务的JSON格式数据
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict]: 任务数据，失败时返回None
        """
        try:
            task_data = self.db_manager.get_annotation_task(task_id)
            
            if not task_data:
                return None
            
            # 标准化输出格式
            json_data = {
                "task_id": task_data.get("task_id"),
                "file_id": task_data.get("file_id"),
                "status": task_data.get("status"),
                "priority": task_data.get("priority"),
                "assigned_to": task_data.get("assigned_to"),
                "created_at": task_data.get("created_at"),
                "updated_at": task_data.get("updated_at"),
                "annotation_data": task_data.get("annotation_data", {}),
                "metadata": task_data.get("metadata", {})
            }
            
            return json_data
            
        except Exception as e:
            self.logger.error(f"获取注释任务JSON失败: {e}")
            return None
    
    def export_annotation_data_for_interface(
        self, 
        task_id: str, 
        format_type: str = "web"
    ) -> Optional[Dict[str, Any]]:
        """
        导出注释数据供界面使用
        
        Args:
            task_id: 任务ID
            format_type: 格式类型 ("web", "mobile", "api")
            
        Returns:
            Optional[Dict]: 格式化的数据，失败时返回None
        """
        task_data = self.get_annotation_task_json(task_id)
        
        if not task_data:
            return None
        
        annotation_data = task_data.get("annotation_data", {})
        
        if format_type == "web":
            return self._format_for_web_interface(annotation_data, task_data)
        elif format_type == "mobile":
            return self._format_for_mobile_interface(annotation_data, task_data)
        elif format_type == "api":
            return self._format_for_api_interface(annotation_data, task_data)
        else:
            return annotation_data
    
    def _format_for_web_interface(
        self, 
        annotation_data: Dict[str, Any], 
        task_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """为Web界面格式化数据"""
        return {
            "taskInfo": {
                "id": task_data.get("task_id"),
                "status": task_data.get("status"),
                "priority": task_data.get("priority"),
                "createdAt": task_data.get("created_at"),
                "assignedTo": task_data.get("assigned_to")
            },
            "imageInfo": {
                "fileId": annotation_data.get("file_id"),
                "metadata": annotation_data.get("image_metadata", {})
            },
            "regions": annotation_data.get("regions", []),
            "statistics": annotation_data.get("statistics", {}),
            "uiConfig": {
                "showConfidence": True,
                "enableBatchEdit": True,
                "autoSave": True,
                "confidenceThreshold": self.config.confidence_threshold
            }
        }
    
    def _format_for_mobile_interface(
        self, 
        annotation_data: Dict[str, Any], 
        task_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """为移动界面格式化数据"""
        # 简化数据结构，适合移动设备
        regions = annotation_data.get("regions", [])
        simplified_regions = []
        
        for region in regions:
            simplified_regions.append({
                "id": region.get("region_id"),
                "text": region.get("text"),
                "confidence": region.get("confidence"),
                "needsReview": region.get("requires_review", False),
                "bbox": region.get("bounding_box", [])
            })
        
        return {
            "taskId": task_data.get("task_id"),
            "status": task_data.get("status"),
            "regions": simplified_regions,
            "summary": {
                "total": len(regions),
                "needsReview": annotation_data.get("statistics", {}).get("requires_review_count", 0),
                "avgConfidence": annotation_data.get("statistics", {}).get("avg_confidence", 0.0)
            }
        }
    
    def _format_for_api_interface(
        self, 
        annotation_data: Dict[str, Any], 
        task_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """为API接口格式化数据"""
        return {
            "task": task_data,
            "data": annotation_data,
            "schema_version": "1.0",
            "format_type": "api",
            "generated_at": datetime.now().isoformat()
        }

def create_ocr_annotation_integrator(
    db_manager: MongoDBManager = None, 
    config: AnnotationWorkflowConfig = None
) -> OCRAnnotationIntegrator:
    """
    创建OCR注释集成器实例
    
    Args:
        db_manager: MongoDB管理器
        config: 工作流配置
        
    Returns:
        OCRAnnotationIntegrator: 集成器实例
    """
    return OCRAnnotationIntegrator(db_manager, config)

# 示例使用
if __name__ == "__main__":
    # 创建集成器
    integrator = create_ocr_annotation_integrator()
    
    # 示例OCR结果
    sample_ocr_results = [
        OCRResult(
            text="示例文本1",
            confidence=0.95,
            box=[[100, 100], [200, 100], [200, 150], [100, 150]],
            page=1,
            column=1,
            element_type="text"
        ),
        OCRResult(
            text="示例文本2",
            confidence=0.75,
            box=[[100, 200], [300, 200], [300, 250], [100, 250]],
            page=1,
            column=1,
            element_type="text"
        )
    ]
    
    # 转换为注释格式
    annotation_data = integrator.convert_ocr_to_annotation_format(
        sample_ocr_results, 
        "test_file_001"
    )
    
    print("转换结果:")
    print(json.dumps(annotation_data, indent=2, ensure_ascii=False)) 
"""
增强版基于人工标注的OCR处理器
集成学习系统，利用历史标注数据优化预处理参数和识别效果
"""

import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import json
import logging
from PIL import Image
from pillow_heif import register_heif_opener

from .annotation_guided_ocr import AnnotationGuidedOCR, AnnotatedOCRResult
from .interactive_annotator import AnnotationRegion
from .learning_system import OCRLearningSystem, TrainingSample
from .processor import OCRProcessor

# 注册HEIF/HEIC格式支持
register_heif_opener()

class EnhancedAnnotationGuidedOCR(AnnotationGuidedOCR):
    """增强版基于人工标注的OCR处理器，集成学习系统"""
    
    def __init__(self, learning_db_path: str = "data/ocr_learning.db"):
        super().__init__()
        self.learning_system = OCRLearningSystem(learning_db_path)
        self.logger = logging.getLogger(__name__)
        
    def process_with_annotations_and_learning(self, 
                                            image_path: str, 
                                            annotation_path: Optional[str] = None,
                                            create_annotation: bool = False,
                                            collect_training_data: bool = True) -> List[AnnotatedOCRResult]:
        """
        使用标注数据和学习系统进行OCR处理
        
        Args:
            image_path: 图像文件路径
            annotation_path: 标注文件路径
            create_annotation: 是否创建新标注
            collect_training_data: 是否收集训练数据
        
        Returns:
            带标注信息的OCR结果列表
        """
        print(f"🚀 [EnhancedOCR] 开始增强OCR处理")
        
        # 首先进行常规的标注OCR处理
        results = self.process_with_annotations(image_path, annotation_path, create_annotation)
        
        if not results:
            return results
        
        # 为每个区域类型获取优化的预处理参数
        optimized_results = []
        
        for result in results:
            print(f"🔧 优化区域 {result.region_id} ({result.region_type})")
            
            # 获取该区域类型的最优预处理参数
            optimal_params = self.learning_system.get_optimized_preprocessing_params(result.region_type)
            
            # 使用优化参数重新处理该区域
            enhanced_result = self._reprocess_region_with_params(
                image_path, result, optimal_params
            )
            
            if enhanced_result and enhanced_result.confidence > result.confidence:
                print(f"✅ 区域 {result.region_id} 优化成功: {result.confidence:.3f} -> {enhanced_result.confidence:.3f}")
                optimized_results.append(enhanced_result)
            else:
                print(f"⚪ 区域 {result.region_id} 保持原结果")
                optimized_results.append(result)
        
        return optimized_results
    
    def _reprocess_region_with_params(self, 
                                    image_path: str,
                                    original_result: AnnotatedOCRResult,
                                    preprocessing_params: Dict) -> Optional[AnnotatedOCRResult]:
        """
        使用优化的预处理参数重新处理区域
        
        Args:
            image_path: 原始图像路径
            original_result: 原始OCR结果
            preprocessing_params: 优化的预处理参数
        
        Returns:
            重新处理的OCR结果
        """
        try:
            # 加载原始图像
            image = self._load_image(image_path)
            if image is None:
                return None
            
            # 提取区域
            x, y, w, h = original_result.bbox
            region_image = image[y:y+h, x:x+w]
            
            # 应用优化的预处理参数
            processed_image = self._apply_optimized_preprocessing(
                region_image, 
                original_result.region_type, 
                preprocessing_params
            )
            
            # 进行OCR识别
            results = self.ocr_processor.ocr.ocr(processed_image, cls=True)
            
            if not results or not results[0]:
                return None
            
            # 提取文本和置信度
            text_lines = []
            confidences = []
            raw_results = []
            
            for line in results[0]:
                if len(line) >= 2:
                    bbox, (text, conf) = line[0], line[1]
                    if text.strip():
                        text_lines.append(text.strip())
                        confidences.append(conf)
                        raw_results.append({
                            'bbox': bbox,
                            'text': text,
                            'confidence': conf
                        })
            
            if not text_lines:
                return None
            
            combined_text = '\n'.join(text_lines)
            avg_confidence = np.mean(confidences) if confidences else 0.0
            
            # 创建新的结果对象
            enhanced_result = AnnotatedOCRResult(
                region_id=original_result.region_id,
                region_type=original_result.region_type,
                order=original_result.order,
                bbox=original_result.bbox,
                text=combined_text,
                confidence=avg_confidence,
                raw_ocr_results=raw_results
            )
            
            return enhanced_result
            
        except Exception as e:
            self.logger.error(f"重新处理区域失败: {e}")
            return None
    
    def _apply_optimized_preprocessing(self, 
                                     image: np.ndarray, 
                                     region_type: str, 
                                     params: Dict) -> np.ndarray:
        """
        应用优化的预处理参数
        
        Args:
            image: 输入图像
            region_type: 区域类型
            params: 预处理参数
        
        Returns:
            预处理后的图像
        """
        try:
            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # 应用CLAHE增强
            clahe_clip_limit = params.get('clahe_clip_limit', 2.0)
            clahe = cv2.createCLAHE(clipLimit=clahe_clip_limit, tileGridSize=(8, 8))
            enhanced = clahe.apply(gray)
            
            # 根据参数选择阈值方法
            if params.get('otsu_threshold', False):
                # 使用OTSU阈值
                _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            else:
                # 使用自适应阈值
                block_size = params.get('adaptive_threshold_block_size', 11)
                c_value = params.get('adaptive_threshold_c', 2)
                binary = cv2.adaptiveThreshold(
                    enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 
                    block_size, c_value
                )
            
            # 应用形态学操作
            if 'morphology_kernel_size' in params:
                kernel_size = params['morphology_kernel_size']
                if isinstance(kernel_size, (list, tuple)) and len(kernel_size) == 2:
                    kernel = np.ones(kernel_size, np.uint8)
                else:
                    kernel = np.ones((2, 2), np.uint8)
                
                morph_op = params.get('morphology_operation', 'close')
                if morph_op == 'close':
                    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
                elif morph_op == 'open':
                    binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
            
            # 多阈值处理（用于图像区域）
            if params.get('multiple_thresholds', False):
                # 尝试多种阈值方法并选择最佳结果
                binary1 = cv2.adaptiveThreshold(
                    enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
                )
                _, binary2 = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
                
                # 简单的选择策略：选择前景像素更多的结果
                if np.sum(binary1 == 0) > np.sum(binary2 == 0):
                    binary = binary1
                else:
                    binary = binary2
            
            return binary
            
        except Exception as e:
            self.logger.error(f"预处理失败: {e}")
            # 返回简单的二值化结果作为后备
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            return binary
    
    def collect_feedback_and_learn(self, 
                                 results: List[AnnotatedOCRResult],
                                 corrected_texts: Dict[str, str],
                                 image_path: str) -> List[TrainingSample]:
        """
        收集用户反馈并进行学习
        
        Args:
            results: OCR识别结果
            corrected_texts: 用户修正的文本 {region_id: corrected_text}
            image_path: 原始图像路径
        
        Returns:
            收集的训练样本
        """
        print(f"📚 收集反馈数据进行学习...")
        
        # 为结果添加图像路径信息
        enhanced_results = []
        for result in results:
            # 创建一个包含图像路径的新结果对象
            enhanced_result = AnnotatedOCRResult(
                region_id=result.region_id,
                region_type=result.region_type,
                order=result.order,
                bbox=result.bbox,
                text=result.text,
                confidence=result.confidence,
                raw_ocr_results=result.raw_ocr_results
            )
            enhanced_results.append(enhanced_result)
        
        # 收集训练数据
        samples = self.learning_system.collect_training_data(enhanced_results, corrected_texts)
        
        if samples:
            print(f"✅ 收集了 {len(samples)} 个训练样本")
            
            # 为每个区域类型优化预处理参数
            region_types = set(sample.region_type for sample in samples)
            for region_type in region_types:
                print(f"🔧 优化 {region_type} 区域的预处理参数...")
                self.learning_system.optimize_preprocessing_params(region_type)
        
        return samples
    
    def get_learning_report(self) -> Dict:
        """获取学习系统报告"""
        metrics = self.learning_system.generate_learning_report()
        
        return {
            'total_samples': metrics.total_samples,
            'accuracy_improvement': f"{metrics.accuracy_improvement:.2%}",
            'confidence_improvement': f"{metrics.confidence_improvement:.2%}",
            'region_performance': {
                region: f"{performance:.3f}" 
                for region, performance in metrics.region_type_performance.items()
            },
            'common_errors': metrics.common_errors[:5],  # 只返回前5个常见错误
            'preprocessing_effectiveness': {
                region: f"{effectiveness:.3f}"
                for region, effectiveness in metrics.preprocessing_effectiveness.items()
            }
        }
    
    def analyze_region_performance(self, region_type: str) -> Dict:
        """分析特定区域类型的性能"""
        error_analysis = self.learning_system.analyze_error_patterns(region_type)
        
        return {
            'region_type': region_type,
            'total_samples': error_analysis['total_samples'],
            'average_performance': error_analysis['region_performance'].get(region_type, 0.0),
            'error_types': error_analysis['error_types'],
            'common_mistakes': list(error_analysis['common_mistakes'].items())[:5]
        }
    
    def export_learning_data(self, output_path: str):
        """导出学习数据"""
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 获取所有区域类型的数据
        all_data = {}
        
        for region_type in ['single_column', 'left_column', 'right_column', 'table', 'image']:
            samples = self.learning_system.database.get_samples_by_region_type(region_type)
            
            if samples:
                all_data[region_type] = {
                    'sample_count': len(samples),
                    'samples': [
                        {
                            'sample_id': sample.sample_id,
                            'ground_truth_text': sample.ground_truth_text,
                            'ocr_predicted_text': sample.ocr_predicted_text,
                            'confidence': sample.confidence,
                            'created_at': sample.created_at
                        }
                        for sample in samples[:10]  # 只导出前10个样本作为示例
                    ],
                    'best_preprocessing_params': self.learning_system.database.get_best_preprocessing_params(region_type)
                }
        
        # 添加整体统计
        report = self.get_learning_report()
        all_data['summary'] = report
        
        # 保存到文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(all_data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"学习数据已导出到: {output_path}")

def main():
    """主函数 - 演示增强OCR系统的使用"""
    import sys
    
    if len(sys.argv) < 2:
        print("用法: python enhanced_annotation_guided_ocr.py <图片路径> [标注文件路径]")
        print("示例: python enhanced_annotation_guided_ocr.py pic/IMG_1320.heic")
        return
    
    image_path = sys.argv[1]
    annotation_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 创建增强OCR处理器
    enhanced_ocr = EnhancedAnnotationGuidedOCR()
    
    # 处理图像
    print("🚀 开始增强OCR处理...")
    results = enhanced_ocr.process_with_annotations_and_learning(
        image_path, 
        annotation_path, 
        create_annotation=True
    )
    
    if results:
        print(f"\n✅ 处理完成！共识别 {len(results)} 个区域：")
        
        for result in results:
            print(f"\n区域 {result.order} ({result.region_type}):")
            print(f"位置: {result.bbox}")
            print(f"置信度: {result.confidence:.3f}")
            print(f"文本: {result.text[:100]}...")
        
        # 模拟用户反馈（实际应用中这些数据来自用户界面）
        print("\n📝 模拟收集用户反馈...")
        corrected_texts = {}
        
        # 这里可以添加一些模拟的修正文本
        # corrected_texts[results[0].region_id] = "修正后的文本内容"
        
        if corrected_texts:
            samples = enhanced_ocr.collect_feedback_and_learn(
                results, corrected_texts, image_path
            )
            print(f"📚 收集了 {len(samples)} 个训练样本")
        
        # 生成学习报告
        print("\n📊 生成学习报告...")
        report = enhanced_ocr.get_learning_report()
        
        print(f"总样本数: {report['total_samples']}")
        print(f"准确率改进: {report['accuracy_improvement']}")
        print(f"置信度改进: {report['confidence_improvement']}")
        
        if report['region_performance']:
            print("\n区域性能:")
            for region, performance in report['region_performance'].items():
                print(f"  {region}: {performance}")
        
        # 导出结果和学习数据
        output_dir = Path(image_path).parent / "ocr_results"
        output_dir.mkdir(exist_ok=True)
        
        # 导出OCR结果
        results_file = output_dir / f"{Path(image_path).stem}_enhanced_results.json"
        enhanced_ocr.export_results(results, str(results_file))
        
        # 导出学习数据
        learning_file = output_dir / f"{Path(image_path).stem}_learning_data.json"
        enhanced_ocr.export_learning_data(str(learning_file))
        
        print(f"\n📁 结果已保存到:")
        print(f"- OCR结果: {results_file}")
        print(f"- 学习数据: {learning_file}")
        
    else:
        print("❌ OCR处理失败或无结果")

if __name__ == "__main__":
    main() 
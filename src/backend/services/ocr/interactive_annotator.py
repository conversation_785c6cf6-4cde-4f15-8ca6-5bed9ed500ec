"""
交互式OCR标注系统
允许用户手动圈画和标注文档区域，提高OCR处理的准确性
"""

import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
import json
from PIL import Image
from pillow_heif import register_heif_opener
import logging

# 注册HEIF/HEIC格式支持
register_heif_opener()

@dataclass
class AnnotationRegion:
    """标注区域"""
    id: str
    x: int
    y: int
    width: int
    height: int
    region_type: str  # 'single_column', 'left_column', 'right_column', 'table', 'image', 'header', 'footer'
    label: str = ""
    confidence: float = 1.0  # 人工标注的置信度为1.0
    order: int = 0  # 阅读顺序

@dataclass
class AnnotationData:
    """完整的标注数据"""
    image_path: str
    image_width: int
    image_height: int
    regions: List[AnnotationRegion]
    created_at: str
    modified_at: str

class InteractiveAnnotator:
    """交互式标注器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.current_image = None
        self.display_image = None
        self.annotations = []
        self.current_region_type = 'single_column'
        self.drawing = False
        self.start_point = None
        self.temp_rect = None
        self.region_colors = {
            'single_column': (0, 255, 0),      # 绿色
            'left_column': (255, 0, 0),        # 蓝色
            'right_column': (0, 0, 255),       # 红色
            'table': (255, 255, 0),            # 青色
            'image': (255, 0, 255),            # 洋红色
            'header': (128, 255, 128),         # 浅绿色
            'footer': (128, 128, 255),         # 浅蓝色
            'special': (255, 128, 0),          # 橙色
        }
        self.window_name = "OCR区域标注 - 按ESC退出，按S保存"
        
    def load_image(self, image_path: str) -> bool:
        """加载图像"""
        try:
            image_path = Path(image_path)
            
            if image_path.suffix.lower() in ['.heic', '.heif']:
                # 使用PIL读取HEIC格式
                pil_image = Image.open(image_path)
                if pil_image.mode != 'RGB':
                    pil_image = pil_image.convert('RGB')
                image_array = np.array(pil_image)
                self.current_image = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
            else:
                # 使用OpenCV读取其他格式
                self.current_image = cv2.imread(str(image_path))
            
            if self.current_image is None:
                self.logger.error(f"无法加载图像: {image_path}")
                return False
            
            # 创建显示图像的副本
            self.display_image = self.current_image.copy()
            self.image_path = str(image_path)
            
            # 如果图像太大，缩放以适应屏幕
            height, width = self.current_image.shape[:2]
            max_height = 800
            if height > max_height:
                scale = max_height / height
                new_width = int(width * scale)
                new_height = int(height * scale)
                self.display_image = cv2.resize(self.display_image, (new_width, new_height))
                self.scale_factor = scale
            else:
                self.scale_factor = 1.0
            
            self.logger.info(f"成功加载图像: {image_path}, 尺寸: {width}x{height}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载图像失败: {e}")
            return False
    
    def mouse_callback(self, event, x, y, flags, param):
        """鼠标回调函数"""
        if event == cv2.EVENT_LBUTTONDOWN:
            # 开始绘制矩形
            self.drawing = True
            self.start_point = (x, y)
            
        elif event == cv2.EVENT_MOUSEMOVE:
            if self.drawing:
                # 绘制临时矩形
                temp_image = self.display_image.copy()
                self._draw_existing_annotations(temp_image)
                
                # 绘制当前正在绘制的矩形
                color = self.region_colors.get(self.current_region_type, (255, 255, 255))
                cv2.rectangle(temp_image, self.start_point, (x, y), color, 2)
                
                # 显示区域类型
                cv2.putText(temp_image, self.current_region_type, 
                           (self.start_point[0], self.start_point[1] - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
                
                cv2.imshow(self.window_name, temp_image)
                
        elif event == cv2.EVENT_LBUTTONUP:
            if self.drawing:
                # 完成矩形绘制
                self.drawing = False
                end_point = (x, y)
                
                # 计算矩形区域（转换回原始图像坐标）
                x1 = int(min(self.start_point[0], end_point[0]) / self.scale_factor)
                y1 = int(min(self.start_point[1], end_point[1]) / self.scale_factor)
                x2 = int(max(self.start_point[0], end_point[0]) / self.scale_factor)
                y2 = int(max(self.start_point[1], end_point[1]) / self.scale_factor)
                
                width = x2 - x1
                height = y2 - y1
                
                # 只有当矩形足够大时才添加
                if width > 10 and height > 10:
                    region = AnnotationRegion(
                        id=f"region_{len(self.annotations) + 1}",
                        x=x1,
                        y=y1,
                        width=width,
                        height=height,
                        region_type=self.current_region_type,
                        order=len(self.annotations) + 1
                    )
                    self.annotations.append(region)
                    self.logger.info(f"添加区域: {region.region_type} at ({x1}, {y1}) {width}x{height}")
                
                # 重新绘制所有标注
                self._redraw_annotations()
    
    def _draw_existing_annotations(self, image):
        """绘制已有的标注"""
        for region in self.annotations:
            # 转换到显示坐标
            x1 = int(region.x * self.scale_factor)
            y1 = int(region.y * self.scale_factor)
            x2 = int((region.x + region.width) * self.scale_factor)
            y2 = int((region.y + region.height) * self.scale_factor)
            
            color = self.region_colors.get(region.region_type, (255, 255, 255))
            
            # 绘制矩形
            cv2.rectangle(image, (x1, y1), (x2, y2), color, 2)
            
            # 绘制标签
            label = f"{region.id}: {region.region_type}"
            cv2.putText(image, label, (x1, y1 - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            # 绘制顺序号
            cv2.putText(image, str(region.order), (x1 + 5, y1 + 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
    
    def _redraw_annotations(self):
        """重新绘制所有标注"""
        temp_image = self.display_image.copy()
        self._draw_existing_annotations(temp_image)
        cv2.imshow(self.window_name, temp_image)
    
    def _show_help(self):
        """显示帮助信息"""
        help_text = [
            "=== OCR区域标注工具 ===",
            "",
            "鼠标操作:",
            "- 左键拖拽: 绘制区域",
            "",
            "键盘快捷键:",
            "1 - 单栏文本",
            "2 - 左栏文本", 
            "3 - 右栏文本",
            "4 - 表格",
            "5 - 图像",
            "6 - 页眉",
            "7 - 页脚",
            "8 - 特殊区域",
            "",
            "u - 撤销上一个区域",
            "c - 清除所有区域",
            "s - 保存标注",
            "h - 显示帮助",
            "ESC - 退出",
            "",
            f"当前区域类型: {self.current_region_type}",
            f"已标注区域: {len(self.annotations)}个"
        ]
        
        print("\n".join(help_text))
    
    def start_annotation(self, image_path: str, load_existing: bool = True) -> bool:
        """开始标注过程"""
        if not self.load_image(image_path):
            return False
        
        # 尝试加载已有的标注
        annotation_file = Path(image_path).with_suffix('.json')
        if load_existing and annotation_file.exists():
            self.load_annotations(str(annotation_file))
        
        # 创建窗口
        cv2.namedWindow(self.window_name, cv2.WINDOW_NORMAL)
        cv2.setMouseCallback(self.window_name, self.mouse_callback)
        
        # 显示帮助
        self._show_help()
        
        # 初始显示
        self._redraw_annotations()
        
        print(f"\n开始标注图像: {image_path}")
        print("按 'h' 查看帮助，按 ESC 退出")
        
        while True:
            key = cv2.waitKey(1) & 0xFF
            
            if key == 27:  # ESC键退出
                break
            elif key == ord('1'):
                self.current_region_type = 'single_column'
                print(f"切换到: {self.current_region_type}")
            elif key == ord('2'):
                self.current_region_type = 'left_column'
                print(f"切换到: {self.current_region_type}")
            elif key == ord('3'):
                self.current_region_type = 'right_column'
                print(f"切换到: {self.current_region_type}")
            elif key == ord('4'):
                self.current_region_type = 'table'
                print(f"切换到: {self.current_region_type}")
            elif key == ord('5'):
                self.current_region_type = 'image'
                print(f"切换到: {self.current_region_type}")
            elif key == ord('6'):
                self.current_region_type = 'header'
                print(f"切换到: {self.current_region_type}")
            elif key == ord('7'):
                self.current_region_type = 'footer'
                print(f"切换到: {self.current_region_type}")
            elif key == ord('8'):
                self.current_region_type = 'special'
                print(f"切换到: {self.current_region_type}")
            elif key == ord('u'):  # 撤销
                if self.annotations:
                    removed = self.annotations.pop()
                    print(f"撤销区域: {removed.id}")
                    self._redraw_annotations()
            elif key == ord('c'):  # 清除所有
                self.annotations.clear()
                print("清除所有标注")
                self._redraw_annotations()
            elif key == ord('s'):  # 保存
                self.save_annotations(str(annotation_file))
                print(f"标注已保存到: {annotation_file}")
            elif key == ord('h'):  # 帮助
                self._show_help()
        
        cv2.destroyAllWindows()
        return True
    
    def save_annotations(self, output_path: str):
        """保存标注数据"""
        from datetime import datetime
        
        height, width = self.current_image.shape[:2]
        
        annotation_data = AnnotationData(
            image_path=self.image_path,
            image_width=width,
            image_height=height,
            regions=self.annotations,
            created_at=datetime.now().isoformat(),
            modified_at=datetime.now().isoformat()
        )
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(asdict(annotation_data), f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"标注数据已保存到: {output_path}")
    
    def load_annotations(self, annotation_path: str) -> bool:
        """加载标注数据"""
        try:
            with open(annotation_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.annotations = [
                AnnotationRegion(**region_data) 
                for region_data in data['regions']
            ]
            
            self.logger.info(f"加载了 {len(self.annotations)} 个标注区域")
            return True
            
        except Exception as e:
            self.logger.error(f"加载标注数据失败: {e}")
            return False
    
    def export_for_ocr(self, annotation_path: str) -> Dict:
        """导出标注数据供OCR使用"""
        if not Path(annotation_path).exists():
            return {}
        
        with open(annotation_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 按阅读顺序排序
        regions = sorted(data['regions'], key=lambda x: x['order'])
        
        # 转换为OCR处理器可用的格式
        ocr_regions = {
            'single_column': [],
            'left_column': [],
            'right_column': [],
            'table': [],
            'image': [],
            'header': [],
            'footer': [],
            'special': []
        }
        
        for region in regions:
            region_type = region['region_type']
            if region_type in ocr_regions:
                ocr_regions[region_type].append({
                    'x': region['x'],
                    'y': region['y'],
                    'width': region['width'],
                    'height': region['height'],
                    'order': region['order']
                })
        
        return ocr_regions

def main():
    """主函数 - 演示如何使用标注工具"""
    import sys
    
    if len(sys.argv) < 2:
        print("用法: python interactive_annotator.py <图片路径>")
        print("示例: python interactive_annotator.py pic/IMG_1320.heic")
        return
    
    image_path = sys.argv[1]
    
    annotator = InteractiveAnnotator()
    success = annotator.start_annotation(image_path)
    
    if success:
        print("标注完成！")
        
        # 导出OCR格式
        annotation_file = Path(image_path).with_suffix('.json')
        if annotation_file.exists():
            ocr_regions = annotator.export_for_ocr(str(annotation_file))
            print(f"导出的OCR区域: {ocr_regions}")
    else:
        print("标注失败！")

if __name__ == "__main__":
    main() 
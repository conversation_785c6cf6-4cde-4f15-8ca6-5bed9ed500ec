"""
OCR和标注系统集成服务
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
import logging
from bson import ObjectId
import re
from .types import OCRResult

from src.backend.logger import get_logger

logger = get_logger(__name__)

class OCRAnnotationIntegrator:
    """OCR和标注系统集成类"""
    
    def __init__(self):
        """初始化集成服务"""
        self.db = None  # 将在异步方法中初始化
        self.export_service = None  # 暂时禁用
        self.annotation_service = None  # 暂时禁用
        self.logger = logging.getLogger(__name__)
        
    async def sync_ocr_results(
        self,
        task_id: str,
        ocr_results: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        同步OCR结果到标注系统
        
        Args:
            task_id: 任务ID
            ocr_results: OCR结果列表
            
        Returns:
            Dict[str, Any]: 同步结果
        """
        try:
            # 获取任务信息
            task = await self.db["annotation_tasks"].find_one(
                {"_id": ObjectId(task_id)}
            )
            if not task:
                raise ValueError(f"找不到任务: {task_id}")
            
            # 更新任务状态
            await self.db["annotation_tasks"].update_one(
                {"_id": ObjectId(task_id)},
                {
                    "$set": {
                        "ocr_status": "completed",
                        "ocr_completed_at": datetime.now()
                    }
                }
            )
            
            # 创建标注记录
            annotations = []
            for result in ocr_results:
                annotation = {
                    "task_id": ObjectId(task_id),
                    "image_id": result["image_id"],
                    "boxes": [
                        {
                            "points": box["points"],
                            "text": box["text"],
                            "confidence": box["confidence"],
                            "category": box.get("category", "text")
                        }
                        for box in result["boxes"]
                    ],
                    "status": "pending",
                    "created_at": datetime.now()
                }
                annotations.append(annotation)
            
            # 批量插入标注记录
            if annotations:
                await self.db["annotations"].insert_many(annotations)
            
            return {
                "success": True,
                "message": "OCR结果同步成功",
                "task_id": task_id,
                "annotation_count": len(annotations)
            }
            
        except Exception as e:
            logger.error(f"同步OCR结果失败: {e}")
            return {
                "success": False,
                "message": f"同步失败: {str(e)}"
            }
    
    async def export_task_data(
        self,
        task_id: str,
        output_dir: str,
        include_images: bool = False
    ) -> Dict[str, Any]:
        """
        导出任务数据
        
        Args:
            task_id: 任务ID
            output_dir: 输出目录
            include_images: 是否包含图像
            
        Returns:
            Dict[str, Any]: 导出结果
        """
        try:
            # 获取任务信息
            task = await self.db["annotation_tasks"].find_one(
                {"_id": ObjectId(task_id)}
            )
            if not task:
                raise ValueError(f"找不到任务: {task_id}")
            
            # 获取任务的标注数据
            annotations = await self.db["annotations"].find({
                "task_id": ObjectId(task_id)
            }).to_list(None)
            
            if not annotations:
                return {
                    "success": False,
                    "message": "任务没有标注数据"
                }
            
            # 导出标注数据
            label_file, warnings = await self.export_service.export_annotations(
                task["project_id"],
                output_dir,
                batch_size=100
            )
            
            # 导出分类标签
            categories_file = await self.export_service.export_multi_level_categories(
                task["project_id"],
                output_dir
            )
            
            return {
                "success": True,
                "message": "任务数据导出成功",
                "task_id": task_id,
                "label_file": label_file,
                "categories_file": categories_file,
                "annotation_count": len(annotations),
                "warnings": warnings
            }
            
        except Exception as e:
            logger.error(f"导出任务数据失败: {e}")
            return {
                "success": False,
                "message": f"导出失败: {str(e)}"
            }
    
    async def update_annotation_quality(
        self,
        task_id: str,
        quality_scores: Dict[str, float]
    ) -> Dict[str, Any]:
        """
        更新标注质量分数
        
        Args:
            task_id: 任务ID
            quality_scores: 质量分数字典 {annotation_id: score}
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        try:
            updated_count = 0
            for annotation_id, score in quality_scores.items():
                # 验证分数范围
                if not 0 <= score <= 1:
                    continue
                
                # 更新质量分数
                result = await self.db["annotations"].update_one(
                    {
                        "_id": ObjectId(annotation_id),
                        "task_id": ObjectId(task_id)
                    },
                    {
                        "$set": {
                            "quality_score": score,
                            "updated_at": datetime.now()
                        }
                    }
                )
                updated_count += result.modified_count
            
            return {
                "success": True,
                "message": "质量分数更新成功",
                "task_id": task_id,
                "updated_count": updated_count
            }
            
        except Exception as e:
            logger.error(f"更新标注质量分数失败: {e}")
            return {
                "success": False,
                "message": f"更新失败: {str(e)}"
            }
    
    async def get_task_statistics(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务统计信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 获取任务信息
            task = await self.db["annotation_tasks"].find_one(
                {"_id": ObjectId(task_id)}
            )
            if not task:
                raise ValueError(f"找不到任务: {task_id}")
            
            # 获取标注统计
            annotations = await self.db["annotations"].find({
                "task_id": ObjectId(task_id)
            }).to_list(None)
            
            # 计算统计信息
            total_annotations = len(annotations)
            completed_annotations = sum(1 for a in annotations if a["status"] == "completed")
            pending_annotations = sum(1 for a in annotations if a["status"] == "pending")
            
            # 计算平均质量分数
            quality_scores = [a.get("quality_score", 0) for a in annotations]
            avg_quality_score = sum(quality_scores) / len(quality_scores) if quality_scores else 0
            
            return {
                "success": True,
                "task_id": task_id,
                "statistics": {
                    "total_annotations": total_annotations,
                    "completed_annotations": completed_annotations,
                    "pending_annotations": pending_annotations,
                    "completion_rate": completed_annotations / total_annotations if total_annotations else 0,
                    "average_quality_score": avg_quality_score
                }
            }
            
        except Exception as e:
            logger.error(f"获取任务统计信息失败: {e}")
            return {
                "success": False,
                "message": f"获取统计信息失败: {str(e)}"
            }
    
    def convert_to_markdown(self, ocr_result: Dict[str, Any]) -> str:
        """
        将OCR结果转换为Markdown格式
        
        Args:
            ocr_result: OCR识别结果
            
        Returns:
            str: Markdown格式的文本
        """
        if not ocr_result or "regions" not in ocr_result:
            return ""
            
        regions = ocr_result["regions"]
        if not regions:
            return ""
            
        # 按y坐标排序区域
        def get_y_coord(region):
            try:
                box = region.get("box", [])
                if isinstance(box, list) and len(box) > 0:
                    if isinstance(box[0], list) and len(box[0]) > 1:
                        return box[0][1]
                    elif isinstance(box[0], (int, float)):
                        return box[1] if len(box) > 1 else 0
                return 0
            except (KeyError, IndexError, TypeError):
                return 0

        regions.sort(key=get_y_coord)
        
        markdown_lines = []
        current_section = None
        
        for region in regions:
            text = region["text"].strip()
            region_type = region["region_type"]
            
            if not text:
                continue
                
            # 根据区域类型添加Markdown格式
            if region_type == "title":
                markdown_lines.append(f"# {text}\n")
                current_section = None
            elif region_type == "heading":
                if current_section != text:
                    markdown_lines.append(f"## {text}\n")
                    current_section = text
            elif region_type == "annotation":
                markdown_lines.append(f"> {text}\n")
            else:  # text
                # 处理特殊格式
                # 1. 检查是否为列表项
                if re.match(r"^\d+\.", text):
                    markdown_lines.append(f"{text}\n")
                elif re.match(r"^[•·\-]", text):
                    markdown_lines.append(f"- {text.lstrip('•·-')}\n")
                # 2. 检查是否为表格行
                elif "|" in text or "\t" in text:
                    cells = text.split("|" if "|" in text else "\t")
                    markdown_lines.append(f"| {' | '.join(cells)} |\n")
                # 3. 普通文本
                else:
                    markdown_lines.append(f"{text}\n")
        
        # 合并相邻的相同类型区域
        merged_lines = []
        current_type = None
        current_text = []
        
        for line in markdown_lines:
            line_type = self._get_line_type(line)
            
            if line_type != current_type and current_text:
                merged_lines.append("".join(current_text))
                current_text = []
            
            current_type = line_type
            current_text.append(line)
        
        if current_text:
            merged_lines.append("".join(current_text))
        
        return "\n".join(merged_lines)
    
    def _get_line_type(self, line: str) -> str:
        """
        获取行的类型
        
        Args:
            line: Markdown行
            
        Returns:
            str: 行类型
        """
        if line.startswith("#"):
            return "heading"
        elif line.startswith(">"):
            return "quote"
        elif line.startswith("- ") or re.match(r"^\d+\.", line):
            return "list"
        elif line.startswith("|"):
            return "table"
        else:
            return "text"
    
    def process_ocr_result(self, ocr_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理OCR结果，生成Markdown格式
        
        Args:
            ocr_result: OCR识别结果
            
        Returns:
            Dict[str, Any]: 处理后的结果，包含Markdown格式
        """
        try:
            # 生成Markdown格式
            markdown_text = self.convert_to_markdown(ocr_result)
            
            # 添加到结果中
            ocr_result["markdown_text"] = markdown_text
            
            # 记录处理时间
            ocr_result["markdown_generated_at"] = datetime.utcnow().isoformat()
            
            return ocr_result
        except Exception as e:
            self.logger.error(f"处理OCR结果时出错: {str(e)}")
            raise 
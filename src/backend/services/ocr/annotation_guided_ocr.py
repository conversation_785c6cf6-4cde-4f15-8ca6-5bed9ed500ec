"""
基于人工标注的OCR处理器
利用人工标注的区域信息，提高OCR处理的准确性和结构化程度
"""

import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import json
import logging
from PIL import Image
from pillow_heif import register_heif_opener

from .processor import OCRProcessor
from .types import OCRResult
from .interactive_annotator import InteractiveAnnotator, AnnotationRegion

# 注册HEIF/HEIC格式支持
register_heif_opener()

@dataclass
class AnnotatedOCRResult:
    """带标注信息的OCR结果"""
    region_id: str
    region_type: str
    order: int
    bbox: Tuple[int, int, int, int]  # x, y, width, height
    text: str
    confidence: float
    raw_ocr_results: List[Dict]  # 原始OCR结果

class AnnotationGuidedOCR:
    """基于人工标注的OCR处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ocr_processor = OCRProcessor()
        self.annotator = InteractiveAnnotator()
        
    def process_with_annotations(self, 
                               image_path: str, 
                               annotation_path: Optional[str] = None,
                               create_annotation: bool = False) -> List[AnnotatedOCRResult]:
        """
        使用标注数据进行OCR处理
        
        Args:
            image_path: 图像文件路径
            annotation_path: 标注文件路径，如果为None则自动推断
            create_annotation: 如果标注文件不存在，是否创建新标注
        
        Returns:
            带标注信息的OCR结果列表
        """
        print(f"🔍 [AnnotationGuidedOCR] 开始处理:")
        print(f"   图像路径: {image_path}")
        print(f"   标注路径: {annotation_path}")
        print(f"   创建标注: {create_annotation}")
        
        image_path = Path(image_path)
        
        # 确定标注文件路径
        if annotation_path is None:
            annotation_path = image_path.with_suffix('.json')
        else:
            annotation_path = Path(annotation_path)
        
        print(f"   最终标注路径: {annotation_path}")
        print(f"   标注文件存在: {annotation_path.exists()}")
        
        # 检查标注文件是否存在
        if not annotation_path.exists():
            if create_annotation:
                self.logger.info(f"标注文件不存在，启动交互式标注: {annotation_path}")
                success = self.annotator.start_annotation(str(image_path))
                if not success:
                    self.logger.error("标注创建失败")
                    return []
            else:
                print(f"⚠️ 标注文件不存在，回退到默认OCR")
                self.logger.warning(f"标注文件不存在: {annotation_path}")
                return self._fallback_ocr(str(image_path))
        
        # 加载图像
        print(f"🖼️ 加载图像...")
        image = self._load_image(str(image_path))
        if image is None:
            print(f"❌ 图像加载失败")
            return []
        
        print(f"✅ 图像加载成功，尺寸: {image.shape}")
        
        # 加载标注数据
        print(f"📝 加载标注数据...")
        annotations = self._load_annotations(str(annotation_path))
        if not annotations:
            print(f"⚠️ 标注数据为空，回退到默认OCR")
            return self._fallback_ocr(str(image_path))
        
        print(f"✅ 加载了 {len(annotations)} 个标注区域")
        
        # 按阅读顺序排序
        annotations.sort(key=lambda x: x.order)
        
        results = []
        
        for i, annotation in enumerate(annotations, 1):
            print(f"🔍 处理标注区域 {i}/{len(annotations)}: {annotation.id}")
            
            # 提取区域图像
            region_image = self._extract_region(image, annotation)
            if region_image is None:
                print(f"❌ 区域提取失败: {annotation.id}")
                continue
            
            print(f"✅ 区域提取成功，尺寸: {region_image.shape}")
            
            # 对区域进行OCR处理
            region_result = self._process_region_ocr(
                region_image, 
                annotation,
                str(image_path)
            )
            
            if region_result:
                print(f"✅ OCR处理成功: {region_result.text[:50]}...")
                results.append(region_result)
            else:
                print(f"❌ OCR处理失败: {annotation.id}")
        
        print(f"🎯 处理完成，共 {len(results)} 个有效结果")
        self.logger.info(f"处理完成，共 {len(results)} 个标注区域")
        return results
    
    def _load_image(self, image_path: str) -> Optional[np.ndarray]:
        """加载图像"""
        try:
            image_path = Path(image_path)
            
            if image_path.suffix.lower() in ['.heic', '.heif']:
                # 使用PIL读取HEIC格式
                pil_image = Image.open(image_path)
                if pil_image.mode != 'RGB':
                    pil_image = pil_image.convert('RGB')
                image_array = np.array(pil_image)
                return cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
            else:
                # 使用OpenCV读取其他格式
                return cv2.imread(str(image_path))
                
        except Exception as e:
            self.logger.error(f"加载图像失败: {e}")
            return None
    
    def _load_annotations(self, annotation_path: str) -> List[AnnotationRegion]:
        """加载标注数据"""
        try:
            with open(annotation_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            annotations = [
                AnnotationRegion(**region_data) 
                for region_data in data['regions']
            ]
            
            self.logger.info(f"加载了 {len(annotations)} 个标注区域")
            return annotations
            
        except Exception as e:
            self.logger.error(f"加载标注数据失败: {e}")
            return []
    
    def _extract_region(self, image: np.ndarray, annotation: AnnotationRegion) -> Optional[np.ndarray]:
        """提取标注区域的图像"""
        try:
            x, y, w, h = annotation.x, annotation.y, annotation.width, annotation.height
            
            # 确保坐标在图像范围内
            height, width = image.shape[:2]
            x = max(0, min(x, width - 1))
            y = max(0, min(y, height - 1))
            w = min(w, width - x)
            h = min(h, height - y)
            
            if w <= 0 or h <= 0:
                self.logger.warning(f"无效的区域尺寸: {annotation.id}")
                return None
            
            region = image[y:y+h, x:x+w]
            return region
            
        except Exception as e:
            self.logger.error(f"提取区域失败: {e}")
            return None
    
    def _process_region_ocr(self, 
                          region_image: np.ndarray, 
                          annotation: AnnotationRegion,
                          original_image_path: str) -> Optional[AnnotatedOCRResult]:
        """对单个区域进行OCR处理"""
        try:
            print(f"🔍 [_process_region_ocr] 开始处理区域: {annotation.id}")
            print(f"   区域类型: {annotation.region_type}")
            print(f"   区域尺寸: {region_image.shape}")
            
            # 根据区域类型选择不同的处理策略
            if annotation.region_type == 'table':
                # 表格区域特殊处理
                print(f"📊 处理表格区域...")
                text, confidence, raw_results = self._process_table_region(region_image)
            elif annotation.region_type == 'image':
                # 图像区域可能包含文字
                print(f"🖼️ 处理图像区域...")
                text, confidence, raw_results = self._process_image_region(region_image)
            else:
                # 普通文本区域
                print(f"📝 处理文本区域...")
                text, confidence, raw_results = self._process_text_region(
                    region_image, annotation.region_type
                )
            
            print(f"📊 OCR处理结果:")
            print(f"   文本: '{text}'")
            print(f"   置信度: {confidence}")
            print(f"   原始结果数量: {len(raw_results)}")
            
            if not text.strip():
                print(f"⚠️ 文本为空，返回None")
                return None
            
            result = AnnotatedOCRResult(
                region_id=annotation.id,
                region_type=annotation.region_type,
                order=annotation.order,
                bbox=(annotation.x, annotation.y, annotation.width, annotation.height),
                text=text,
                confidence=confidence,
                raw_ocr_results=raw_results
            )
            
            print(f"✅ 创建AnnotatedOCRResult成功")
            return result
            
        except Exception as e:
            print(f"❌ [_process_region_ocr] 异常: {type(e).__name__}: {e}")
            import traceback
            traceback.print_exc()
            self.logger.error(f"处理区域OCR失败: {e}")
            return None
    
    def _process_text_region(self, region_image: np.ndarray, region_type: str) -> Tuple[str, float, List[Dict]]:
        """处理文本区域"""
        # 根据区域类型进行预处理
        if region_type in ['left_column', 'right_column']:
            # 列文本可能需要特殊的预处理
            processed_image = self._preprocess_column_text(region_image)
        else:
            # 普通文本预处理
            processed_image = self._preprocess_text_image(region_image)
        
        # 使用OCR处理器
        results = self.ocr_processor.ocr.ocr(processed_image, cls=True)
        
        if not results or not results[0]:
            return "", 0.0, []
        
        # 提取文本和置信度
        text_lines = []
        confidences = []
        raw_results = []
        
        for line in results[0]:
            if len(line) >= 2:
                bbox, (text, conf) = line[0], line[1]
                if text.strip():
                    text_lines.append(text.strip())
                    confidences.append(conf)
                    raw_results.append({
                        'bbox': bbox,
                        'text': text,
                        'confidence': conf
                    })
        
        combined_text = '\n'.join(text_lines)
        avg_confidence = np.mean(confidences) if confidences else 0.0
        
        return combined_text, avg_confidence, raw_results
    
    def _process_table_region(self, region_image: np.ndarray) -> Tuple[str, float, List[Dict]]:
        """处理表格区域"""
        # 表格预处理
        processed_image = self._preprocess_table_image(region_image)
        
        # OCR处理
        results = self.ocr_processor.ocr.ocr(processed_image, cls=True)
        
        if not results or not results[0]:
            return "", 0.0, []
        
        # 表格文本结构化处理
        text_blocks = []
        confidences = []
        raw_results = []
        
        for line in results[0]:
            if len(line) >= 2:
                bbox, (text, conf) = line[0], line[1]
                if text.strip():
                    text_blocks.append({
                        'text': text.strip(),
                        'bbox': bbox,
                        'confidence': conf
                    })
                    confidences.append(conf)
                    raw_results.append({
                        'bbox': bbox,
                        'text': text,
                        'confidence': conf
                    })
        
        # 尝试重建表格结构
        structured_text = self._reconstruct_table_structure(text_blocks)
        avg_confidence = np.mean(confidences) if confidences else 0.0
        
        return structured_text, avg_confidence, raw_results
    
    def _process_image_region(self, region_image: np.ndarray) -> Tuple[str, float, List[Dict]]:
        """处理图像区域（可能包含文字）"""
        # 图像区域的文字可能比较特殊，需要增强处理
        processed_image = self._preprocess_image_text(region_image)
        
        results = self.ocr_processor.ocr.ocr(processed_image, cls=True)
        
        if not results or not results[0]:
            return "[图像区域]", 0.5, []
        
        # 提取图像中的文字
        text_lines = []
        confidences = []
        raw_results = []
        
        for line in results[0]:
            if len(line) >= 2:
                bbox, (text, conf) = line[0], line[1]
                if text.strip():
                    text_lines.append(text.strip())
                    confidences.append(conf)
                    raw_results.append({
                        'bbox': bbox,
                        'text': text,
                        'confidence': conf
                    })
        
        if text_lines:
            combined_text = f"[图像区域包含文字]\n" + '\n'.join(text_lines)
            avg_confidence = np.mean(confidences)
        else:
            combined_text = "[图像区域 - 无文字]"
            avg_confidence = 0.5
        
        return combined_text, avg_confidence, raw_results
    
    def _preprocess_column_text(self, image: np.ndarray) -> np.ndarray:
        """预处理列文本"""
        # 列文本可能行间距较小，需要特殊处理
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 自适应阈值
        binary = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # 轻微的形态学操作
        kernel = np.ones((1, 2), np.uint8)
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return binary
    
    def _preprocess_text_image(self, image: np.ndarray) -> np.ndarray:
        """预处理普通文本图像"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 直方图均衡化
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        
        # 自适应阈值
        binary = cv2.adaptiveThreshold(
            enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        return binary
    
    def _preprocess_table_image(self, image: np.ndarray) -> np.ndarray:
        """预处理表格图像"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 增强对比度
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        
        # 二值化
        _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 去除表格线条（可选）
        # 这里可以添加更复杂的表格线检测和去除逻辑
        
        return binary
    
    def _preprocess_image_text(self, image: np.ndarray) -> np.ndarray:
        """预处理图像中的文字"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 强化对比度
        clahe = cv2.createCLAHE(clipLimit=4.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        
        # 多种阈值方法尝试
        binary1 = cv2.adaptiveThreshold(
            enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        _, binary2 = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 选择更好的二值化结果
        # 这里简单地使用自适应阈值
        return binary1
    
    def _reconstruct_table_structure(self, text_blocks: List[Dict]) -> str:
        """重建表格结构"""
        if not text_blocks:
            return ""
        
        # 按Y坐标排序（行）
        text_blocks.sort(key=lambda x: x['bbox'][0][1])
        
        # 简单的表格重建：按行分组
        rows = []
        current_row = []
        current_y = text_blocks[0]['bbox'][0][1]
        y_threshold = 20  # Y坐标差异阈值
        
        for block in text_blocks:
            block_y = block['bbox'][0][1]
            
            if abs(block_y - current_y) <= y_threshold:
                # 同一行
                current_row.append(block)
            else:
                # 新行
                if current_row:
                    # 按X坐标排序（列）
                    current_row.sort(key=lambda x: x['bbox'][0][0])
                    row_text = ' | '.join([b['text'] for b in current_row])
                    rows.append(row_text)
                
                current_row = [block]
                current_y = block_y
        
        # 处理最后一行
        if current_row:
            current_row.sort(key=lambda x: x['bbox'][0][0])
            row_text = ' | '.join([b['text'] for b in current_row])
            rows.append(row_text)
        
        return '\n'.join(rows)
    
    def _fallback_ocr(self, image_path: str) -> List[AnnotatedOCRResult]:
        """回退到普通OCR处理"""
        self.logger.info("使用回退OCR处理")
        
        try:
            # 使用普通OCR处理器
            result = self.ocr_processor.process_image(image_path)
            
            if result and result.text:
                return [AnnotatedOCRResult(
                    region_id="fallback_1",
                    region_type="single_column",
                    order=1,
                    bbox=(0, 0, result.image_width, result.image_height),
                    text=result.text,
                    confidence=result.confidence,
                    raw_ocr_results=[]
                )]
            
        except Exception as e:
            self.logger.error(f"回退OCR处理失败: {e}")
        
        return []
    
    def export_results(self, results: List[AnnotatedOCRResult], output_path: str):
        """导出OCR结果"""
        output_data = {
            'total_regions': len(results),
            'processed_at': self._get_timestamp(),
            'results': []
        }
        
        for result in results:
            output_data['results'].append({
                'region_id': result.region_id,
                'region_type': result.region_type,
                'order': result.order,
                'bbox': result.bbox,
                'text': result.text,
                'confidence': result.confidence,
                'raw_ocr_count': len(result.raw_ocr_results)
            })
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"OCR结果已导出到: {output_path}")
    
    def generate_structured_text(self, results: List[AnnotatedOCRResult]) -> str:
        """生成结构化文本"""
        if not results:
            return ""
        
        # 按阅读顺序排序
        results.sort(key=lambda x: x.order)
        
        structured_parts = []
        
        for result in results:
            # 添加区域标识
            region_header = f"\n=== {result.region_type.upper()} (区域 {result.order}) ==="
            structured_parts.append(region_header)
            structured_parts.append(result.text)
            structured_parts.append("")  # 空行分隔
        
        return '\n'.join(structured_parts)
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()

def main():
    """主函数 - 演示如何使用标注引导的OCR"""
    import sys
    
    if len(sys.argv) < 2:
        print("用法: python annotation_guided_ocr.py <图片路径> [标注文件路径]")
        print("示例: python annotation_guided_ocr.py pic/IMG_1320.heic")
        return
    
    image_path = sys.argv[1]
    annotation_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 创建处理器
    guided_ocr = AnnotationGuidedOCR()
    
    # 处理图像
    results = guided_ocr.process_with_annotations(
        image_path, 
        annotation_path, 
        create_annotation=True  # 如果没有标注文件，创建新的
    )
    
    if results:
        print(f"\n处理完成！共识别 {len(results)} 个区域：")
        
        for result in results:
            print(f"\n区域 {result.order} ({result.region_type}):")
            print(f"位置: {result.bbox}")
            print(f"置信度: {result.confidence:.2f}")
            print(f"文本: {result.text[:100]}...")
        
        # 导出结果
        output_path = Path(image_path).with_suffix('_annotated_ocr.json')
        guided_ocr.export_results(results, str(output_path))
        
        # 生成结构化文本
        structured_text = guided_ocr.generate_structured_text(results)
        text_output_path = Path(image_path).with_suffix('_structured.txt')
        with open(text_output_path, 'w', encoding='utf-8') as f:
            f.write(structured_text)
        
        print(f"\n结果已保存到:")
        print(f"- JSON: {output_path}")
        print(f"- 文本: {text_output_path}")
    else:
        print("OCR处理失败或无结果")

if __name__ == "__main__":
    main() 
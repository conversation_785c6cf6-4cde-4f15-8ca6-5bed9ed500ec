#!/usr/bin/env python3
"""
标注系统服务 - 集成OCR工作流
支持OCR结果标注、数据流管理和JSON格式数据交换
"""

import sys
import os
from pathlib import Path
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Union, Any
import logging
from dataclasses import dataclass, asdict
from enum import Enum

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.backend.models.mongodb import MongoDBManager
from src.backend.services.ocr.types import OCRResult
from src.backend.logger import get_logger

# 设置日志
logger = get_logger(__name__)

class AnnotationStatus(Enum):
    """标注状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    REVIEWED = "reviewed"
    REJECTED = "rejected"

class AnnotationType(Enum):
    """标注类型枚举"""
    TEXT_CORRECTION = "text_correction"
    BOUNDING_BOX = "bounding_box"
    CLASSIFICATION = "classification"
    LAYOUT_ANNOTATION = "layout_annotation"
    QUALITY_RATING = "quality_rating"

@dataclass
class AnnotationData:
    """标注数据结构"""
    annotation_id: str
    ocr_result_id: str
    annotation_type: AnnotationType
    original_text: str
    corrected_text: Optional[str] = None
    bounding_box: Optional[List[List[int]]] = None
    classification: Optional[str] = None
    quality_score: Optional[float] = None
    confidence: Optional[float] = None
    annotator_id: str = "system"
    status: AnnotationStatus = AnnotationStatus.PENDING
    created_at: datetime = None
    updated_at: datetime = None
    metadata: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
        if self.metadata is None:
            self.metadata = {}

@dataclass
class AnnotationTask:
    """标注任务结构"""
    task_id: str
    file_id: str
    ocr_results: List[OCRResult]
    annotations: List[AnnotationData]
    status: AnnotationStatus = AnnotationStatus.PENDING
    priority: int = 1
    assigned_to: Optional[str] = None
    created_at: datetime = None
    due_date: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.metadata is None:
            self.metadata = {}

class AnnotationService:
    """标注系统服务类"""
    
    def __init__(self, db_manager: MongoDBManager = None):
        """
        初始化标注服务
        
        Args:
            db_manager: MongoDB管理器实例
        """
        self.db_manager = db_manager or MongoDBManager()
        self.logger = logging.getLogger(__name__)
        
        # 确保数据库连接
        if not self.db_manager.connect():
            raise ConnectionError("无法连接到MongoDB数据库")
    
    def create_annotation_task(
        self, 
        file_id: str, 
        ocr_results: List[OCRResult],
        priority: int = 1,
        assigned_to: Optional[str] = None,
        due_date: Optional[datetime] = None
    ) -> AnnotationTask:
        """
        创建标注任务
        
        Args:
            file_id: 文件ID
            ocr_results: OCR识别结果列表
            priority: 任务优先级 (1-5, 5最高)
            assigned_to: 分配给的标注员
            due_date: 截止日期
            
        Returns:
            AnnotationTask: 创建的标注任务
        """
        task_id = str(uuid.uuid4())
        
        # 为每个OCR结果创建初始标注
        annotations = []
        for ocr_result in ocr_results:
            annotation = AnnotationData(
                annotation_id=str(uuid.uuid4()),
                ocr_result_id=getattr(ocr_result, 'id', str(uuid.uuid4())),
                annotation_type=AnnotationType.TEXT_CORRECTION,
                original_text=ocr_result.text,
                bounding_box=ocr_result.box,
                confidence=ocr_result.confidence
            )
            annotations.append(annotation)
        
        # 创建标注任务
        task = AnnotationTask(
            task_id=task_id,
            file_id=file_id,
            ocr_results=ocr_results,
            annotations=annotations,
            priority=priority,
            assigned_to=assigned_to,
            due_date=due_date
        )
        
        # 保存到数据库
        self._save_annotation_task(task)
        
        self.logger.info(f"创建标注任务: {task_id}, 包含 {len(annotations)} 个标注项")
        return task
    
    def get_annotation_task(self, task_id: str) -> Optional[AnnotationTask]:
        """
        获取标注任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            AnnotationTask: 标注任务，如果不存在返回None
        """
        try:
            task_data = self.db_manager.get_annotation_task(task_id)
            if task_data:
                return self._dict_to_annotation_task(task_data)
            return None
        except Exception as e:
            self.logger.error(f"获取标注任务失败: {e}")
            return None
    
    def update_annotation(
        self, 
        annotation_id: str, 
        corrected_text: Optional[str] = None,
        bounding_box: Optional[List[List[int]]] = None,
        classification: Optional[str] = None,
        quality_score: Optional[float] = None,
        status: Optional[AnnotationStatus] = None
    ) -> bool:
        """
        更新标注数据
        
        Args:
            annotation_id: 标注ID
            corrected_text: 修正后的文本
            bounding_box: 边界框坐标
            classification: 分类标签
            quality_score: 质量评分
            status: 标注状态
            
        Returns:
            bool: 更新是否成功
        """
        try:
            update_data = {
                "updated_at": datetime.now()
            }
            
            if corrected_text is not None:
                update_data["corrected_text"] = corrected_text
            if bounding_box is not None:
                update_data["bounding_box"] = bounding_box
            if classification is not None:
                update_data["classification"] = classification
            if quality_score is not None:
                update_data["quality_score"] = quality_score
            if status is not None:
                update_data["status"] = status.value
            
            success = self.db_manager.update_annotation(annotation_id, update_data)
            
            if success:
                self.logger.info(f"更新标注成功: {annotation_id}")
            else:
                self.logger.warning(f"更新标注失败: {annotation_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"更新标注时出错: {e}")
            return False
    
    def submit_annotation_task(self, task_id: str) -> bool:
        """
        提交标注任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 提交是否成功
        """
        try:
            # 更新任务状态为已完成
            update_data = {
                "status": AnnotationStatus.COMPLETED.value,
                "updated_at": datetime.now()
            }
            
            success = self.db_manager.update_annotation_task(task_id, update_data)
            
            if success:
                self.logger.info(f"提交标注任务成功: {task_id}")
                # 触发训练数据更新
                self._update_training_data(task_id)
            else:
                self.logger.warning(f"提交标注任务失败: {task_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"提交标注任务时出错: {e}")
            return False
    
    def get_pending_tasks(self, assigned_to: Optional[str] = None, limit: int = 10) -> List[AnnotationTask]:
        """
        获取待处理的标注任务
        
        Args:
            assigned_to: 分配给的标注员
            limit: 返回任务数量限制
            
        Returns:
            List[AnnotationTask]: 待处理任务列表
        """
        try:
            filter_criteria = {"status": AnnotationStatus.PENDING.value}
            if assigned_to:
                filter_criteria["assigned_to"] = assigned_to
            
            tasks_data = self.db_manager.get_annotation_tasks(filter_criteria, limit)
            tasks = [self._dict_to_annotation_task(task_data) for task_data in tasks_data]
            
            self.logger.info(f"获取到 {len(tasks)} 个待处理标注任务")
            return tasks
            
        except Exception as e:
            self.logger.error(f"获取待处理任务时出错: {e}")
            return []
    
    def export_annotations_to_json(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        导出标注数据为JSON格式
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: JSON格式的标注数据
        """
        try:
            task = self.get_annotation_task(task_id)
            if not task:
                return None
            
            # 转换为可序列化的字典
            export_data = {
                "task_id": task.task_id,
                "file_id": task.file_id,
                "status": task.status.value,
                "created_at": task.created_at.isoformat(),
                "annotations": []
            }
            
            for annotation in task.annotations:
                annotation_dict = {
                    "annotation_id": annotation.annotation_id,
                    "ocr_result_id": annotation.ocr_result_id,
                    "annotation_type": annotation.annotation_type.value,
                    "original_text": annotation.original_text,
                    "corrected_text": annotation.corrected_text,
                    "bounding_box": annotation.bounding_box,
                    "classification": annotation.classification,
                    "quality_score": annotation.quality_score,
                    "confidence": annotation.confidence,
                    "status": annotation.status.value,
                    "created_at": annotation.created_at.isoformat(),
                    "updated_at": annotation.updated_at.isoformat()
                }
                export_data["annotations"].append(annotation_dict)
            
            self.logger.info(f"导出标注数据: {task_id}")
            return export_data
            
        except Exception as e:
            self.logger.error(f"导出标注数据时出错: {e}")
            return None
    
    def import_annotations_from_json(self, json_data: Dict[str, Any]) -> bool:
        """
        从JSON格式导入标注数据
        
        Args:
            json_data: JSON格式的标注数据
            
        Returns:
            bool: 导入是否成功
        """
        try:
            task_id = json_data.get("task_id")
            if not task_id:
                self.logger.error("JSON数据中缺少task_id")
                return False
            
            # 更新标注数据
            for annotation_data in json_data.get("annotations", []):
                annotation_id = annotation_data.get("annotation_id")
                if not annotation_id:
                    continue
                
                update_data = {
                    "corrected_text": annotation_data.get("corrected_text"),
                    "classification": annotation_data.get("classification"),
                    "quality_score": annotation_data.get("quality_score"),
                    "status": annotation_data.get("status", AnnotationStatus.PENDING.value),
                    "updated_at": datetime.now()
                }
                
                self.db_manager.update_annotation(annotation_id, update_data)
            
            self.logger.info(f"导入标注数据成功: {task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"导入标注数据时出错: {e}")
            return False
    
    def get_annotation_statistics(self) -> Dict[str, Any]:
        """
        获取标注统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            stats = {
                "total_tasks": 0,
                "pending_tasks": 0,
                "completed_tasks": 0,
                "total_annotations": 0,
                "accuracy_improvement": 0.0,
                "average_quality_score": 0.0
            }
            
            # 获取任务统计
            all_tasks = self.db_manager.get_annotation_tasks({})
            stats["total_tasks"] = len(all_tasks)
            
            pending_tasks = [t for t in all_tasks if t.get("status") == AnnotationStatus.PENDING.value]
            stats["pending_tasks"] = len(pending_tasks)
            
            completed_tasks = [t for t in all_tasks if t.get("status") == AnnotationStatus.COMPLETED.value]
            stats["completed_tasks"] = len(completed_tasks)
            
            # 获取标注统计
            all_annotations = self.db_manager.get_all_annotations()
            stats["total_annotations"] = len(all_annotations)
            
            # 计算平均质量评分
            quality_scores = [a.get("quality_score", 0) for a in all_annotations if a.get("quality_score")]
            if quality_scores:
                stats["average_quality_score"] = sum(quality_scores) / len(quality_scores)
            
            self.logger.info("获取标注统计信息成功")
            return stats
            
        except Exception as e:
            self.logger.error(f"获取标注统计信息时出错: {e}")
            return {}
    
    def _save_annotation_task(self, task: AnnotationTask) -> bool:
        """保存标注任务到数据库"""
        try:
            task_dict = self._annotation_task_to_dict(task)
            return self.db_manager.save_annotation_task(task_dict)
        except Exception as e:
            self.logger.error(f"保存标注任务时出错: {e}")
            return False
    
    def _annotation_task_to_dict(self, task: AnnotationTask) -> Dict[str, Any]:
        """将标注任务转换为字典"""
        return {
            "task_id": task.task_id,
            "file_id": task.file_id,
            "ocr_results": [asdict(result) for result in task.ocr_results],
            "annotations": [self._annotation_to_dict(ann) for ann in task.annotations],
            "status": task.status.value,
            "priority": task.priority,
            "assigned_to": task.assigned_to,
            "created_at": task.created_at,
            "due_date": task.due_date,
            "metadata": task.metadata
        }
    
    def _annotation_to_dict(self, annotation: AnnotationData) -> Dict[str, Any]:
        """将标注数据转换为字典"""
        return {
            "annotation_id": annotation.annotation_id,
            "ocr_result_id": annotation.ocr_result_id,
            "annotation_type": annotation.annotation_type.value,
            "original_text": annotation.original_text,
            "corrected_text": annotation.corrected_text,
            "bounding_box": annotation.bounding_box,
            "classification": annotation.classification,
            "quality_score": annotation.quality_score,
            "confidence": annotation.confidence,
            "annotator_id": annotation.annotator_id,
            "status": annotation.status.value,
            "created_at": annotation.created_at,
            "updated_at": annotation.updated_at,
            "metadata": annotation.metadata
        }
    
    def _dict_to_annotation_task(self, task_dict: Dict[str, Any]) -> AnnotationTask:
        """将字典转换为标注任务"""
        # 转换OCR结果
        ocr_results = []
        for result_dict in task_dict.get("ocr_results", []):
            ocr_result = OCRResult(
                text=result_dict.get("text", ""),
                confidence=result_dict.get("confidence", 0.0),
                box=result_dict.get("box", []),
                page=result_dict.get("page", 0),
                column=result_dict.get("column", 0),
                element_type=result_dict.get("element_type", "text")
            )
            ocr_results.append(ocr_result)
        
        # 转换标注数据
        annotations = []
        for ann_dict in task_dict.get("annotations", []):
            annotation = AnnotationData(
                annotation_id=ann_dict.get("annotation_id", ""),
                ocr_result_id=ann_dict.get("ocr_result_id", ""),
                annotation_type=AnnotationType(ann_dict.get("annotation_type", "text_correction")),
                original_text=ann_dict.get("original_text", ""),
                corrected_text=ann_dict.get("corrected_text"),
                bounding_box=ann_dict.get("bounding_box"),
                classification=ann_dict.get("classification"),
                quality_score=ann_dict.get("quality_score"),
                confidence=ann_dict.get("confidence"),
                annotator_id=ann_dict.get("annotator_id", "system"),
                status=AnnotationStatus(ann_dict.get("status", "pending")),
                created_at=ann_dict.get("created_at", datetime.now()),
                updated_at=ann_dict.get("updated_at", datetime.now()),
                metadata=ann_dict.get("metadata", {})
            )
            annotations.append(annotation)
        
        return AnnotationTask(
            task_id=task_dict.get("task_id", ""),
            file_id=task_dict.get("file_id", ""),
            ocr_results=ocr_results,
            annotations=annotations,
            status=AnnotationStatus(task_dict.get("status", "pending")),
            priority=task_dict.get("priority", 1),
            assigned_to=task_dict.get("assigned_to"),
            created_at=task_dict.get("created_at", datetime.now()),
            due_date=task_dict.get("due_date"),
            metadata=task_dict.get("metadata", {})
        )
    
    def _update_training_data(self, task_id: str):
        """更新训练数据"""
        try:
            # 获取已完成的标注任务
            task = self.get_annotation_task(task_id)
            if not task or task.status != AnnotationStatus.COMPLETED:
                return
            
            # 将标注数据转换为训练样本
            for annotation in task.annotations:
                if annotation.corrected_text and annotation.corrected_text != annotation.original_text:
                    training_sample = {
                        "sample_id": str(uuid.uuid4()),
                        "original_text": annotation.original_text,
                        "corrected_text": annotation.corrected_text,
                        "bounding_box": annotation.bounding_box,
                        "confidence": annotation.confidence,
                        "quality_score": annotation.quality_score,
                        "source": "annotation",
                        "task_id": task_id,
                        "created_at": datetime.now()
                    }
                    
                    self.db_manager.save_training_sample(training_sample)
            
            self.logger.info(f"更新训练数据完成: {task_id}")
            
        except Exception as e:
            self.logger.error(f"更新训练数据时出错: {e}")

# 工厂函数
def create_annotation_service(db_manager: MongoDBManager = None) -> AnnotationService:
    """
    创建标注服务实例
    
    Args:
        db_manager: MongoDB管理器实例
        
    Returns:
        AnnotationService: 标注服务实例
    """
    return AnnotationService(db_manager)

if __name__ == "__main__":
    # 测试标注服务
    service = create_annotation_service()
    
    # 创建测试OCR结果
    test_ocr_results = [
        OCRResult(
            text="Hello World",
            confidence=0.95,
            box=[[10, 10], [100, 10], [100, 30], [10, 30]]
        ),
        OCRResult(
            text="Test Document",
            confidence=0.88,
            box=[[10, 40], [120, 40], [120, 60], [10, 60]]
        )
    ]
    
    # 创建标注任务
    task = service.create_annotation_task(
        file_id="test_file_001",
        ocr_results=test_ocr_results,
        priority=3
    )
    
    print(f"创建标注任务: {task.task_id}")
    print(f"包含 {len(task.annotations)} 个标注项")
    
    # 获取统计信息
    stats = service.get_annotation_statistics()
    print(f"标注统计: {stats}") 
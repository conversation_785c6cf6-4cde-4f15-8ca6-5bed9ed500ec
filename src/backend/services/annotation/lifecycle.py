#!/usr/bin/env python3
"""
注释任务生命周期管理模块
实现注释任务从创建到完成的完整生命周期管理，包括状态跟踪、转换、通知和统计
"""

import sys
import os
from pathlib import Path
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union, Any, Tuple, Callable
import logging
from dataclasses import dataclass, asdict
from enum import Enum
from bson import ObjectId

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.database.mongodb_models import MongoDBManager
from src.backend.annotation_service import AnnotationService, AnnotationStatus, AnnotationTask, AnnotationData
from src.backend.database.database import Database
from src.backend.logger import get_logger

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskLifecycleStatus(Enum):
    """任务生命周期状态枚举"""
    CREATED = "created"                    # 任务已创建
    ASSIGNED = "assigned"                  # 任务已分配
    IN_PROGRESS = "in_progress"           # 任务进行中
    PAUSED = "paused"                     # 任务暂停
    REVIEW_PENDING = "review_pending"     # 等待审核
    REVIEW_IN_PROGRESS = "review_in_progress"  # 审核中
    REVISION_REQUIRED = "revision_required"    # 需要修订
    COMPLETED = "completed"               # 任务完成
    VERIFIED = "verified"                 # 任务已验证
    ARCHIVED = "archived"                 # 任务已归档
    CANCELLED = "cancelled"               # 任务已取消
    EXPIRED = "expired"                   # 任务已过期

class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5

class TaskEvent(Enum):
    """任务事件类型枚举"""
    CREATED = "created"
    ASSIGNED = "assigned"
    STARTED = "started"
    PAUSED = "paused"
    RESUMED = "resumed"
    SUBMITTED = "submitted"
    REVIEWED = "reviewed"
    APPROVED = "approved"
    REJECTED = "rejected"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    EXPIRED = "expired"

@dataclass
class TaskStatusTransition:
    """任务状态转换记录"""
    from_status: TaskLifecycleStatus
    to_status: TaskLifecycleStatus
    event: TaskEvent
    timestamp: datetime
    user_id: Optional[str] = None
    reason: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class TaskMetrics:
    """任务指标数据"""
    total_regions: int
    completed_regions: int
    reviewed_regions: int
    approved_regions: int
    rejected_regions: int
    avg_confidence: float
    avg_quality_score: float
    completion_percentage: float
    review_percentage: float
    estimated_time_remaining: Optional[float] = None

@dataclass
class TaskDeadline:
    """任务截止时间配置"""
    due_date: datetime
    warning_threshold: timedelta = timedelta(hours=24)  # 截止前24小时警告
    escalation_threshold: timedelta = timedelta(hours=12)  # 截止前12小时升级
    auto_reassign: bool = False  # 是否自动重新分配

@dataclass
class TaskLifecycleEvent:
    """任务生命周期事件"""
    event_id: str
    task_id: str
    event_type: TaskEvent
    timestamp: datetime
    user_id: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    system_generated: bool = False

class TaskLifecycleManager:
    """任务生命周期管理器"""
    
    def __init__(self, db_manager: MongoDBManager = None, annotation_service: AnnotationService = None):
        """
        初始化任务生命周期管理器
        
        Args:
            db_manager: MongoDB管理器实例
            annotation_service: 注释服务实例
        """
        self.db_manager = db_manager or MongoDBManager()
        self.annotation_service = annotation_service or AnnotationService(self.db_manager)
        self.logger = logging.getLogger(__name__)
        
        # 状态转换规则
        self.valid_transitions = self._define_valid_transitions()
        
        # 事件处理器
        self.event_handlers: Dict[TaskEvent, List[Callable]] = {}
        self._register_default_handlers()
        
        # 确保数据库连接
        if not self.db_manager.connect():
            raise ConnectionError("无法连接到MongoDB数据库")
    
    def _define_valid_transitions(self) -> Dict[TaskLifecycleStatus, List[TaskLifecycleStatus]]:
        """定义有效的状态转换规则"""
        return {
            TaskLifecycleStatus.CREATED: [
                TaskLifecycleStatus.ASSIGNED,
                TaskLifecycleStatus.CANCELLED
            ],
            TaskLifecycleStatus.ASSIGNED: [
                TaskLifecycleStatus.IN_PROGRESS,
                TaskLifecycleStatus.CANCELLED,
                TaskLifecycleStatus.EXPIRED
            ],
            TaskLifecycleStatus.IN_PROGRESS: [
                TaskLifecycleStatus.PAUSED,
                TaskLifecycleStatus.REVIEW_PENDING,
                TaskLifecycleStatus.COMPLETED,
                TaskLifecycleStatus.CANCELLED
            ],
            TaskLifecycleStatus.PAUSED: [
                TaskLifecycleStatus.IN_PROGRESS,
                TaskLifecycleStatus.CANCELLED,
                TaskLifecycleStatus.EXPIRED
            ],
            TaskLifecycleStatus.REVIEW_PENDING: [
                TaskLifecycleStatus.REVIEW_IN_PROGRESS,
                TaskLifecycleStatus.REVISION_REQUIRED,
                TaskLifecycleStatus.COMPLETED
            ],
            TaskLifecycleStatus.REVIEW_IN_PROGRESS: [
                TaskLifecycleStatus.REVISION_REQUIRED,
                TaskLifecycleStatus.COMPLETED,
                TaskLifecycleStatus.VERIFIED
            ],
            TaskLifecycleStatus.REVISION_REQUIRED: [
                TaskLifecycleStatus.IN_PROGRESS,
                TaskLifecycleStatus.CANCELLED
            ],
            TaskLifecycleStatus.COMPLETED: [
                TaskLifecycleStatus.VERIFIED,
                TaskLifecycleStatus.ARCHIVED,
                TaskLifecycleStatus.REVISION_REQUIRED
            ],
            TaskLifecycleStatus.VERIFIED: [
                TaskLifecycleStatus.ARCHIVED
            ],
            TaskLifecycleStatus.ARCHIVED: [],  # 终态
            TaskLifecycleStatus.CANCELLED: [],  # 终态
            TaskLifecycleStatus.EXPIRED: [
                TaskLifecycleStatus.ASSIGNED,  # 可以重新分配
                TaskLifecycleStatus.CANCELLED
            ]
        }
    
    def _register_default_handlers(self):
        """注册默认事件处理器"""
        self.register_event_handler(TaskEvent.CREATED, self._handle_task_created)
        self.register_event_handler(TaskEvent.ASSIGNED, self._handle_task_assigned)
        self.register_event_handler(TaskEvent.STARTED, self._handle_task_started)
        self.register_event_handler(TaskEvent.COMPLETED, self._handle_task_completed)
        self.register_event_handler(TaskEvent.EXPIRED, self._handle_task_expired)
    
    def register_event_handler(self, event: TaskEvent, handler: Callable):
        """注册事件处理器"""
        if event not in self.event_handlers:
            self.event_handlers[event] = []
        self.event_handlers[event].append(handler)
    
    def create_task_with_lifecycle(
        self,
        file_id: str,
        ocr_results: List[Any],
        priority: TaskPriority = TaskPriority.MEDIUM,
        assigned_to: Optional[str] = None,
        deadline: Optional[TaskDeadline] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        创建带有生命周期管理的任务
        
        Args:
            file_id: 文件ID
            ocr_results: OCR结果列表
            priority: 任务优先级
            assigned_to: 分配给的用户
            deadline: 截止时间配置
            metadata: 任务元数据
            
        Returns:
            str: 任务ID
        """
        try:
            # 创建基础任务
            task = self.annotation_service.create_annotation_task(
                file_id=file_id,
                ocr_results=ocr_results,
                priority=priority.value,
                assigned_to=assigned_to,
                due_date=deadline.due_date if deadline else None
            )
            
            # 初始化生命周期数据
            lifecycle_data = {
                "task_id": task.task_id,
                "current_status": TaskLifecycleStatus.CREATED.value,
                "priority": priority.value,
                "deadline": self._serialize_deadline(deadline) if deadline else None,
                "metadata": metadata or {},
                "status_history": [],
                "events": [],
                "metrics": self._calculate_initial_metrics(task),
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
            
            # 保存生命周期数据
            self.db_manager.save_task_lifecycle(lifecycle_data)
            
            # 触发创建事件
            self._trigger_event(task.task_id, TaskEvent.CREATED, user_id=assigned_to)
            
            # 如果已分配，触发分配事件
            if assigned_to:
                self.transition_task_status(
                    task.task_id, 
                    TaskLifecycleStatus.ASSIGNED, 
                    TaskEvent.ASSIGNED,
                    user_id=assigned_to
                )
            
            self.logger.info(f"创建带生命周期管理的任务: {task.task_id}")
            return task.task_id
            
        except Exception as e:
            self.logger.error(f"创建任务失败: {e}")
            raise
    
    def transition_task_status(
        self,
        task_id: str,
        new_status: TaskLifecycleStatus,
        event: TaskEvent,
        user_id: Optional[str] = None,
        reason: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        转换任务状态
        
        Args:
            task_id: 任务ID
            new_status: 新状态
            event: 触发事件
            user_id: 操作用户ID
            reason: 转换原因
            metadata: 附加元数据
            
        Returns:
            bool: 转换是否成功
        """
        try:
            # 获取当前状态
            lifecycle_data = self.db_manager.get_task_lifecycle(task_id)
            if not lifecycle_data:
                self.logger.error(f"任务生命周期数据不存在: {task_id}")
                return False
            
            current_status = TaskLifecycleStatus(lifecycle_data["current_status"])
            
            # 验证状态转换是否有效
            if not self._is_valid_transition(current_status, new_status):
                self.logger.warning(f"无效的状态转换: {current_status.value} -> {new_status.value}")
                return False
            
            # 创建状态转换记录
            transition = TaskStatusTransition(
                from_status=current_status,
                to_status=new_status,
                event=event,
                timestamp=datetime.now(),
                user_id=user_id,
                reason=reason,
                metadata=metadata
            )
            
            # 更新生命周期数据
            lifecycle_data["current_status"] = new_status.value
            lifecycle_data["status_history"].append(self._serialize_transition(transition))
            lifecycle_data["updated_at"] = datetime.now()
            
            # 更新指标
            lifecycle_data["metrics"] = self._update_task_metrics(task_id)
            
            # 保存更新
            success = self.db_manager.update_task_lifecycle(task_id, lifecycle_data)
            
            if success:
                # 触发事件
                self._trigger_event(task_id, event, user_id=user_id, details=metadata)
                self.logger.info(f"任务状态转换成功: {task_id} {current_status.value} -> {new_status.value}")
                return True
            else:
                self.logger.error(f"任务状态转换失败: {task_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"任务状态转换异常: {e}")
            return False
    
    def get_task_lifecycle_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务生命周期状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 生命周期状态数据
        """
        try:
            lifecycle_data = self.db_manager.get_task_lifecycle(task_id)
            if not lifecycle_data:
                return None
            
            # 添加实时计算的字段
            lifecycle_data["time_since_creation"] = (
                datetime.now() - lifecycle_data["created_at"]
            ).total_seconds()
            
            lifecycle_data["time_since_last_update"] = (
                datetime.now() - lifecycle_data["updated_at"]
            ).total_seconds()
            
            # 检查是否过期
            if self._is_task_expired(lifecycle_data):
                lifecycle_data["is_expired"] = True
                lifecycle_data["expiry_reason"] = self._get_expiry_reason(lifecycle_data)
            else:
                lifecycle_data["is_expired"] = False
            
            return lifecycle_data
            
        except Exception as e:
            self.logger.error(f"获取任务生命周期状态失败: {e}")
            return None
    
    def get_tasks_by_status(
        self, 
        status: TaskLifecycleStatus,
        assigned_to: Optional[str] = None,
        priority: Optional[TaskPriority] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        根据状态获取任务列表
        
        Args:
            status: 任务状态
            assigned_to: 分配给的用户
            priority: 优先级过滤
            limit: 返回数量限制
            
        Returns:
            List[Dict[str, Any]]: 任务列表
        """
        try:
            filter_criteria = {"current_status": status.value}
            
            if priority:
                filter_criteria["priority"] = priority.value
            
            tasks = self.db_manager.get_task_lifecycles(filter_criteria, limit)
            
            # 如果指定了分配用户，需要从annotation_task中过滤
            if assigned_to:
                filtered_tasks = []
                for task in tasks:
                    annotation_task = self.annotation_service.get_annotation_task(task["task_id"])
                    if annotation_task and annotation_task.assigned_to == assigned_to:
                        filtered_tasks.append(task)
                tasks = filtered_tasks
            
            return tasks
            
        except Exception as e:
            self.logger.error(f"根据状态获取任务失败: {e}")
            return []
    
    def get_overdue_tasks(self) -> List[Dict[str, Any]]:
        """获取过期任务列表"""
        try:
            current_time = datetime.now()
            overdue_tasks = []
            
            # 获取所有活跃任务
            active_statuses = [
                TaskLifecycleStatus.ASSIGNED,
                TaskLifecycleStatus.IN_PROGRESS,
                TaskLifecycleStatus.PAUSED,
                TaskLifecycleStatus.REVIEW_PENDING,
                TaskLifecycleStatus.REVIEW_IN_PROGRESS
            ]
            
            for status in active_statuses:
                tasks = self.get_tasks_by_status(status)
                for task in tasks:
                    if self._is_task_expired(task):
                        overdue_tasks.append(task)
            
            return overdue_tasks
            
        except Exception as e:
            self.logger.error(f"获取过期任务失败: {e}")
            return []
    
    def get_task_statistics(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        try:
            stats = {
                "total_tasks": 0,
                "by_status": {},
                "by_priority": {},
                "completion_rate": 0.0,
                "average_completion_time": 0.0,
                "overdue_count": 0,
                "active_tasks": 0
            }
            
            # 获取所有任务
            all_tasks = self.db_manager.get_task_lifecycles({})
            stats["total_tasks"] = len(all_tasks)
            
            # 按状态统计
            for status in TaskLifecycleStatus:
                count = len([t for t in all_tasks if t["current_status"] == status.value])
                stats["by_status"][status.value] = count
            
            # 按优先级统计
            for priority in TaskPriority:
                count = len([t for t in all_tasks if t["priority"] == priority.value])
                stats["by_priority"][priority.name.lower()] = count
            
            # 计算完成率
            completed_count = stats["by_status"].get("completed", 0) + stats["by_status"].get("verified", 0)
            if stats["total_tasks"] > 0:
                stats["completion_rate"] = completed_count / stats["total_tasks"]
            
            # 计算活跃任务数
            active_statuses = ["assigned", "in_progress", "paused", "review_pending", "review_in_progress"]
            stats["active_tasks"] = sum(stats["by_status"].get(status, 0) for status in active_statuses)
            
            # 计算过期任务数
            stats["overdue_count"] = len(self.get_overdue_tasks())
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取任务统计信息失败: {e}")
            return {}
    
    def process_expired_tasks(self) -> int:
        """处理过期任务"""
        try:
            overdue_tasks = self.get_overdue_tasks()
            processed_count = 0
            
            for task in overdue_tasks:
                task_id = task["task_id"]
                current_status = TaskLifecycleStatus(task["current_status"])
                
                # 根据当前状态决定处理方式
                if current_status in [TaskLifecycleStatus.ASSIGNED, TaskLifecycleStatus.PAUSED]:
                    # 标记为过期
                    success = self.transition_task_status(
                        task_id,
                        TaskLifecycleStatus.EXPIRED,
                        TaskEvent.EXPIRED,
                        reason="任务超过截止时间"
                    )
                    if success:
                        processed_count += 1
                
                elif current_status == TaskLifecycleStatus.IN_PROGRESS:
                    # 可以选择自动提交审核或标记过期
                    success = self.transition_task_status(
                        task_id,
                        TaskLifecycleStatus.REVIEW_PENDING,
                        TaskEvent.SUBMITTED,
                        reason="任务超时自动提交审核"
                    )
                    if success:
                        processed_count += 1
            
            self.logger.info(f"处理过期任务完成，共处理 {processed_count} 个任务")
            return processed_count
            
        except Exception as e:
            self.logger.error(f"处理过期任务失败: {e}")
            return 0
    
    def _is_valid_transition(self, from_status: TaskLifecycleStatus, to_status: TaskLifecycleStatus) -> bool:
        """检查状态转换是否有效"""
        valid_targets = self.valid_transitions.get(from_status, [])
        return to_status in valid_targets
    
    def _calculate_initial_metrics(self, task: AnnotationTask) -> Dict[str, Any]:
        """计算初始任务指标"""
        total_regions = len(task.annotations)
        
        return {
            "total_regions": total_regions,
            "completed_regions": 0,
            "reviewed_regions": 0,
            "approved_regions": 0,
            "rejected_regions": 0,
            "avg_confidence": sum(a.confidence or 0 for a in task.annotations) / total_regions if total_regions > 0 else 0,
            "avg_quality_score": 0.0,
            "completion_percentage": 0.0,
            "review_percentage": 0.0
        }
    
    def _update_task_metrics(self, task_id: str) -> Dict[str, Any]:
        """更新任务指标"""
        try:
            task = self.annotation_service.get_annotation_task(task_id)
            if not task:
                return {}
            
            total_regions = len(task.annotations)
            completed_regions = len([a for a in task.annotations if a.status == AnnotationStatus.COMPLETED])
            
            # 计算质量评分
            quality_scores = [a.quality_score for a in task.annotations if a.quality_score is not None]
            avg_quality_score = sum(quality_scores) / len(quality_scores) if quality_scores else 0.0
            
            return {
                "total_regions": total_regions,
                "completed_regions": completed_regions,
                "reviewed_regions": 0,  # 需要根据实际审核状态计算
                "approved_regions": 0,
                "rejected_regions": 0,
                "avg_confidence": sum(a.confidence or 0 for a in task.annotations) / total_regions if total_regions > 0 else 0,
                "avg_quality_score": avg_quality_score,
                "completion_percentage": completed_regions / total_regions if total_regions > 0 else 0,
                "review_percentage": 0.0
            }
            
        except Exception as e:
            self.logger.error(f"更新任务指标失败: {e}")
            return {}
    
    def _is_task_expired(self, task_data: Dict[str, Any]) -> bool:
        """检查任务是否过期"""
        try:
            deadline_config = task_data.get("deadline")
            if not deadline_config:
                return False
            
            due_date = deadline_config.get("due_date")
            if not due_date:
                return False
            
            # 如果due_date是字符串，转换为datetime
            if isinstance(due_date, str):
                due_date = datetime.fromisoformat(due_date.replace('Z', '+00:00'))
            
            return datetime.now() > due_date
            
        except Exception as e:
            self.logger.error(f"检查任务过期状态失败: {e}")
            return False
    
    def _get_expiry_reason(self, task_data: Dict[str, Any]) -> str:
        """获取任务过期原因"""
        current_status = task_data.get("current_status")
        time_since_update = (datetime.now() - task_data["updated_at"]).total_seconds()
        
        if current_status == "assigned" and time_since_update > 86400:  # 24小时
            return "任务分配后超过24小时未开始"
        elif current_status == "in_progress" and time_since_update > 172800:  # 48小时
            return "任务进行中超过48小时无更新"
        else:
            return "任务超过截止时间"
    
    def _trigger_event(
        self, 
        task_id: str, 
        event: TaskEvent, 
        user_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """触发任务事件"""
        try:
            # 创建事件记录
            event_record = TaskLifecycleEvent(
                event_id=str(uuid.uuid4()),
                task_id=task_id,
                event_type=event,
                timestamp=datetime.now(),
                user_id=user_id,
                details=details,
                system_generated=user_id is None
            )
            
            # 保存事件记录
            self.db_manager.save_task_event(self._serialize_event(event_record))
            
            # 调用事件处理器
            handlers = self.event_handlers.get(event, [])
            for handler in handlers:
                try:
                    handler(task_id, event_record)
                except Exception as e:
                    self.logger.error(f"事件处理器执行失败: {e}")
            
        except Exception as e:
            self.logger.error(f"触发事件失败: {e}")
    
    def _handle_task_created(self, task_id: str, event: TaskLifecycleEvent):
        """处理任务创建事件"""
        self.logger.info(f"任务已创建: {task_id}")
    
    def _handle_task_assigned(self, task_id: str, event: TaskLifecycleEvent):
        """处理任务分配事件"""
        self.logger.info(f"任务已分配: {task_id} -> {event.user_id}")
    
    def _handle_task_started(self, task_id: str, event: TaskLifecycleEvent):
        """处理任务开始事件"""
        self.logger.info(f"任务已开始: {task_id}")
    
    def _handle_task_completed(self, task_id: str, event: TaskLifecycleEvent):
        """处理任务完成事件"""
        self.logger.info(f"任务已完成: {task_id}")
    
    def _handle_task_expired(self, task_id: str, event: TaskLifecycleEvent):
        """处理任务过期事件"""
        self.logger.warning(f"任务已过期: {task_id}")
    
    def _serialize_deadline(self, deadline: TaskDeadline) -> Dict[str, Any]:
        """序列化截止时间对象"""
        return {
            "due_date": deadline.due_date,
            "warning_threshold_seconds": deadline.warning_threshold.total_seconds(),
            "escalation_threshold_seconds": deadline.escalation_threshold.total_seconds(),
            "auto_reassign": deadline.auto_reassign
        }
    
    def _deserialize_deadline(self, deadline_data: Dict[str, Any]) -> TaskDeadline:
        """反序列化截止时间对象"""
        return TaskDeadline(
            due_date=deadline_data["due_date"],
            warning_threshold=timedelta(seconds=deadline_data["warning_threshold_seconds"]),
            escalation_threshold=timedelta(seconds=deadline_data["escalation_threshold_seconds"]),
            auto_reassign=deadline_data["auto_reassign"]
        )
    
    def _serialize_transition(self, transition: TaskStatusTransition) -> Dict[str, Any]:
        """序列化状态转换记录"""
        return {
            "from_status": transition.from_status.value,
            "to_status": transition.to_status.value,
            "event": transition.event.value,
            "timestamp": transition.timestamp,
            "user_id": transition.user_id,
            "reason": transition.reason,
            "metadata": transition.metadata
        }
    
    def _serialize_event(self, event: TaskLifecycleEvent) -> Dict[str, Any]:
        """序列化事件记录"""
        return {
            "event_id": event.event_id,
            "task_id": event.task_id,
            "event_type": event.event_type.value,
            "timestamp": event.timestamp,
            "user_id": event.user_id,
            "details": event.details,
            "system_generated": event.system_generated
        }

def create_task_lifecycle_manager(
    db_manager: MongoDBManager = None,
    annotation_service: AnnotationService = None
) -> TaskLifecycleManager:
    """创建任务生命周期管理器实例"""
    return TaskLifecycleManager(db_manager, annotation_service)

# 导出主要类和函数
__all__ = [
    'TaskLifecycleStatus',
    'TaskPriority', 
    'TaskEvent',
    'TaskStatusTransition',
    'TaskMetrics',
    'TaskDeadline',
    'TaskLifecycleEvent',
    'TaskLifecycleManager',
    'create_task_lifecycle_manager'
]
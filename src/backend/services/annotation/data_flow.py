#!/usr/bin/env python3
"""
标注数据流管理
处理标注数据的创建、更新、导出等操作
"""

import sys
import os
from pathlib import Path
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Union, Any, Tuple
import logging
from dataclasses import dataclass, asdict
from enum import Enum
from bson import ObjectId

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.backend.services.ocr.integrator import OCRAnnotationIntegrator, AnnotationWorkflowConfig
from src.backend.annotation_service import AnnotationData, AnnotationTask, AnnotationType, AnnotationStatus
from src.database.mongodb_models import MongoDBManager
from src.backend.database.database import Database
from src.backend.logger import get_logger

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class AnnotationChange:
    """注释变更记录"""
    region_id: str
    field_name: str
    old_value: Any
    new_value: Any
    change_type: str  # "text_edit", "confidence_adjust", "bbox_modify", "status_change"
    timestamp: str
    user_id: Optional[str] = None
    confidence_score: Optional[float] = None

@dataclass
class CompletedAnnotation:
    """完成的注释数据"""
    task_id: str
    file_id: str
    region_id: str
    original_text: str
    annotated_text: str
    original_confidence: float
    final_confidence: float
    annotation_type: str
    status: str
    changes: List[AnnotationChange]
    completion_time: str
    total_edit_time: Optional[float] = None
    user_id: Optional[str] = None
    quality_score: Optional[float] = None

@dataclass
class AnnotationBatch:
    """注释批次数据"""
    batch_id: str
    task_id: str
    file_id: str
    completed_annotations: List[CompletedAnnotation]
    batch_statistics: Dict[str, Any]
    completion_time: str
    total_regions: int
    completed_regions: int
    user_id: Optional[str] = None

class AnnotationDataFlowManager:
    """标注数据流管理类"""
    
    def __init__(self, db_manager: MongoDBManager = None):
        """初始化数据流管理器"""
        self.db_manager = db_manager or MongoDBManager()
        self.logger = logging.getLogger(__name__)
        
        # 确保数据库连接
        if not self.db_manager.connect():
            raise ConnectionError("无法连接到MongoDB数据库")
        
        self.db = Database.get_mongodb()
    
    async def create_annotation(self, data: Dict) -> Dict:
        """创建新的标注"""
        try:
            # 添加元数据
            data["created_at"] = datetime.now()
            data["updated_at"] = datetime.now()
            data["status"] = "pending"
            data["system_id"] = "default"
            
            # 插入数据库
            result = await self.db.annotations.insert_one(data)
            
            return {
                "success": True,
                "annotation_id": str(result.inserted_id)
            }
            
        except Exception as e:
            logger.error(f"创建标注失败: {e}")
            return {
                "success": False,
                "message": f"创建标注失败: {str(e)}"
            }
    
    async def update_annotation(self, annotation_id: str, data: Dict) -> Dict:
        """更新标注"""
        try:
            # 更新元数据
            data["updated_at"] = datetime.now()
            
            # 更新数据库
            result = await self.db.annotations.update_one(
                {"_id": ObjectId(annotation_id)},
                {"$set": data}
            )
            
            if result.modified_count == 0:
                return {
                    "success": False,
                    "message": "标注不存在或无需更新"
                }
            
            return {
                "success": True,
                "message": "标注已更新"
            }
            
        except Exception as e:
            logger.error(f"更新标注失败: {e}")
            return {
                "success": False,
                "message": f"更新标注失败: {str(e)}"
            }
    
    async def get_annotation(self, annotation_id: str) -> Dict:
        """获取标注详情"""
        try:
            annotation = await self.db.annotations.find_one(
                {"_id": ObjectId(annotation_id)}
            )
            
            if not annotation:
                return {
                    "success": False,
                    "message": "标注不存在"
                }
            
            # 转换ObjectId为字符串
            annotation["_id"] = str(annotation["_id"])
            
            return {
                "success": True,
                "data": annotation
            }
            
        except Exception as e:
            logger.error(f"获取标注失败: {e}")
            return {
                "success": False,
                "message": f"获取标注失败: {str(e)}"
            }
    
    async def delete_annotation(self, annotation_id: str) -> Dict:
        """删除标注"""
        try:
            result = await self.db.annotations.delete_one(
                {"_id": ObjectId(annotation_id)}
            )
            
            if result.deleted_count == 0:
                return {
                    "success": False,
                    "message": "标注不存在"
                }
            
            return {
                "success": True,
                "message": "标注已删除"
            }
            
        except Exception as e:
            logger.error(f"删除标注失败: {e}")
            return {
                "success": False,
                "message": f"删除标注失败: {str(e)}"
            }
    
    async def list_annotations(
        self,
        filters: Dict = None,
        skip: int = 0,
        limit: int = 20
    ) -> Dict:
        """获取标注列表"""
        try:
            # 构建查询条件
            query = filters or {}
            
            # 执行查询
            annotations = await self.db.annotations.find(query).skip(skip).limit(limit).to_list(None)
            
            # 转换ObjectId为字符串
            for annotation in annotations:
                annotation["_id"] = str(annotation["_id"])
            
            # 获取总数
            total = await self.db.annotations.count_documents(query)
            
            return {
                "success": True,
                "data": {
                    "annotations": annotations,
                    "total": total,
                    "skip": skip,
                    "limit": limit
                }
            }
            
        except Exception as e:
            logger.error(f"获取标注列表失败: {e}")
            return {
                "success": False,
                "message": f"获取标注列表失败: {str(e)}"
            }
    
    async def export_annotations(
        self,
        filters: Dict = None,
        format: str = "json"
    ) -> Dict:
        """导出标注数据"""
        try:
            # 构建查询条件
            query = filters or {}
            
            # 获取所有匹配的标注
            annotations = await self.db.annotations.find(query).to_list(None)
            
            # 转换ObjectId为字符串
            for annotation in annotations:
                annotation["_id"] = str(annotation["_id"])
            
            if format == "json":
                # 导出为JSON
                export_data = json.dumps(annotations, ensure_ascii=False, indent=2)
                content_type = "application/json"
            else:
                return {
                    "success": False,
                    "message": f"不支持的导出格式: {format}"
                }
            
            return {
                "success": True,
                "data": {
                    "content": export_data,
                    "content_type": content_type,
                    "filename": f"annotations_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{format}"
                }
            }
            
        except Exception as e:
            logger.error(f"导出标注失败: {e}")
            return {
                "success": False,
                "message": f"导出标注失败: {str(e)}"
            }
    
    def collect_annotation_changes(
        self, 
        task_id: str, 
        region_updates: List[Dict[str, Any]],
        user_id: Optional[str] = None
    ) -> List[AnnotationChange]:
        """
        收集注释变更记录
        
        Args:
            task_id: 任务ID
            region_updates: 区域更新列表
            user_id: 用户ID
            
        Returns:
            List[AnnotationChange]: 变更记录列表
        """
        self.logger.info(f"收集任务 {task_id} 的注释变更，共 {len(region_updates)} 个更新")
        
        changes = []
        current_time = datetime.now().isoformat()
        
        for update in region_updates:
            region_id = update.get("region_id")
            if not region_id:
                continue
            
            # 检查文本变更
            if "text" in update and "original_text" in update:
                change = AnnotationChange(
                    region_id=region_id,
                    field_name="text",
                    old_value=update["original_text"],
                    new_value=update["text"],
                    change_type="text_edit",
                    timestamp=current_time,
                    user_id=user_id,
                    confidence_score=update.get("confidence")
                )
                changes.append(change)
            
            # 检查置信度调整
            if "confidence" in update and "original_confidence" in update:
                change = AnnotationChange(
                    region_id=region_id,
                    field_name="confidence",
                    old_value=update["original_confidence"],
                    new_value=update["confidence"],
                    change_type="confidence_adjust",
                    timestamp=current_time,
                    user_id=user_id
                )
                changes.append(change)
            
            # 检查边界框修改
            if "bounding_box" in update and "original_bbox" in update:
                change = AnnotationChange(
                    region_id=region_id,
                    field_name="bounding_box",
                    old_value=update["original_bbox"],
                    new_value=update["bounding_box"],
                    change_type="bbox_modify",
                    timestamp=current_time,
                    user_id=user_id
                )
                changes.append(change)
            
            # 检查状态变更
            if "status" in update and "original_status" in update:
                change = AnnotationChange(
                    region_id=region_id,
                    field_name="status",
                    old_value=update["original_status"],
                    new_value=update["status"],
                    change_type="status_change",
                    timestamp=current_time,
                    user_id=user_id
                )
                changes.append(change)
        
        self.logger.info(f"收集到 {len(changes)} 个变更记录")
        return changes
    
    def create_completed_annotation(
        self, 
        task_id: str,
        file_id: str,
        region_data: Dict[str, Any],
        changes: List[AnnotationChange],
        user_id: Optional[str] = None,
        edit_time: Optional[float] = None
    ) -> CompletedAnnotation:
        """
        创建完成的注释记录
        
        Args:
            task_id: 任务ID
            file_id: 文件ID
            region_data: 区域数据
            changes: 变更记录
            user_id: 用户ID
            edit_time: 编辑时间（秒）
            
        Returns:
            CompletedAnnotation: 完成的注释记录
        """
        # 计算质量分数
        quality_score = self._calculate_quality_score(region_data, changes)
        
        completed_annotation = CompletedAnnotation(
            task_id=task_id,
            file_id=file_id,
            region_id=region_data.get("region_id"),
            original_text=region_data.get("original_text", ""),
            annotated_text=region_data.get("text", ""),
            original_confidence=region_data.get("original_confidence", 0.0),
            final_confidence=region_data.get("confidence", 0.0),
            annotation_type=region_data.get("annotation_type", "text_correction"),
            status=region_data.get("status", "completed"),
            changes=changes,
            completion_time=datetime.now().isoformat(),
            total_edit_time=edit_time,
            user_id=user_id,
            quality_score=quality_score
        )
        
        return completed_annotation
    
    def _calculate_quality_score(
        self, 
        region_data: Dict[str, Any], 
        changes: List[AnnotationChange]
    ) -> float:
        """
        计算注释质量分数
        
        Args:
            region_data: 区域数据
            changes: 变更记录
            
        Returns:
            float: 质量分数 (0.0-1.0)
        """
        base_score = 0.8  # 基础分数
        
        # 根据置信度调整
        confidence = region_data.get("confidence", 0.0)
        confidence_bonus = min(0.2, confidence * 0.2)
        
        # 根据变更次数调整（变更越少质量越高）
        change_penalty = min(0.3, len(changes) * 0.05)
        
        # 根据文本长度调整
        text_length = len(region_data.get("text", ""))
        length_bonus = min(0.1, text_length / 100 * 0.1)
        
        quality_score = base_score + confidence_bonus + length_bonus - change_penalty
        return max(0.0, min(1.0, quality_score))
    
    def process_annotation_batch(
        self, 
        task_id: str,
        completed_regions: List[Dict[str, Any]],
        user_id: Optional[str] = None
    ) -> AnnotationBatch:
        """
        处理注释批次
        
        Args:
            task_id: 任务ID
            completed_regions: 完成的区域列表
            user_id: 用户ID
            
        Returns:
            AnnotationBatch: 注释批次数据
        """
        self.logger.info(f"处理任务 {task_id} 的注释批次，共 {len(completed_regions)} 个区域")
        
        batch_id = str(uuid.uuid4())
        completed_annotations = []
        
        # 获取任务信息
        task_data = self.db_manager.get_annotation_task(task_id)
        file_id = task_data.get("file_id") if task_data else "unknown"
        
        for region in completed_regions:
            # 收集该区域的变更
            region_changes = region.get("changes", [])
            changes = []
            
            for change_data in region_changes:
                change = AnnotationChange(
                    region_id=region.get("region_id"),
                    field_name=change_data.get("field_name"),
                    old_value=change_data.get("old_value"),
                    new_value=change_data.get("new_value"),
                    change_type=change_data.get("change_type"),
                    timestamp=change_data.get("timestamp"),
                    user_id=change_data.get("user_id", user_id),
                    confidence_score=change_data.get("confidence_score")
                )
                changes.append(change)
            
            # 创建完成的注释记录
            completed_annotation = self.create_completed_annotation(
                task_id=task_id,
                file_id=file_id,
                region_data=region,
                changes=changes,
                user_id=user_id,
                edit_time=region.get("edit_time")
            )
            completed_annotations.append(completed_annotation)
        
        # 计算批次统计
        batch_statistics = self._calculate_batch_statistics(completed_annotations)
        
        annotation_batch = AnnotationBatch(
            batch_id=batch_id,
            task_id=task_id,
            file_id=file_id,
            completed_annotations=completed_annotations,
            batch_statistics=batch_statistics,
            completion_time=datetime.now().isoformat(),
            total_regions=len(completed_regions),
            completed_regions=len(completed_annotations),
            user_id=user_id
        )
        
        return annotation_batch
    
    def _calculate_batch_statistics(
        self, 
        completed_annotations: List[CompletedAnnotation]
    ) -> Dict[str, Any]:
        """
        计算批次统计信息
        
        Args:
            completed_annotations: 完成的注释列表
            
        Returns:
            Dict: 统计信息
        """
        if not completed_annotations:
            return {
                "total_annotations": 0,
                "avg_quality_score": 0.0,
                "avg_confidence_improvement": 0.0,
                "total_changes": 0,
                "avg_edit_time": 0.0
            }
        
        total_annotations = len(completed_annotations)
        total_quality = sum(ann.quality_score or 0.0 for ann in completed_annotations)
        total_confidence_improvement = sum(
            (ann.final_confidence - ann.original_confidence) 
            for ann in completed_annotations
        )
        total_changes = sum(len(ann.changes) for ann in completed_annotations)
        total_edit_time = sum(ann.total_edit_time or 0.0 for ann in completed_annotations)
        
        return {
            "total_annotations": total_annotations,
            "avg_quality_score": total_quality / total_annotations,
            "avg_confidence_improvement": total_confidence_improvement / total_annotations,
            "total_changes": total_changes,
            "avg_changes_per_annotation": total_changes / total_annotations,
            "avg_edit_time": total_edit_time / total_annotations,
            "text_edit_count": sum(
                1 for ann in completed_annotations 
                for change in ann.changes 
                if change.change_type == "text_edit"
            ),
            "confidence_adjust_count": sum(
                1 for ann in completed_annotations 
                for change in ann.changes 
                if change.change_type == "confidence_adjust"
            ),
            "bbox_modify_count": sum(
                1 for ann in completed_annotations 
                for change in ann.changes 
                if change.change_type == "bbox_modify"
            )
        }
    
    def save_annotation_batch_to_database(
        self, 
        annotation_batch: AnnotationBatch
    ) -> bool:
        """
        保存注释批次到数据库
        
        Args:
            annotation_batch: 注释批次数据
            
        Returns:
            bool: 保存是否成功
        """
        try:
            self.logger.info(f"保存注释批次 {annotation_batch.batch_id} 到数据库")
            
            # 转换为字典格式
            batch_data = {
                "batch_id": annotation_batch.batch_id,
                "task_id": annotation_batch.task_id,
                "file_id": annotation_batch.file_id,
                "completion_time": annotation_batch.completion_time,
                "total_regions": annotation_batch.total_regions,
                "completed_regions": annotation_batch.completed_regions,
                "user_id": annotation_batch.user_id,
                "batch_statistics": annotation_batch.batch_statistics,
                "completed_annotations": []
            }
            
            # 转换注释数据
            for annotation in annotation_batch.completed_annotations:
                annotation_data = {
                    "task_id": annotation.task_id,
                    "file_id": annotation.file_id,
                    "region_id": annotation.region_id,
                    "original_text": annotation.original_text,
                    "annotated_text": annotation.annotated_text,
                    "original_confidence": annotation.original_confidence,
                    "final_confidence": annotation.final_confidence,
                    "annotation_type": annotation.annotation_type,
                    "status": annotation.status,
                    "completion_time": annotation.completion_time,
                    "total_edit_time": annotation.total_edit_time,
                    "user_id": annotation.user_id,
                    "quality_score": annotation.quality_score,
                    "changes": []
                }
                
                # 转换变更数据
                for change in annotation.changes:
                    change_data = {
                        "region_id": change.region_id,
                        "field_name": change.field_name,
                        "old_value": change.old_value,
                        "new_value": change.new_value,
                        "change_type": change.change_type,
                        "timestamp": change.timestamp,
                        "user_id": change.user_id,
                        "confidence_score": change.confidence_score
                    }
                    annotation_data["changes"].append(change_data)
                
                batch_data["completed_annotations"].append(annotation_data)
            
            # 保存到数据库
            success = self.db_manager.save_annotation_batch(batch_data)
            
            if success:
                self.logger.info(f"注释批次 {annotation_batch.batch_id} 保存成功")
                
                # 更新任务状态
                self._update_task_completion_status(annotation_batch.task_id, annotation_batch)
                
                return True
            else:
                self.logger.error(f"注释批次 {annotation_batch.batch_id} 保存失败")
                return False
                
        except Exception as e:
            self.logger.error(f"保存注释批次失败: {e}")
            return False
    
    def _update_task_completion_status(
        self, 
        task_id: str, 
        annotation_batch: AnnotationBatch
    ):
        """
        更新任务完成状态
        
        Args:
            task_id: 任务ID
            annotation_batch: 注释批次
        """
        try:
            # 获取任务数据
            task_data = self.db_manager.get_annotation_task(task_id)
            if not task_data:
                return
            
            # 更新任务状态
            update_data = {
                "status": "completed",
                "completed_at": datetime.now(),
                "completion_statistics": annotation_batch.batch_statistics,
                "completed_regions": annotation_batch.completed_regions,
                "total_regions": annotation_batch.total_regions
            }
            
            self.db_manager.update_annotation_task(task_id, update_data)
            self.logger.info(f"任务 {task_id} 状态已更新为完成")
            
        except Exception as e:
            self.logger.error(f"更新任务状态失败: {e}")
    
    def get_annotation_history(
        self, 
        file_id: Optional[str] = None,
        user_id: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        获取注释历史记录
        
        Args:
            file_id: 文件ID过滤
            user_id: 用户ID过滤
            start_date: 开始日期过滤
            end_date: 结束日期过滤
            
        Returns:
            List[Dict]: 注释历史记录
        """
        try:
            filters = {}
            
            if file_id:
                filters["file_id"] = file_id
            if user_id:
                filters["user_id"] = user_id
            if start_date:
                filters["completion_time"] = {"$gte": start_date}
            if end_date:
                if "completion_time" not in filters:
                    filters["completion_time"] = {}
                filters["completion_time"]["$lte"] = end_date
            
            history = self.db_manager.get_annotation_batches(filters)
            self.logger.info(f"获取到 {len(history)} 条注释历史记录")
            
            return history
            
        except Exception as e:
            self.logger.error(f"获取注释历史失败: {e}")
            return []
    
    def export_annotation_results(
        self, 
        task_id: str, 
        format_type: str = "json"
    ) -> Optional[Dict[str, Any]]:
        """
        导出注释结果
        
        Args:
            task_id: 任务ID
            format_type: 导出格式 ("json", "csv", "xml")
            
        Returns:
            Optional[Dict]: 导出的数据
        """
        try:
            # 获取任务的注释批次
            batches = self.db_manager.get_annotation_batches({"task_id": task_id})
            
            if not batches:
                return None
            
            if format_type == "json":
                return self._export_as_json(batches)
            elif format_type == "csv":
                return self._export_as_csv(batches)
            elif format_type == "xml":
                return self._export_as_xml(batches)
            else:
                return self._export_as_json(batches)
                
        except Exception as e:
            self.logger.error(f"导出注释结果失败: {e}")
            return None
    
    def _export_as_json(self, batches: List[Dict[str, Any]]) -> Dict[str, Any]:
        """导出为JSON格式"""
        return {
            "export_type": "json",
            "export_time": datetime.now().isoformat(),
            "total_batches": len(batches),
            "batches": batches
        }
    
    def _export_as_csv(self, batches: List[Dict[str, Any]]) -> Dict[str, Any]:
        """导出为CSV格式"""
        csv_data = []
        headers = [
            "batch_id", "task_id", "file_id", "region_id", 
            "original_text", "annotated_text", "original_confidence", 
            "final_confidence", "quality_score", "completion_time"
        ]
        
        for batch in batches:
            for annotation in batch.get("completed_annotations", []):
                row = [
                    batch.get("batch_id"),
                    annotation.get("task_id"),
                    annotation.get("file_id"),
                    annotation.get("region_id"),
                    annotation.get("original_text"),
                    annotation.get("annotated_text"),
                    annotation.get("original_confidence"),
                    annotation.get("final_confidence"),
                    annotation.get("quality_score"),
                    annotation.get("completion_time")
                ]
                csv_data.append(row)
        
        return {
            "export_type": "csv",
            "headers": headers,
            "data": csv_data
        }
    
    def _export_as_xml(self, batches: List[Dict[str, Any]]) -> Dict[str, Any]:
        """导出为XML格式"""
        # 简化的XML结构
        xml_structure = {
            "annotation_export": {
                "export_time": datetime.now().isoformat(),
                "total_batches": len(batches),
                "batches": batches
            }
        }
        
        return {
            "export_type": "xml",
            "structure": xml_structure
        }

def create_annotation_data_flow_manager(
    db_manager: MongoDBManager = None
) -> AnnotationDataFlowManager:
    """
    创建注释数据回流管理器实例
    
    Args:
        db_manager: MongoDB管理器
        
    Returns:
        AnnotationDataFlowManager: 管理器实例
    """
    return AnnotationDataFlowManager(db_manager)

# 示例使用
if __name__ == "__main__":
    # 创建管理器
    flow_manager = create_annotation_data_flow_manager()
    
    # 示例完成的区域数据
    sample_completed_regions = [
        {
            "region_id": "region_1",
            "original_text": "原始文本1",
            "text": "修正后文本1",
            "original_confidence": 0.75,
            "confidence": 0.95,
            "annotation_type": "text_correction",
            "status": "completed",
            "edit_time": 30.5,
            "changes": [
                {
                    "field_name": "text",
                    "old_value": "原始文本1",
                    "new_value": "修正后文本1",
                    "change_type": "text_edit",
                    "timestamp": datetime.now().isoformat()
                }
            ]
        }
    ]
    
    # 处理注释批次
    batch = flow_manager.process_annotation_batch(
        task_id="test_task_001",
        completed_regions=sample_completed_regions,
        user_id="test_user"
    )
    
    print("注释批次处理结果:")
    print(f"批次ID: {batch.batch_id}")
    print(f"完成注释数: {batch.completed_regions}")
    print(f"批次统计: {batch.batch_statistics}") 
#!/usr/bin/env python3
"""
数据导入导出系统
支持多种标注格式的导入导出，包括PaddleOCR、COCO、YOLO等格式
"""

import os
import json
import zipfile
import tempfile
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from pathlib import Path
import shutil

from pymongo import MongoClient
from paddlelabel_converter import PaddleLabelConverter

class DataImportExportManager:
    """数据导入导出管理器"""
    
    def __init__(self):
        # 连接MongoDB
        self.client = MongoClient("mongodb://localhost:27017/")
        self.db = self.client.agent_test
        
        # 初始化转换器
        self.converter = PaddleLabelConverter()
        
        # 支持的格式
        self.supported_formats = {
            "paddleocr": {
                "name": "PaddleOCR格式",
                "extensions": [".json"],
                "import_supported": True,
                "export_supported": True
            },
            "coco": {
                "name": "COCO格式",
                "extensions": [".json"],
                "import_supported": True,
                "export_supported": True
            },
            "yolo": {
                "name": "YOLO格式",
                "extensions": [".txt"],
                "import_supported": True,
                "export_supported": True
            },
            "labelme": {
                "name": "LabelMe格式",
                "extensions": [".json"],
                "import_supported": True,
                "export_supported": True
            },
            "pascal_voc": {
                "name": "Pascal VOC格式",
                "extensions": [".xml"],
                "import_supported": True,
                "export_supported": True
            }
        }
    
    def detect_format(self, file_path: str) -> Dict:
        """自动检测文件格式"""
        try:
            file_ext = Path(file_path).suffix.lower()
            
            # 基于文件扩展名初步判断
            possible_formats = []
            for format_name, format_info in self.supported_formats.items():
                if file_ext in format_info["extensions"]:
                    possible_formats.append(format_name)
            
            if not possible_formats:
                return {"success": False, "message": f"不支持的文件扩展名: {file_ext}"}
            
            # 对于JSON文件，尝试解析内容来确定具体格式
            if file_ext == ".json":
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # COCO格式检测
                    if isinstance(data, dict) and "images" in data and "annotations" in data:
                        return {"success": True, "format": "coco", "confidence": 0.9}
                    
                    # LabelMe格式检测
                    if isinstance(data, dict) and "shapes" in data and "imagePath" in data:
                        return {"success": True, "format": "labelme", "confidence": 0.9}
                    
                    # PaddleOCR格式检测
                    if isinstance(data, list) and len(data) > 0:
                        first_item = data[0]
                        if isinstance(first_item, dict) and "image_name" in first_item:
                            return {"success": True, "format": "paddleocr", "confidence": 0.8}
                    
                    # 默认返回第一个可能的格式
                    return {"success": True, "format": possible_formats[0], "confidence": 0.5}
                
                except json.JSONDecodeError:
                    return {"success": False, "message": "JSON文件格式错误"}
            
            # 对于其他格式，返回第一个匹配的
            return {"success": True, "format": possible_formats[0], "confidence": 0.7}
        
        except Exception as e:
            return {"success": False, "message": f"格式检测失败: {str(e)}"}
    
    def import_annotations(self, file_path: str, project_id: str, 
                          source_format: Optional[str] = None) -> Dict:
        """导入标注数据"""
        try:
            from bson import ObjectId
            
            # 验证项目存在
            project = self.db.annotation_projects.find_one({"_id": ObjectId(project_id)})
            if not project:
                return {"success": False, "message": "项目不存在"}
            
            # 自动检测格式（如果未指定）
            if not source_format:
                format_result = self.detect_format(file_path)
                if not format_result["success"]:
                    return format_result
                source_format = format_result["format"]
            
            # 验证格式支持
            if source_format not in self.supported_formats:
                return {"success": False, "message": f"不支持的格式: {source_format}"}
            
            if not self.supported_formats[source_format]["import_supported"]:
                return {"success": False, "message": f"格式 {source_format} 不支持导入"}
            
            # 读取和解析文件
            import_data = self._read_annotation_file(file_path, source_format)
            if not import_data["success"]:
                return import_data
            
            annotations_data = import_data["data"]
            
            # 转换为PaddleOCR格式（内部标准格式）
            if source_format != "paddleocr":
                convert_result = self.converter.convert_format(
                    annotations_data, source_format, "paddleocr"
                )
                if not convert_result["success"]:
                    return convert_result
                annotations_data = convert_result["data"]
            
            # 导入到数据库
            import_result = self._import_to_database(annotations_data, project_id)
            
            return import_result
        
        except Exception as e:
            return {"success": False, "message": f"导入失败: {str(e)}"}
    
    def _read_annotation_file(self, file_path: str, format_type: str) -> Dict:
        """读取标注文件"""
        try:
            if format_type in ["paddleocr", "coco", "labelme"]:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return {"success": True, "data": data}
            
            elif format_type == "yolo":
                # YOLO格式需要配合图像文件夹
                return {"success": False, "message": "YOLO格式导入需要额外的图像文件夹信息"}
            
            elif format_type == "pascal_voc":
                # Pascal VOC格式需要解析XML
                return {"success": False, "message": "Pascal VOC格式导入功能开发中"}
            
            else:
                return {"success": False, "message": f"不支持的格式: {format_type}"}
        
        except Exception as e:
            return {"success": False, "message": f"读取文件失败: {str(e)}"}
    
    def _import_to_database(self, annotations_data: List[Dict], project_id: str) -> Dict:
        """将标注数据导入到数据库"""
        try:
            imported_count = 0
            failed_count = 0
            results = []
            
            for annotation_item in annotations_data:
                try:
                    # 检查图像是否已存在
                    existing_image = self.db.annotation_images.find_one({
                        "project_id": project_id,
                        "image_name": annotation_item.get("image_name", "")
                    })
                    
                    if existing_image:
                        results.append({
                            "image_name": annotation_item.get("image_name", ""),
                            "status": "skipped",
                            "reason": "图像已存在"
                        })
                        continue
                    
                    # 创建图像记录
                    image_record = {
                        "project_id": project_id,
                        "image_name": annotation_item.get("image_name", ""),
                        "image_path": annotation_item.get("image_path", ""),
                        "image_width": annotation_item.get("width", 0),
                        "image_height": annotation_item.get("height", 0),
                        "status": "imported",
                        "created_at": datetime.now(),
                        "annotations": annotation_item.get("annotations", []),
                        "annotation_count": len(annotation_item.get("annotations", [])),
                        "has_preannotation": False,
                        "import_source": "file_import"
                    }
                    
                    # 插入数据库
                    result = self.db.annotation_images.insert_one(image_record)
                    
                    imported_count += 1
                    results.append({
                        "image_name": annotation_item.get("image_name", ""),
                        "status": "imported",
                        "image_id": str(result.inserted_id),
                        "annotation_count": len(annotation_item.get("annotations", []))
                    })
                
                except Exception as e:
                    failed_count += 1
                    results.append({
                        "image_name": annotation_item.get("image_name", ""),
                        "status": "failed",
                        "error": str(e)
                    })
            
            # 更新项目统计
            if imported_count > 0:
                from bson import ObjectId
                self.db.annotation_projects.update_one(
                    {"_id": ObjectId(project_id)},
                    {"$inc": {"statistics.total_images": imported_count}}
                )
            
            return {
                "success": True,
                "message": f"导入完成，成功: {imported_count}，失败: {failed_count}",
                "imported_count": imported_count,
                "failed_count": failed_count,
                "results": results
            }
        
        except Exception as e:
            return {"success": False, "message": f"数据库导入失败: {str(e)}"}
    
    def export_annotations(self, project_id: str, export_format: str = "paddleocr",
                          include_images: bool = False) -> Dict:
        """导出标注数据"""
        try:
            from bson import ObjectId
            
            # 验证项目存在
            project = self.db.annotation_projects.find_one({"_id": ObjectId(project_id)})
            if not project:
                return {"success": False, "message": "项目不存在"}
            
            # 验证导出格式
            if export_format not in self.supported_formats:
                return {"success": False, "message": f"不支持的导出格式: {export_format}"}
            
            if not self.supported_formats[export_format]["export_supported"]:
                return {"success": False, "message": f"格式 {export_format} 不支持导出"}
            
            # 获取项目中的所有图像
            images = list(self.db.annotation_images.find({"project_id": project_id}))
            
            if not images:
                return {"success": False, "message": "项目中没有标注数据"}
            
            # 转换为PaddleOCR格式
            export_data = []
            for image in images:
                image_data = {
                    "image_name": image["image_name"],
                    "image_path": image["image_path"],
                    "width": image["image_width"],
                    "height": image["image_height"],
                    "annotations": image["annotations"]
                }
                export_data.append(image_data)
            
            # 转换为目标格式
            if export_format != "paddleocr":
                convert_result = self.converter.convert_format(
                    export_data, "paddleocr", export_format
                )
                if not convert_result["success"]:
                    return convert_result
                export_data = convert_result["data"]
            
            # 创建导出包
            export_result = self._create_export_package(
                export_data, project, export_format, include_images
            )
            
            return export_result
        
        except Exception as e:
            return {"success": False, "message": f"导出失败: {str(e)}"}
    
    def _create_export_package(self, export_data: Any, project: Dict, 
                              export_format: str, include_images: bool) -> Dict:
        """创建导出包"""
        try:
            # 创建临时目录
            temp_dir = tempfile.mkdtemp()
            
            try:
                # 项目信息
                project_name = project.get("name", "project").replace(" ", "_")
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                
                # 创建导出文件
                if export_format == "paddleocr":
                    export_file = os.path.join(temp_dir, f"{project_name}_annotations.json")
                    with open(export_file, 'w', encoding='utf-8') as f:
                        json.dump(export_data, f, ensure_ascii=False, indent=2)
                
                elif export_format == "coco":
                    export_file = os.path.join(temp_dir, f"{project_name}_coco.json")
                    with open(export_file, 'w', encoding='utf-8') as f:
                        json.dump(export_data, f, ensure_ascii=False, indent=2)
                
                elif export_format == "yolo":
                    # YOLO格式需要创建多个文件
                    yolo_dir = os.path.join(temp_dir, "yolo_annotations")
                    os.makedirs(yolo_dir, exist_ok=True)
                    
                    for item in export_data:
                        txt_file = os.path.join(yolo_dir, f"{Path(item['image_name']).stem}.txt")
                        with open(txt_file, 'w') as f:
                            f.write(item.get("annotations_text", ""))
                    
                    export_file = yolo_dir
                
                else:
                    return {"success": False, "message": f"导出格式 {export_format} 暂不支持"}
                
                # 如果包含图像，复制图像文件
                if include_images:
                    images_dir = os.path.join(temp_dir, "images")
                    os.makedirs(images_dir, exist_ok=True)
                    
                    copied_images = 0
                    for item in export_data if isinstance(export_data, list) else []:
                        image_path = item.get("image_path", "")
                        if image_path and os.path.exists(image_path):
                            dest_path = os.path.join(images_dir, item.get("image_name", ""))
                            shutil.copy2(image_path, dest_path)
                            copied_images += 1
                
                # 创建ZIP包
                zip_file = os.path.join(temp_dir, f"{project_name}_{export_format}_{timestamp}.zip")
                with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    if os.path.isfile(export_file):
                        zipf.write(export_file, os.path.basename(export_file))
                    elif os.path.isdir(export_file):
                        for root, dirs, files in os.walk(export_file):
                            for file in files:
                                file_path = os.path.join(root, file)
                                arcname = os.path.relpath(file_path, temp_dir)
                                zipf.write(file_path, arcname)
                    
                    # 添加图像文件
                    if include_images and os.path.exists(os.path.join(temp_dir, "images")):
                        images_dir = os.path.join(temp_dir, "images")
                        for root, dirs, files in os.walk(images_dir):
                            for file in files:
                                file_path = os.path.join(root, file)
                                arcname = os.path.relpath(file_path, temp_dir)
                                zipf.write(file_path, arcname)
                
                # 读取ZIP文件内容
                with open(zip_file, 'rb') as f:
                    zip_content = f.read()
                
                return {
                    "success": True,
                    "data": {
                        "zip_content": zip_content,
                        "filename": os.path.basename(zip_file),
                        "size": len(zip_content),
                        "format": export_format,
                        "include_images": include_images,
                        "annotation_count": len(export_data) if isinstance(export_data, list) else 1
                    }
                }
            
            finally:
                # 清理临时文件
                shutil.rmtree(temp_dir, ignore_errors=True)
        
        except Exception as e:
            return {"success": False, "message": f"创建导出包失败: {str(e)}"}
    
    def batch_import_from_zip(self, zip_file_path: str, project_id: str) -> Dict:
        """从ZIP文件批量导入"""
        try:
            temp_dir = tempfile.mkdtemp()
            
            try:
                # 解压ZIP文件
                with zipfile.ZipFile(zip_file_path, 'r') as zipf:
                    zipf.extractall(temp_dir)
                
                # 查找标注文件
                annotation_files = []
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        file_ext = Path(file).suffix.lower()
                        
                        if file_ext in ['.json', '.xml', '.txt']:
                            annotation_files.append(file_path)
                
                if not annotation_files:
                    return {"success": False, "message": "ZIP文件中未找到标注文件"}
                
                # 批量导入
                total_imported = 0
                total_failed = 0
                import_results = []
                
                for annotation_file in annotation_files:
                    result = self.import_annotations(annotation_file, project_id)
                    
                    if result["success"]:
                        total_imported += result.get("imported_count", 0)
                        total_failed += result.get("failed_count", 0)
                    else:
                        total_failed += 1
                    
                    import_results.append({
                        "file": os.path.basename(annotation_file),
                        "result": result
                    })
                
                return {
                    "success": True,
                    "message": f"批量导入完成，成功: {total_imported}，失败: {total_failed}",
                    "total_imported": total_imported,
                    "total_failed": total_failed,
                    "file_results": import_results
                }
            
            finally:
                # 清理临时文件
                shutil.rmtree(temp_dir, ignore_errors=True)
        
        except Exception as e:
            return {"success": False, "message": f"批量导入失败: {str(e)}"}
    
    def get_import_export_statistics(self, project_id: str) -> Dict:
        """获取导入导出统计信息"""
        try:
            from bson import ObjectId
            
            # 验证项目存在
            project = self.db.annotation_projects.find_one({"_id": ObjectId(project_id)})
            if not project:
                return {"success": False, "message": "项目不存在"}
            
            # 统计导入的图像
            total_images = self.db.annotation_images.count_documents({"project_id": project_id})
            imported_images = self.db.annotation_images.count_documents({
                "project_id": project_id,
                "import_source": "file_import"
            })
            preannotated_images = self.db.annotation_images.count_documents({
                "project_id": project_id,
                "has_preannotation": True
            })
            
            # 统计标注数量
            pipeline = [
                {"$match": {"project_id": project_id}},
                {"$group": {
                    "_id": None,
                    "total_annotations": {"$sum": "$annotation_count"},
                    "avg_annotations_per_image": {"$avg": "$annotation_count"}
                }}
            ]
            
            annotation_stats = list(self.db.annotation_images.aggregate(pipeline))
            total_annotations = annotation_stats[0]["total_annotations"] if annotation_stats else 0
            avg_annotations = annotation_stats[0]["avg_annotations_per_image"] if annotation_stats else 0
            
            return {
                "success": True,
                "data": {
                    "project_id": project_id,
                    "project_name": project.get("name", ""),
                    "total_images": total_images,
                    "imported_images": imported_images,
                    "preannotated_images": preannotated_images,
                    "manual_images": total_images - imported_images - preannotated_images,
                    "total_annotations": total_annotations,
                    "avg_annotations_per_image": round(avg_annotations, 2) if avg_annotations else 0,
                    "supported_formats": self.supported_formats
                }
            }
        
        except Exception as e:
            return {"success": False, "message": f"获取统计信息失败: {str(e)}"}

# 全局实例
_import_export_manager = None

def get_import_export_manager() -> DataImportExportManager:
    """获取数据导入导出管理器实例"""
    global _import_export_manager
    if _import_export_manager is None:
        _import_export_manager = DataImportExportManager()
    return _import_export_manager 
"""
数据导出服务
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import json
import os
import numpy as np
from bson import ObjectId

from src.backend.database.database import get_mongodb
from src.backend.minio_client import MinioClient
from src.backend.logger import get_logger

logger = get_logger(__name__)

class DataExportService:
    """数据导出服务类"""
    
    def __init__(self):
        """初始化数据导出服务"""
        self.db = None  # 将在异步方法中初始化
        self.minio = MinioClient()
        
        # 支持的标注类型
        self.annotation_types = {
            "text": "文本",
            "image": "图片",
            "chart": "图表",
            "table": "表格"
        }
        
    def standardize_coordinates(
        self,
        points: List[Dict[str, float]],
        image_width: int,
        image_height: int
    ) -> List[float]:
        """
        标准化坐标点
        
        Args:
            points: 坐标点列表，每个点包含x和y坐标
            image_width: 图像宽度
            image_height: 图像高度
            
        Returns:
            List[float]: 标准化后的坐标点列表 [x1,y1,x2,y2,x3,y3,x4,y4]
        """
        # 确保有4个点
        if len(points) != 4:
            raise ValueError("坐标点必须是4个点")
            
        # 标准化坐标（转换为相对坐标）
        normalized_points = []
        for point in points:
            x = point["x"] / image_width
            y = point["y"] / image_height
            normalized_points.extend([x, y])
            
        return normalized_points
    
    def format_label_line(
        self,
        image_path: str,
        boxes: List[Dict[str, Any]],
        image_width: int,
        image_height: int
    ) -> str:
        """
        格式化标签行
        
        Args:
            image_path: 图像路径
            boxes: 标注框列表
            image_width: 图像宽度
            image_height: 图像高度
            
        Returns:
            str: 格式化的标签行
        """
        # 格式：image_path\ttab\tx1,y1,x2,y2,x3,y3,x4,y4\tcategory\tconfidence
        formatted_boxes = []
        
        for box in boxes:
            # 标准化坐标
            coords = self.standardize_coordinates(
                box["points"],
                image_width,
                image_height
            )
            
            # 获取类别和置信度
            category = box.get("category", "text")  # 默认为文本
            confidence = box.get("confidence", 1.0)  # 默认置信度为1.0
            
            # 格式化坐标
            coords_str = ",".join(f"{c:.4f}" for c in coords)
            
            # 组合框信息
            formatted_boxes.append(f"\t{coords_str}\t{category}\t{confidence:.4f}")
            
        # 组合所有框信息
        boxes_str = "".join(formatted_boxes)
        
        return f"{image_path}{boxes_str}"
    
    async def validate_annotation_format(
        self,
        annotation: Dict[str, Any]
    ) -> Tuple[bool, Optional[str]]:
        """
        验证标注格式
        
        Args:
            annotation: 标注数据
            
        Returns:
            Tuple[bool, Optional[str]]: (是否有效, 错误信息)
        """
        try:
            # 验证必需字段
            required_fields = ["image_id", "boxes"]
            for field in required_fields:
                if field not in annotation:
                    return False, f"缺少必需字段: {field}"
            
            # 验证boxes格式
            boxes = annotation["boxes"]
            if not isinstance(boxes, list):
                return False, "boxes必须是列表"
                
            for box in boxes:
                # 验证points
                if "points" not in box:
                    return False, "box缺少points字段"
                    
                points = box["points"]
                if not isinstance(points, list) or len(points) != 4:
                    return False, "points必须是包含4个点的列表"
                    
                for point in points:
                    if not isinstance(point, dict) or "x" not in point or "y" not in point:
                        return False, "每个点必须包含x和y坐标"
                
                # 验证category
                if "category" in box and box["category"] not in self.annotation_types:
                    return False, f"无效的标注类型: {box['category']}"
                
                # 验证confidence
                if "confidence" in box:
                    confidence = box["confidence"]
                    if not isinstance(confidence, (int, float)) or not 0 <= confidence <= 1:
                        return False, "confidence必须是0到1之间的数值"
            
            return True, None
            
        except Exception as e:
            return False, str(e)
    
    async def export_annotations(
        self,
        project_id: str,
        output_dir: str,
        batch_size: int = 100
    ) -> Tuple[str, List[str]]:
        """
        导出项目标注数据
        
        Args:
            project_id: 项目ID
            output_dir: 输出目录
            batch_size: 批处理大小
            
        Returns:
            Tuple[str, List[str]]: (标签文件路径, 警告信息列表)
        """
        try:
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 获取项目信息
            project = await self.db["projects"].find_one(
                {"_id": ObjectId(project_id)}
            )
            if not project:
                raise ValueError(f"找不到项目: {project_id}")
            
            # 获取项目下的所有标注
            annotations = self.db["annotations"].find({
                "project_id": ObjectId(project_id)
            })
            
            # 准备标签文件
            label_file = os.path.join(output_dir, "label.txt")
            warnings = []
            
            async with open(label_file, "w", encoding="utf-8") as f:
                # 分批处理标注
                batch = []
                async for annotation in annotations:
                    batch.append(annotation)
                    
                    if len(batch) >= batch_size:
                        await self._process_annotation_batch(
                            batch,
                            f,
                            output_dir,
                            warnings
                        )
                        batch = []
                
                # 处理剩余的标注
                if batch:
                    await self._process_annotation_batch(
                        batch,
                        f,
                        output_dir,
                        warnings
                    )
            
            return label_file, warnings
            
        except Exception as e:
            raise Exception(f"导出标注数据失败: {str(e)}")
    
    async def _process_annotation_batch(
        self,
        batch: List[Dict[str, Any]],
        label_file,
        output_dir: str,
        warnings: List[str]
    ) -> None:
        """
        处理一批标注数据
        
        Args:
            batch: 标注数据批次
            label_file: 标签文件对象
            output_dir: 输出目录
            warnings: 警告信息列表
        """
        for annotation in batch:
            try:
                # 验证标注格式
                is_valid, error = await self.validate_annotation_format(annotation)
                if not is_valid:
                    warnings.append(f"标注 {annotation['_id']} 格式无效: {error}")
                    continue
                
                # 获取图像信息
                image_info = await self.db["images"].find_one(
                    {"_id": ObjectId(annotation["image_id"])}
                )
                
                # 从MinIO下载图像
                image_data = await self.minio.get_file(
                    image_info["bucket"],
                    image_info["object_name"]
                )
                
                # 保存图像到本地
                image_filename = f"{image_info['_id']}.{image_info['format']}"
                image_path = os.path.join(output_dir, image_filename)
                with open(image_path, "wb") as f:
                    f.write(image_data)
                
                # 格式化标签行
                label_line = self.format_label_line(
                    image_filename,
                    annotation["boxes"],
                    image_info["width"],
                    image_info["height"]
                )
                
                # 写入标签文件
                label_file.write(f"{label_line}\n")
                
            except Exception as e:
                warnings.append(f"处理标注 {annotation['_id']} 失败: {str(e)}")
    
    async def export_dataset_version(
        self,
        project_id: str,
        version: str,
        output_dir: str,
        batch_size: int = 100
    ) -> Tuple[str, List[str]]:
        """
        导出特定版本的数据集
        
        Args:
            project_id: 项目ID
            version: 数据集版本
            output_dir: 输出目录
            batch_size: 批处理大小
            
        Returns:
            Tuple[str, List[str]]: (标签文件路径, 警告信息列表)
        """
        try:
            # 获取版本信息
            version_info = await self.db["dataset_versions"].find_one({
                "project_id": ObjectId(project_id),
                "version": version
            })
            if not version_info:
                raise ValueError(f"找不到数据集版本: {version}")
            
            # 创建版本目录
            version_dir = os.path.join(output_dir, f"version_{version}")
            os.makedirs(version_dir, exist_ok=True)
            
            # 导出标注数据
            label_file, warnings = await self.export_annotations(
                project_id,
                version_dir,
                batch_size
            )
            
            # 保存版本信息
            version_info_file = os.path.join(version_dir, "version_info.json")
            with open(version_info_file, "w", encoding="utf-8") as f:
                json.dump({
                    "version": version,
                    "created_at": version_info["created_at"].isoformat(),
                    "description": version_info.get("description", ""),
                    "metadata": version_info.get("metadata", {})
                }, f, indent=2)
            
            return label_file, warnings
            
        except Exception as e:
            raise Exception(f"导出数据集版本失败: {str(e)}")
    
    async def export_multi_level_categories(
        self,
        project_id: str,
        output_dir: str
    ) -> str:
        """
        导出多级分类标签
        
        Args:
            project_id: 项目ID
            output_dir: 输出目录
            
        Returns:
            str: 分类标签文件路径
        """
        try:
            # 获取项目的分类标签
            categories = await self.db["categories"].find({
                "project_id": ObjectId(project_id)
            }).to_list(None)
            
            # 创建分类标签文件
            categories_file = os.path.join(output_dir, "categories.txt")
            with open(categories_file, "w", encoding="utf-8") as f:
                for category in categories:
                    # 格式：main_category\tsubcategory
                    main_category = category["main_category"]
                    subcategories = category.get("subcategories", [])
                    
                    if subcategories:
                        for sub in subcategories:
                            f.write(f"{main_category}\t{sub}\n")
                    else:
                        f.write(f"{main_category}\n")
            
            return categories_file
            
        except Exception as e:
            raise Exception(f"导出分类标签失败: {str(e)}") 
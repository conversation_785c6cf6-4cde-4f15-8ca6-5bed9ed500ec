"""
反馈收集服务
用于收集和管理用户反馈，支持OCR系统的持续学习
"""

from typing import Dict, List, Optional
from datetime import datetime
import json
from pathlib import Path

from src.backend.logger import get_logger

class FeedbackCollectionService:
    """反馈收集服务"""
    
    def __init__(self, storage_path: str = "data/feedback"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        self.logger = get_logger(__name__)
        
    def save_feedback(self, feedback_data: Dict) -> str:
        """保存反馈数据
        
        Args:
            feedback_data: 反馈数据字典
            
        Returns:
            反馈ID
        """
        # 生成反馈ID
        feedback_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 添加时间戳
        feedback_data["timestamp"] = datetime.now().isoformat()
        
        # 保存到文件
        feedback_file = self.storage_path / f"feedback_{feedback_id}.json"
        with feedback_file.open("w", encoding="utf-8") as f:
            json.dump(feedback_data, f, ensure_ascii=False, indent=2)
            
        self.logger.info(f"保存反馈数据：{feedback_id}")
        return feedback_id
    
    def get_feedback(self, feedback_id: str) -> Optional[Dict]:
        """获取反馈数据
        
        Args:
            feedback_id: 反馈ID
            
        Returns:
            反馈数据字典，如果不存在则返回None
        """
        feedback_file = self.storage_path / f"feedback_{feedback_id}.json"
        if not feedback_file.exists():
            self.logger.warning(f"反馈数据不存在：{feedback_id}")
            return None
            
        with feedback_file.open("r", encoding="utf-8") as f:
            return json.load(f)
    
    def get_all_feedback(self, 
                        start_date: Optional[datetime] = None,
                        end_date: Optional[datetime] = None) -> List[Dict]:
        """获取所有反馈数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            反馈数据列表
        """
        feedback_list = []
        
        for feedback_file in self.storage_path.glob("feedback_*.json"):
            with feedback_file.open("r", encoding="utf-8") as f:
                feedback_data = json.load(f)
                
            # 解析时间戳
            timestamp = datetime.fromisoformat(feedback_data["timestamp"])
            
            # 检查日期范围
            if start_date and timestamp < start_date:
                continue
            if end_date and timestamp > end_date:
                continue
                
            feedback_list.append(feedback_data)
            
        self.logger.info(f"获取反馈数据：{len(feedback_list)}条")
        return feedback_list
    
    def delete_feedback(self, feedback_id: str) -> bool:
        """删除反馈数据
        
        Args:
            feedback_id: 反馈ID
            
        Returns:
            是否删除成功
        """
        feedback_file = self.storage_path / f"feedback_{feedback_id}.json"
        if not feedback_file.exists():
            self.logger.warning(f"反馈数据不存在：{feedback_id}")
            return False
            
        feedback_file.unlink()
        self.logger.info(f"删除反馈数据：{feedback_id}")
        return True 
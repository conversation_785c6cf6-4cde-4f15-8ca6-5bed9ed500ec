"""
错误分析服务
用于分析OCR识别错误，提供详细的错误分析和改进建议
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from collections import defaultdict
import difflib
import re
from bson import ObjectId
from pymongo import MongoClient, ASCENDING, DESCENDING
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.backend.logger import get_logger
from src.backend.config import settings

logger = get_logger(__name__)

class ErrorAnalysisService:
    """错误分析服务类"""
    
    def __init__(self):
        """初始化错误分析服务"""
        self.client = MongoClient(settings.MONGODB_URL)
        self.db = self.client[settings.MONGODB_DATABASE]
        self.collection = self.db["error_analysis"]
        
        # 错误类型定义
        self.error_types = {
            "substitution": "字符替换",
            "insertion": "多余字符",
            "deletion": "缺失字符",
            "transposition": "字符顺序错误",
            "spacing": "空格错误",
            "punctuation": "标点符号错误",
            "case": "大小写错误",
            "noise": "噪声字符",
            "segmentation": "分割错误"
        }
    
    def analyze_errors(self, 
                      region_type: Optional[str] = None,
                      start_date: Optional[datetime] = None,
                      end_date: Optional[datetime] = None,
                      min_confidence: Optional[float] = None) -> Dict:
        """
        分析OCR错误并生成详细报告
        
        Args:
            region_type: 要分析的区域类型（可选）
            start_date: 开始日期（可选）
            end_date: 结束日期（可选）
            min_confidence: 最小置信度（可选）
            
        Returns:
            错误分析报告
        """
        # 构建查询条件
        query = {}
        if region_type:
            query["region_type"] = region_type
        if start_date:
            query["created_at"] = {"$gte": start_date}
        if end_date:
            query["created_at"] = query.get("created_at", {})
            query["created_at"]["$lte"] = end_date
        if min_confidence:
            query["confidence"] = {"$gte": min_confidence}
        
        # 获取训练样本
        samples = list(self.db.training_samples.find(query))
        
        if not samples:
            return {
                "message": "没有找到符合条件的样本",
                "sample_count": 0
            }
        
        # 分析错误
        error_stats = self._analyze_error_statistics(samples)
        error_patterns = self._analyze_error_patterns(samples)
        region_analysis = self._analyze_region_performance(samples)
        confidence_analysis = self._analyze_confidence_distribution(samples)
        temporal_analysis = self._analyze_temporal_trends(samples)
        
        # 生成改进建议
        improvement_suggestions = self._generate_improvement_suggestions(
            error_stats,
            error_patterns,
            region_analysis,
            confidence_analysis
        )
        
        return {
            "sample_count": len(samples),
            "period": {
                "start": start_date,
                "end": end_date
            },
            "error_statistics": error_stats,
            "error_patterns": error_patterns,
            "region_analysis": region_analysis,
            "confidence_analysis": confidence_analysis,
            "temporal_analysis": temporal_analysis,
            "improvement_suggestions": improvement_suggestions
        }
    
    def _analyze_error_statistics(self, samples: List[Dict]) -> Dict:
        """分析错误统计信息"""
        error_counts = defaultdict(int)
        total_chars = 0
        total_errors = 0
        
        for sample in samples:
            predicted = sample["ocr_predicted_text"]
            truth = sample["ground_truth_text"]
            
            # 计算字符级错误
            error_details = self._analyze_character_errors(predicted, truth)
            
            # 更新统计
            for error_type, count in error_details["error_counts"].items():
                error_counts[error_type] += count
            
            total_chars += len(truth)
            total_errors += error_details["total_errors"]
        
        # 计算错误率
        error_rate = total_errors / total_chars if total_chars > 0 else 0
        
        # 按错误类型统计
        error_types = {
            error_type: {
                "count": count,
                "percentage": (count / total_errors * 100) if total_errors > 0 else 0
            }
            for error_type, count in error_counts.items()
        }
        
        return {
            "total_samples": len(samples),
            "total_characters": total_chars,
            "total_errors": total_errors,
            "error_rate": error_rate,
            "error_types": error_types
        }
    
    def _analyze_character_errors(self, predicted: str, truth: str) -> Dict:
        """分析字符级错误"""
        matcher = difflib.SequenceMatcher(None, predicted, truth)
        error_counts = defaultdict(int)
        total_errors = 0
        
        for tag, i1, i2, j1, j2 in matcher.get_opcodes():
            if tag == 'replace':
                # 分析替换错误
                pred_chunk = predicted[i1:i2]
                truth_chunk = truth[j1:j2]
                
                if len(pred_chunk) == len(truth_chunk):
                    # 字符替换
                    error_counts["substitution"] += len(pred_chunk)
                    total_errors += len(pred_chunk)
                else:
                    # 复杂替换
                    error_counts["complex"] += 1
                    total_errors += max(len(pred_chunk), len(truth_chunk))
                    
            elif tag == 'delete':
                # 多余字符
                error_counts["insertion"] += i2 - i1
                total_errors += i2 - i1
                
            elif tag == 'insert':
                # 缺失字符
                error_counts["deletion"] += j2 - j1
                total_errors += j2 - j1
        
        # 分析空格错误
        pred_spaces = len(re.findall(r'\s+', predicted))
        truth_spaces = len(re.findall(r'\s+', truth))
        if pred_spaces != truth_spaces:
            error_counts["spacing"] += abs(pred_spaces - truth_spaces)
            total_errors += abs(pred_spaces - truth_spaces)
        
        # 分析标点符号错误
        pred_puncts = len(re.findall(r'[^\w\s]', predicted))
        truth_puncts = len(re.findall(r'[^\w\s]', truth))
        if pred_puncts != truth_puncts:
            error_counts["punctuation"] += abs(pred_puncts - truth_puncts)
            total_errors += abs(pred_puncts - truth_puncts)
        
        return {
            "error_counts": dict(error_counts),
            "total_errors": total_errors
        }
    
    def _analyze_error_patterns(self, samples: List[Dict]) -> List[Dict]:
        """分析错误模式"""
        error_patterns = defaultdict(lambda: {"count": 0, "examples": []})
        
        for sample in samples:
            predicted = sample["ocr_predicted_text"]
            truth = sample["ground_truth_text"]
            
            # 使用滑动窗口查找重复的错误模式
            window_size = 3  # 考虑3个字符的上下文
            
            for i in range(len(predicted) - window_size + 1):
                pred_window = predicted[i:i+window_size]
                truth_window = truth[i:i+window_size] if i < len(truth) else ""
                
                if pred_window != truth_window:
                    pattern = {
                        "context": truth[max(0, i-2):i],  # 前文
                        "error": (pred_window, truth_window),  # 错误部分
                        "following": truth[i+window_size:i+window_size+2]  # 后文
                    }
                    
                    pattern_key = f"{pattern['context']}|{pattern['error']}|{pattern['following']}"
                    error_patterns[pattern_key]["count"] += 1
                    
                    if len(error_patterns[pattern_key]["examples"]) < 3:
                        error_patterns[pattern_key]["examples"].append({
                            "predicted": predicted,
                            "truth": truth,
                            "position": i
                        })
        
        # 转换为列表并排序
        patterns_list = [
            {
                "pattern": key,
                "count": value["count"],
                "examples": value["examples"]
            }
            for key, value in error_patterns.items()
        ]
        
        return sorted(patterns_list, key=lambda x: x["count"], reverse=True)[:10]
    
    def _analyze_region_performance(self, samples: List[Dict]) -> Dict:
        """分析不同区域类型的性能"""
        region_stats = defaultdict(lambda: {
            "total_samples": 0,
            "total_errors": 0,
            "avg_confidence": 0.0,
            "error_types": defaultdict(int)
        })
        
        for sample in samples:
            region_type = sample["region_type"]
            stats = region_stats[region_type]
            
            # 更新基本统计
            stats["total_samples"] += 1
            stats["avg_confidence"] += sample["confidence"]
            
            # 分析错误
            error_details = self._analyze_character_errors(
                sample["ocr_predicted_text"],
                sample["ground_truth_text"]
            )
            
            stats["total_errors"] += error_details["total_errors"]
            for error_type, count in error_details["error_counts"].items():
                stats["error_types"][error_type] += count
        
        # 计算平均值
        for stats in region_stats.values():
            if stats["total_samples"] > 0:
                stats["avg_confidence"] /= stats["total_samples"]
        
        return dict(region_stats)
    
    def _analyze_confidence_distribution(self, samples: List[Dict]) -> Dict:
        """分析置信度分布"""
        confidence_ranges = {
            "very_low": (0.0, 0.2),
            "low": (0.2, 0.4),
            "medium": (0.4, 0.6),
            "high": (0.6, 0.8),
            "very_high": (0.8, 1.0)
        }
        
        distribution = {
            range_name: {
                "count": 0,
                "error_rate": 0.0,
                "samples": []
            }
            for range_name in confidence_ranges
        }
        
        for sample in samples:
            confidence = sample["confidence"]
            
            # 找到对应的置信度范围
            for range_name, (min_conf, max_conf) in confidence_ranges.items():
                if min_conf <= confidence < max_conf:
                    dist = distribution[range_name]
                    dist["count"] += 1
                    
                    # 计算错误率
                    error_details = self._analyze_character_errors(
                        sample["ocr_predicted_text"],
                        sample["ground_truth_text"]
                    )
                    
                    dist["error_rate"] += error_details["total_errors"] / len(sample["ground_truth_text"])
                    
                    # 保存样本示例（最多3个）
                    if len(dist["samples"]) < 3:
                        dist["samples"].append({
                            "text": sample["ocr_predicted_text"][:100],  # 只保存前100个字符
                            "confidence": confidence,
                            "error_count": error_details["total_errors"]
                        })
                    break
        
        # 计算平均错误率
        for dist in distribution.values():
            if dist["count"] > 0:
                dist["error_rate"] /= dist["count"]
        
        return distribution
    
    def _analyze_temporal_trends(self, samples: List[Dict]) -> Dict:
        """分析时间趋势"""
        if not samples:
            return {}
        
        # 按时间排序
        samples.sort(key=lambda x: x["created_at"])
        
        # 确定时间范围
        start_date = samples[0]["created_at"]
        end_date = samples[-1]["created_at"]
        duration = end_date - start_date
        
        # 根据总时间跨度选择合适的时间间隔
        if duration.days > 30:
            interval = timedelta(days=7)  # 按周
            format_str = "%Y-%m-%d"
        elif duration.days > 7:
            interval = timedelta(days=1)  # 按天
            format_str = "%Y-%m-%d"
        else:
            interval = timedelta(hours=4)  # 按4小时
            format_str = "%Y-%m-%d %H:00"
        
        trends = defaultdict(lambda: {
            "error_rate": 0.0,
            "confidence": 0.0,
            "sample_count": 0
        })
        
        current_date = start_date
        while current_date <= end_date:
            period_end = current_date + interval
            
            # 获取该时间段的样本
            period_samples = [
                s for s in samples
                if current_date <= s["created_at"] < period_end
            ]
            
            if period_samples:
                stats = trends[current_date.strftime(format_str)]
                stats["sample_count"] = len(period_samples)
                
                # 计算平均置信度
                stats["confidence"] = sum(s["confidence"] for s in period_samples) / len(period_samples)
                
                # 计算错误率
                total_errors = 0
                total_chars = 0
                for sample in period_samples:
                    error_details = self._analyze_character_errors(
                        sample["ocr_predicted_text"],
                        sample["ground_truth_text"]
                    )
                    total_errors += error_details["total_errors"]
                    total_chars += len(sample["ground_truth_text"])
                
                if total_chars > 0:
                    stats["error_rate"] = total_errors / total_chars
            
            current_date = period_end
        
        return dict(trends)
    
    def _generate_improvement_suggestions(self,
                                       error_stats: Dict,
                                       error_patterns: List[Dict],
                                       region_analysis: Dict,
                                       confidence_analysis: Dict) -> List[Dict]:
        """生成改进建议"""
        suggestions = []
        
        # 基于错误类型的建议
        if error_stats["error_types"]:
            main_error_type = max(
                error_stats["error_types"].items(),
                key=lambda x: x[1]["count"]
            )[0]
            
            suggestions.append({
                "category": "error_type",
                "priority": "high",
                "issue": f"最常见的错误类型是{self.error_types.get(main_error_type, main_error_type)}",
                "suggestion": self._get_error_type_suggestion(main_error_type)
            })
        
        # 基于区域性能的建议
        if region_analysis:
            worst_region = min(
                region_analysis.items(),
                key=lambda x: x[1]["avg_confidence"]
            )[0]
            
            suggestions.append({
                "category": "region_specific",
                "priority": "medium",
                "issue": f"区域类型'{worst_region}'的识别效果最差",
                "suggestion": self._get_region_specific_suggestion(worst_region)
            })
        
        # 基于置信度分布的建议
        low_conf_samples = confidence_analysis.get("low", {}).get("count", 0)
        if low_conf_samples > 0:
            suggestions.append({
                "category": "confidence",
                "priority": "medium",
                "issue": f"有{low_conf_samples}个样本的置信度较低",
                "suggestion": "考虑调整图像预处理参数，提高图像质量；对于低置信度的区域，可以增加人工审核"
            })
        
        # 基于错误模式的建议
        if error_patterns:
            top_pattern = error_patterns[0]
            suggestions.append({
                "category": "pattern",
                "priority": "high",
                "issue": f"发现频繁出现的错误模式，出现{top_pattern['count']}次",
                "suggestion": "针对特定的错误模式优化识别算法，可能需要增加特定场景的训练数据"
            })
        
        return suggestions
    
    def _get_error_type_suggestion(self, error_type: str) -> str:
        """根据错误类型生成建议"""
        suggestions = {
            "substitution": "增加相似字符的训练样本，优化字符识别模型",
            "insertion": "改进文本分割算法，优化噪声过滤",
            "deletion": "调整文字检测的敏感度，确保不遗漏字符",
            "transposition": "优化文字排序算法，考虑阅读顺序",
            "spacing": "改进空格检测算法，增加空格相关的训练样本",
            "punctuation": "增加标点符号的训练样本，优化标点符号识别",
            "case": "增加大小写混合的训练样本",
            "noise": "改进图像预处理，增强噪声过滤",
            "segmentation": "优化文本区域分割算法，提高分割准确性"
        }
        return suggestions.get(error_type, "收集更多样本进行分析")
    
    def _get_region_specific_suggestion(self, region_type: str) -> str:
        """根据区域类型生成建议"""
        suggestions = {
            "single_column": "优化单栏文本的行间距检测和文本对齐",
            "left_column": "改进左栏文本的边界检测",
            "right_column": "改进右栏文本的边界检测",
            "table": "优化表格线检测和单元格分割",
            "image": "改进图像区域的文字提取",
            "header": "优化页眉检测和特殊格式处理",
            "footer": "优化页脚检测和特殊格式处理",
            "special": "增加特殊区域的训练样本，改进特殊格式识别"
        }
        return suggestions.get(region_type, "针对该区域类型收集更多样本进行分析") 
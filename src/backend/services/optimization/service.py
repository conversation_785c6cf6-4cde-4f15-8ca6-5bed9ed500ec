"""
模型优化服务
提供模型推理性能优化功能
"""

from typing import List, Dict, Any, Optional
import os
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor
import numpy as np
import paddle
import paddle.inference as paddle_infer
from PIL import Image
from datetime import datetime
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.backend.logger import get_logger
from src.backend.config import get_settings

logger = get_logger(__name__)
settings = get_settings()

class ModelOptimizationService:
    """
    模型优化服务
    """
    def __init__(self):
        """初始化服务"""
        self.settings = get_settings()
        self.model_path = self.settings.MODEL_DIR / "ocr"
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 预测器配置
        self.predictor_config = None
        self.predictor = None
        
        # 批处理大小
        self.batch_size = self.settings.MODEL_OPTIMIZATION_BATCH_SIZE
        
        self.optimization_status = {
            "is_running": False,
            "start_time": None,
            "end_time": None,
            "progress": 0,
            "message": ""
        }
        
    def _init_predictor(self):
        """初始化预测器"""
        if self.predictor is not None:
            return
            
        try:
            # 创建配置
            self.predictor_config = paddle_infer.Config()
            
            # 加载模型
            self.predictor_config.set_model(
                os.path.join(self.model_path, "inference.pdmodel"),
                os.path.join(self.model_path, "inference.pdiparams")
            )
            
            # 开启GPU
            if self.settings.OCR_USE_GPU:
                self.predictor_config.enable_use_gpu(
                    memory_pool_init_size_mb=self.settings.OCR_GPU_MEM,
                    device_id=0
                )
                
                # 开启TensorRT加速
                self.predictor_config.enable_tensorrt_engine(
                    workspace_size=1 << 30,
                    max_batch_size=self.batch_size,
                    min_subgraph_size=5,
                    precision_mode=paddle_infer.PrecisionType.Float32,
                    use_static=False,
                    use_calib_mode=False
                )
            else:
                # 使用CPU
                self.predictor_config.disable_gpu()
                
                # 开启MKLDNN加速
                self.predictor_config.enable_mkldnn()
                    
            # 开启内存优化
            self.predictor_config.enable_memory_optim()
            
            # 禁用glog日志
            self.predictor_config.disable_glog_info()
            
            # 创建预测器
            self.predictor = paddle_infer.create_predictor(
                self.predictor_config
            )
            
            logger.info("Model predictor initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize predictor: {str(e)}")
            raise
            
    def _preprocess_image(
        self,
        image: Image.Image
    ) -> np.ndarray:
        """
        预处理图片
        
        Args:
            image: PIL图片
            
        Returns:
            np.ndarray: 预处理后的图片数据
        """
        # 调整大小
        img = image.resize(
            (640, 640),
            Image.LANCZOS
        )
        
        # 转换为numpy数组
        img = np.array(img).astype(np.float32)
        
        # 归一化
        img = img / 255.0
        
        # 转换为CHW格式
        img = img.transpose((2, 0, 1))
        
        # 添加批次维度
        img = np.expand_dims(img, axis=0)
        
        return img
        
    def _batch_preprocess_images(
        self,
        images: List[Image.Image]
    ) -> np.ndarray:
        """
        批量预处理图片
        
        Args:
            images: PIL图片列表
            
        Returns:
            np.ndarray: 预处理后的批次数据
        """
        processed = []
        
        for image in images:
            img = self._preprocess_image(image)
            processed.append(img)
            
        return np.concatenate(processed, axis=0)
        
    def _postprocess_results(
        self,
        results: List[np.ndarray]
    ) -> List[Dict[str, Any]]:
        """
        后处理预测结果
        
        Args:
            results: 预测结果
            
        Returns:
            List[Dict[str, Any]]: 处理后的结果
        """
        processed = []
        
        for result in results:
            # 处理检测结果
            boxes = result[0]
            scores = result[1]
            
            # 过滤低置信度的结果
            mask = scores > 0.5
            boxes = boxes[mask]
            scores = scores[mask]
            
            # 转换为字典格式
            processed.append({
                "boxes": boxes.tolist(),
                "scores": scores.tolist()
            })
            
        return processed
        
    async def predict_single(
        self,
        image: Image.Image
    ) -> Dict[str, Any]:
        """
        单张图片预测
        
        Args:
            image: PIL图片
            
        Returns:
            Dict[str, Any]: 预测结果
        """
        # 初始化预测器
        self._init_predictor()
        
        # 预处理
        img = self._preprocess_image(image)
        
        # 获取输入输出句柄
        input_handle = self.predictor.get_input_handle(0)
        output_handle = self.predictor.get_output_handle(0)
        
        # 设置输入数据
        input_handle.copy_from_cpu(img)
        
        # 运行预测
        self.predictor.run()
        
        # 获取输出数据
        output = output_handle.copy_to_cpu()
        
        # 后处理
        results = self._postprocess_results([output])
        
        return results[0]
        
    async def predict_batch(
        self,
        images: List[Image.Image]
    ) -> List[Dict[str, Any]]:
        """
        批量预测
        
        Args:
            images: PIL图片列表
            
        Returns:
            List[Dict[str, Any]]: 预测结果列表
        """
        # 初始化预测器
        self._init_predictor()
        
        # 预处理
        batch = self._batch_preprocess_images(images)
        
        # 获取输入输出句柄
        input_handle = self.predictor.get_input_handle(0)
        output_handle = self.predictor.get_output_handle(0)
        
        # 设置输入数据
        input_handle.copy_from_cpu(batch)
        
        # 运行预测
        self.predictor.run()
        
        # 获取输出数据
        output = output_handle.copy_to_cpu()
        
        # 后处理
        return self._postprocess_results(output)
        
    async def warmup(self):
        """预热模型"""
        logger.info("Warming up model...")
        
        # 创建随机图片
        dummy_image = Image.fromarray(
            np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        )
        
        # 运行单次预测
        await self.predict_single(dummy_image)
        
        logger.info("Model warmup completed")
        
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        if self.predictor is None:
            return {}
            
        stats = {
            "memory_used": self.predictor.get_memory_used(),
            "runtime_ms": self.predictor.get_runtime_ms(),
            "batch_size": self.batch_size
        }
        
        if self.settings.OCR_USE_GPU:
            stats.update({
                "gpu_memory": self.predictor.get_gpu_memory(),
                "device_id": 0
            })
            
        return stats
        
    async def optimize_model(self) -> Dict:
        """
        优化模型
        
        Returns:
            Dict: 优化结果
        """
        if self.optimization_status["is_running"]:
            return {"message": "Model optimization is already running"}
            
        try:
            self.optimization_status.update({
                "is_running": True,
                "start_time": datetime.now(),
                "progress": 0,
                "message": "Starting model optimization"
            })
            
            # 预热模型
            await self.warmup()
            
            # TODO: 实现具体的优化逻辑
            # 1. 收集性能数据
            # 2. 分析瓶颈
            # 3. 应用优化策略
            # 4. 验证优化效果
            
            # 模拟优化过程
            await asyncio.sleep(2)
            
            self.optimization_status.update({
                "is_running": False,
                "end_time": datetime.now(),
                "progress": 100,
                "message": "Model optimization completed"
            })
            
            return {"message": "Model optimization completed successfully"}
            
        except Exception as e:
            logger.error(f"Model optimization failed: {str(e)}")
            self.optimization_status.update({
                "is_running": False,
                "end_time": datetime.now(),
                "progress": 0,
                "message": f"Model optimization failed: {str(e)}"
            })
            raise
            
    async def get_optimization_status(self) -> Dict:
        """
        获取优化状态
        
        Returns:
            Dict: 优化状态
        """
        return self.optimization_status 
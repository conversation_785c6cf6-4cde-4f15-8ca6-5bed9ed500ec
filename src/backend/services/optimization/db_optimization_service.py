"""
数据库优化服务
提供数据库查询优化和索引管理功能
"""

from typing import List, Dict, Any, Optional
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo import IndexModel, ASCENDING, DESCENDING, TEXT
import asyncio
import logging
from datetime import datetime, timedelta

from src.backend.logger import get_logger
from src.backend.config import get_settings

logger = get_logger(__name__)

class DBOptimizationService:
    """数据库优化服务"""
    
    def __init__(self, mongodb_client: AsyncIOMotorDatabase):
        """
        初始化数据库优化服务
        
        Args:
            mongodb_client: MongoDB数据库实例
        """
        self.db = mongodb_client
        self.config = get_settings()
        
        # 初始化索引配置
        self.index_configs = {
            'ocr_files': [
                {
                    'fields': [('upload_time', DESCENDING)],
                    'name': 'upload_time_idx'
                },
                {
                    'fields': [('status', ASCENDING)],
                    'name': 'status_idx'
                },
                {
                    'fields': [('file_type', ASCENDING)],
                    'name': 'file_type_idx'
                }
            ],
            'performance_metrics': [
                {
                    'fields': [('timestamp', DESCENDING)],
                    'name': 'timestamp_idx'
                },
                {
                    'fields': [('metric_type', ASCENDING)],
                    'name': 'metric_type_idx'
                }
            ]
        }
    
    async def create_indexes(self):
        """创建索引"""
        try:
            for collection_name, indexes in self.index_configs.items():
                collection = self.db[collection_name]
                for index_config in indexes:
                    await collection.create_index(
                        index_config['fields'],
                        name=index_config['name'],
                        background=True
                    )
            logger.info("Successfully created all indexes")
        except Exception as e:
            logger.error(f"Failed to create indexes: {str(e)}")
            raise
    
    async def optimize_query(self, collection: str, query: Dict[str, Any]) -> Dict[str, Any]:
        """
        优化查询
        
        Args:
            collection: 集合名称
            query: 查询条件
            
        Returns:
            Dict[str, Any]: 优化后的查询
        """
        try:
            # 获取集合
            collection = self.db[collection]
            
            # 分析查询
            explain_result = await collection.find(query).explain()
            
            # 根据分析结果优化查询
            optimized_query = self._optimize_query_based_on_explain(query, explain_result)
            
            return optimized_query
            
        except Exception as e:
            logger.error(f"Query optimization failed: {str(e)}")
            raise
    
    def _optimize_query_based_on_explain(
        self,
        original_query: Dict[str, Any],
        explain_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        根据explain结果优化查询
        
        Args:
            original_query: 原始查询
            explain_result: explain结果
            
        Returns:
            Dict[str, Any]: 优化后的查询
        """
        optimized_query = original_query.copy()
        
        # 检查是否使用了索引
        if 'queryPlanner' in explain_result:
            winning_plan = explain_result['queryPlanner']['winningPlan']
            if 'stage' in winning_plan and winning_plan['stage'] == 'COLLSCAN':
                # 全表扫描，尝试添加可能的索引
                for field in original_query:
                    if field in self.index_configs:
                        logger.info(f"Suggesting index for field: {field}")
        
        return optimized_query
    
    async def analyze_query(self, collection: str, query: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析查询性能
        
        Args:
            collection: 集合名称
            query: 查询条件
            
        Returns:
            Dict[str, Any]: 查询计划
        """
        try:
            # 获取集合
            collection = self.db[collection]
            
            # 获取查询计划
            explain_result = await collection.find(query).explain()
            
            # 分析查询计划
            analysis = self._analyze_explain_result(explain_result)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Query analysis failed: {str(e)}")
            raise
    
    def _analyze_explain_result(self, explain_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析explain结果
        
        Args:
            explain_result: explain结果
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        analysis = {
            'index_usage': False,
            'collection_scan': False,
            'execution_stats': {},
            'recommendations': []
        }
        
        if 'queryPlanner' in explain_result:
            winning_plan = explain_result['queryPlanner']['winningPlan']
            
            # 检查索引使用情况
            if 'stage' in winning_plan:
                if winning_plan['stage'] == 'COLLSCAN':
                    analysis['collection_scan'] = True
                    analysis['recommendations'].append(
                        "Query is performing a collection scan. Consider adding appropriate indexes."
                    )
                elif winning_plan['stage'] in ['IXSCAN', 'FETCH']:
                    analysis['index_usage'] = True
        
        return analysis
    
    async def get_collection_stats(self, collection: str) -> Dict[str, Any]:
        """
        获取集合统计信息
        
        Args:
            collection: 集合名称
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 获取集合
            collection = self.db[collection]
            
            # 获取统计信息
            stats = await collection.stats()
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {str(e)}")
            raise 
from motor.motor_asyncio import AsyncIOMotorClient
from typing import Optional
from .config import MONGODB

# 全局数据库连接客户端
client: Optional[AsyncIOMotorClient] = None

async def connect_to_mongo():
    """连接到MongoDB数据库"""
    global client
    if client is None:
        client = AsyncIOMotorClient(MONGODB["uri"])
        
    # 验证连接
    try:
        await client.admin.command('ping')
        print("Successfully connected to MongoDB")
    except Exception as e:
        print(f"Failed to connect to MongoDB: {e}")
        raise e

async def close_mongo_connection():
    """关闭MongoDB连接"""
    global client
    if client is not None:
        client.close()
        client = None
        print("MongoDB connection closed")

def get_database():
    """获取数据库实例"""
    if client is None:
        raise RuntimeError("Database client not initialized")
    return client[MONGODB["db_name"]] 
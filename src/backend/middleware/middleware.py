from fastapi import Request
import time
import logging
from typing import Callable
import uuid

async def request_middleware(request: Request, call_next: Callable):
    """请求处理中间件"""
    # 生成请求 ID
    request_id = str(uuid.uuid4())
    request.state.request_id = request_id
    
    # 记录请求开始
    start_time = time.time()
    logging.info(f"请求开始 - ID: {request_id} - 方法: {request.method} - 路径: {request.url.path}")
    
    try:
        # 处理请求
        response = await call_next(request)
        
        # 记录处理时间
        process_time = time.time() - start_time
        logging.info(f"请求完成 - ID: {request_id} - 状态码: {response.status_code} - 处理时间: {process_time:.2f}s")
        
        # 添加响应头
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Process-Time"] = str(process_time)
        
        return response
        
    except Exception as e:
        # 记录错误
        process_time = time.time() - start_time
        logging.error(f"请求异常 - ID: {request_id} - 错误: {str(e)} - 处理时间: {process_time:.2f}s")
        raise 
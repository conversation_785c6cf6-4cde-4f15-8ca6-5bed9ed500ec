"""
OCR API路由
提供图像文字识别服务，支持默认OCR和基于标注的OCR处理
"""

import json
import uuid
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Optional

from fastapi import APIRouter, File, UploadFile, Form, HTTPException
from fastapi.responses import JSONResponse

# 设置日志
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/ocr", tags=["OCR"])

@router.post("/process")
async def process_default_ocr(file: UploadFile = File(...)):
    """
    默认OCR处理 - 不使用标注
    """
    try:
        # 验证文件类型
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="请上传图像文件")
        
        # 保存上传的文件
        file_path = await save_uploaded_file(file)
        
        # 使用默认OCR处理器
        from ...ocr.processor import OCRProcessor
        
        ocr_processor = OCRProcessor()
        result = ocr_processor.process_image(str(file_path))
        
        # 清理临时文件
        try:
            file_path.unlink()
        except:
            pass
        
        if result:
            return {
                "success": True,
                "text": result.text,
                "confidence": result.confidence,
                "processing_time": result.processing_time,
                "image_width": result.image_width,
                "image_height": result.image_height,
                "total_lines": result.total_lines
            }
        else:
            return {
                "success": False,
                "text": "",
                "confidence": 0,
                "error": "OCR处理失败"
            }
        
    except Exception as e:
        logger.error(f"默认OCR处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"OCR处理失败: {str(e)}")

@router.post("/annotated")
async def process_annotated_ocr(
    file: UploadFile = File(...),
    annotations: str = Form(...)
):
    """
    基于用户标注进行OCR处理
    """
    try:
        # 验证文件类型
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="请上传图像文件")
        
        # 解析标注数据
        try:
            annotation_data = json.loads(annotations)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="标注数据格式错误")
        
        # 保存上传的文件
        file_path = await save_uploaded_file(file)
        
        # 使用基于标注的OCR处理器
        from ...ocr.annotation_guided_ocr import AnnotationGuidedOCR
        
        guided_ocr = AnnotationGuidedOCR()
        
        # 创建临时标注文件
        annotation_file_path = file_path.with_suffix('.json')
        
        # 构建完整的标注数据结构
        full_annotation_data = {
            "image_path": str(file_path),
            "image_width": 0,  # 将在处理时更新
            "image_height": 0,  # 将在处理时更新
            "regions": annotation_data.get("regions", []),
            "created_at": datetime.now().isoformat(),
            "modified_at": datetime.now().isoformat()
        }
        
        with open(annotation_file_path, 'w', encoding='utf-8') as f:
            json.dump(full_annotation_data, f, ensure_ascii=False, indent=2)
        
        # 处理OCR
        results = guided_ocr.process_with_annotations(
            str(file_path),
            str(annotation_file_path),
            create_annotation=False
        )
        
        # 转换结果格式
        ocr_results = []
        for result in results:
            ocr_results.append({
                "regionId": result.region_id,
                "regionType": result.region_type,
                "order": result.order,
                "bbox": result.bbox,
                "text": result.text,
                "confidence": result.confidence
            })
        
        # 清理临时文件
        try:
            file_path.unlink()
            annotation_file_path.unlink()
        except:
            pass
        
        return {
            "success": True,
            "total_regions": len(ocr_results),
            "results": ocr_results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"基于标注的OCR处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"OCR处理失败: {str(e)}")

@router.post("/batch")
async def process_batch_ocr(
    files: List[UploadFile] = File(...),
    use_annotations: bool = Form(False)
):
    """
    批量OCR处理
    """
    try:
        results = []
        
        for file in files:
            try:
                # 验证文件类型
                if not file.content_type or not file.content_type.startswith('image/'):
                    results.append({
                        "filename": file.filename,
                        "status": "error",
                        "error": "不支持的文件类型"
                    })
                    continue
                
                # 保存文件
                file_path = await save_uploaded_file(file)
                
                if use_annotations:
                    # 检查是否有对应的标注文件
                    annotation_file = file_path.with_suffix('.json')
                    if annotation_file.exists():
                        # 使用标注OCR
                        from ...ocr.annotation_guided_ocr import AnnotationGuidedOCR
                        guided_ocr = AnnotationGuidedOCR()
                        ocr_results = guided_ocr.process_with_annotations(
                            str(file_path),
                            str(annotation_file),
                            create_annotation=False
                        )
                        
                        file_result = {
                            "filename": file.filename,
                            "status": "success",
                            "type": "annotated",
                            "regions": []
                        }
                        
                        for result in ocr_results:
                            file_result["regions"].append({
                                "regionId": result.region_id,
                                "regionType": result.region_type,
                                "order": result.order,
                                "text": result.text,
                                "confidence": result.confidence
                            })
                    else:
                        # 回退到默认OCR
                        from ...ocr.processor import OCRProcessor
                        ocr_processor = OCRProcessor()
                        result = ocr_processor.process_image(str(file_path))
                        
                        file_result = {
                            "filename": file.filename,
                            "status": "success",
                            "type": "default",
                            "text": result.text if result else "",
                            "confidence": result.confidence if result else 0
                        }
                else:
                    # 默认OCR处理
                    from ...ocr.processor import OCRProcessor
                    ocr_processor = OCRProcessor()
                    result = ocr_processor.process_image(str(file_path))
                    
                    file_result = {
                        "filename": file.filename,
                        "status": "success",
                        "type": "default",
                        "text": result.text if result else "",
                        "confidence": result.confidence if result else 0
                    }
                
                results.append(file_result)
                
                # 清理临时文件
                try:
                    file_path.unlink()
                except:
                    pass
                    
            except Exception as e:
                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "error": str(e)
                })
        
        return {
            "total_files": len(files),
            "processed": len([r for r in results if r["status"] == "success"]),
            "failed": len([r for r in results if r["status"] == "error"]),
            "results": results
        }
        
    except Exception as e:
        logger.error(f"批量OCR处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量处理失败: {str(e)}")

@router.get("/templates")
async def get_annotation_templates():
    """
    获取标注模板列表
    """
    try:
        templates_dir = Path("templates/annotations")
        templates_dir.mkdir(parents=True, exist_ok=True)
        
        templates = []
        for template_file in templates_dir.glob("*.json"):
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    template_data = json.load(f)
                
                templates.append({
                    "name": template_file.stem,
                    "filename": template_file.name,
                    "regions_count": len(template_data.get("regions", [])),
                    "created_at": template_data.get("created_at", ""),
                    "description": template_data.get("description", "")
                })
            except:
                continue
        
        return {
            "success": True,
            "templates": templates
        }
        
    except Exception as e:
        logger.error(f"获取标注模板失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模板失败: {str(e)}")

@router.post("/templates")
async def save_annotation_template(
    name: str = Form(...),
    description: str = Form(""),
    annotations: str = Form(...)
):
    """
    保存标注模板
    """
    try:
        templates_dir = Path("templates/annotations")
        templates_dir.mkdir(parents=True, exist_ok=True)
        
        # 解析标注数据
        try:
            annotation_data = json.loads(annotations)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="标注数据格式错误")
        
        # 添加模板信息
        template_data = {
            "name": name,
            "description": description,
            "created_at": datetime.now().isoformat(),
            "regions": annotation_data.get("regions", [])
        }
        
        # 保存模板文件
        template_file = templates_dir / f"{name}.json"
        with open(template_file, 'w', encoding='utf-8') as f:
            json.dump(template_data, f, ensure_ascii=False, indent=2)
        
        return {
            "success": True,
            "message": "模板保存成功",
            "template_name": name,
            "regions_count": len(template_data["regions"])
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"保存标注模板失败: {e}")
        raise HTTPException(status_code=500, detail=f"保存模板失败: {str(e)}")

@router.get("/templates/{template_name}")
async def get_annotation_template(template_name: str):
    """
    获取指定的标注模板
    """
    try:
        template_file = Path(f"templates/annotations/{template_name}.json")
        
        if not template_file.exists():
            raise HTTPException(status_code=404, detail="模板不存在")
        
        with open(template_file, 'r', encoding='utf-8') as f:
            template_data = json.load(f)
        
        return {
            "success": True,
            "template": template_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取标注模板失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模板失败: {str(e)}")

@router.delete("/templates/{template_name}")
async def delete_annotation_template(template_name: str):
    """
    删除标注模板
    """
    try:
        template_file = Path(f"templates/annotations/{template_name}.json")
        
        if not template_file.exists():
            raise HTTPException(status_code=404, detail="模板不存在")
        
        template_file.unlink()
        
        return {
            "success": True,
            "message": "模板删除成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除标注模板失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除模板失败: {str(e)}")

@router.get("/status")
async def get_ocr_status():
    """
    获取OCR服务状态
    """
    try:
        # 检查OCR组件是否正常
        from ...ocr.processor import OCRProcessor
        from ...ocr.annotation_guided_ocr import AnnotationGuidedOCR
        
        # 简单的健康检查
        ocr_processor = OCRProcessor()
        guided_ocr = AnnotationGuidedOCR()
        
        return {
            "success": True,
            "status": "healthy",
            "components": {
                "default_ocr": "available",
                "annotated_ocr": "available",
                "templates": "available"
            },
            "supported_formats": ["jpg", "jpeg", "png", "bmp", "tiff", "heic", "heif"],
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"OCR状态检查失败: {e}")
        return {
            "success": False,
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

async def save_uploaded_file(file: UploadFile) -> Path:
    """
    保存上传的文件到临时目录
    """
    # 创建临时目录
    temp_dir = Path("temp")
    temp_dir.mkdir(exist_ok=True)
    
    # 生成唯一文件名
    file_extension = Path(file.filename).suffix if file.filename else ""
    temp_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = temp_dir / temp_filename
    
    # 保存文件
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    return file_path 
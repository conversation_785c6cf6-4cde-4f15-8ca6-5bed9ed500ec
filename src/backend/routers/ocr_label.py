from fastapi import APIRouter, HTTPException, Body
from typing import List, Optional
from datetime import datetime

from ..models.ocr_label import OCRBox, OCRLabel
from ..database import get_database

router = APIRouter(prefix="/api/ocr", tags=["OCR标注"])

@router.post("/labels", response_model=OCRLabel)
async def create_ocr_label(document_id: str = Body(...), labels: List[OCRBox] = Body(default_factory=list)):
    """创建OCR标注"""
    db = get_database()
    collection = db["ocr_labels"]
    
    # 检查是否已存在
    existing = await collection.find_one({"document_id": document_id})
    if existing:
        raise HTTPException(status_code=400, detail="该文档已存在标注")
    
    # 创建新标注
    label = OCRLabel(
        document_id=document_id,
        labels=labels,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    
    result = await collection.insert_one(label.dict())
    label.id = str(result.inserted_id)
    
    return label

@router.get("/labels/{document_id}", response_model=OCRLabel)
async def get_ocr_label(document_id: str):
    """获取OCR标注"""
    db = get_database()
    collection = db["ocr_labels"]
    
    label = await collection.find_one({"document_id": document_id})
    if not label:
        raise HTTPException(status_code=404, detail="标注不存在")
    
    return label

@router.put("/labels/{document_id}", response_model=OCRLabel)
async def update_ocr_label(
    document_id: str,
    labels: List[OCRBox] = Body(...),
    status: Optional[str] = Body(None)
):
    """更新OCR标注"""
    db = get_database()
    collection = db["ocr_labels"]
    
    # 检查是否存在
    label = await collection.find_one({"document_id": document_id})
    if not label:
        raise HTTPException(status_code=404, detail="标注不存在")
    
    # 更新字段
    update_data = {
        "labels": [box.dict() for box in labels],
        "updated_at": datetime.utcnow()
    }
    if status:
        update_data["status"] = status
    
    result = await collection.update_one(
        {"document_id": document_id},
        {"$set": update_data}
    )
    
    if result.modified_count == 0:
        raise HTTPException(status_code=400, detail="更新失败")
    
    updated_label = await collection.find_one({"document_id": document_id})
    return updated_label

@router.delete("/labels/{document_id}")
async def delete_ocr_label(document_id: str):
    """删除OCR标注"""
    db = get_database()
    collection = db["ocr_labels"]
    
    result = await collection.delete_one({"document_id": document_id})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="标注不存在")
    
    return {"status": "success", "message": "标注已删除"} 
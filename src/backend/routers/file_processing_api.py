from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, BackgroundTasks, Request
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import asyncio
import uuid
from datetime import datetime
from bson import ObjectId
from fastapi.responses import JSONResponse
from ..logger import get_logger
from src.backend.services.file_processing_service import FileProcessingService
from src.backend.services.document_conversion_service import DocumentConversionService
from src.backend.services.document_style_service import DocumentStyleService
from src.backend.services.ocr.processor import OCRProcessor

logger = get_logger(__name__)

router = APIRouter(
    tags=["documents"]
)

# 初始化服务
file_service = FileProcessingService()

class ProcessingOptions(BaseModel):
    """处理选项模型"""
    output_format: str = "json"
    apply_style: bool = True
    merge_output: bool = False

class DocumentResponse(BaseModel):
    """文档响应模型"""
    _id: str  # MongoDB ObjectId 将被转换为字符串
    file_id: str
    original_filename: str
    file_type: str
    file_size: int
    upload_time: str
    status: str
    metadata: Optional[Dict[str, Any]] = None

    class Config:
        """Pydantic配置"""
        json_encoders = {
            ObjectId: str  # 将ObjectId转换为字符串
        }

@router.get("")
async def get_documents(request: Request):
    """获取所有文档列表"""
    try:
        logger.info(f"收到获取文档列表请求 - Client IP: {request.client.host}")
        documents = await file_service.get_documents()
        logger.info(f"成功获取文档列表 - 数量: {len(documents)}")
        return JSONResponse(content={
            "success": True,
            "data": documents
        })
    except Exception as e:
        logger.error(f"获取文档列表失败: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"获取文档列表失败: {str(e)}"
            }
        )

@router.get("/{document_id}")
@logger.log_request_response
async def get_document(document_id: str, request: Request):
    """获取单个文档详情"""
    try:
        logger.info(f"收到获取文档详情请求 - Document ID: {document_id}, Client IP: {request.client.host}")
        document = await file_service.get_document(document_id)
        if not document:
            logger.warning(f"文档不存在 - Document ID: {document_id}")
            return JSONResponse(
                status_code=404,
                content={
                    "success": False,
                    "message": "文档不存在"
                }
            )
        logger.info(f"成功获取文档详情 - Document ID: {document_id}")
        return JSONResponse(content={
            "success": True,
            "data": document
        })
    except Exception as e:
        logger.error(f"获取文档详情失败 - Document ID: {document_id}, Error: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"获取文档详情失败: {str(e)}"
            }
        )

@router.post("/upload")
@logger.log_request_response
async def upload_document(
    file: UploadFile = File(...),
    options: Optional[ProcessingOptions] = None,
    request: Request = None
):
    """上传并处理文档"""
    try:
        logger.info(f"收到文档上传请求 - Filename: {file.filename}, Client IP: {request.client.host}")
        logger.info(f"处理选项: {options.model_dump() if options else 'default'}")
        
        result = await file_service.process_document(file, options)
        logger.info(f"文档处理成功 - Document ID: {result.get('id')}")
        
        return JSONResponse(content={
            "success": True,
            "data": result
        })
    except Exception as e:
        logger.error(f"文档上传处理失败 - Filename: {file.filename}, Error: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"文档上传处理失败: {str(e)}"
            }
        )

@router.delete("/{document_id}")
@logger.log_request_response
async def delete_document(document_id: str, request: Request):
    """删除文档"""
    try:
        logger.info(f"收到删除文档请求 - Document ID: {document_id}, Client IP: {request.client.host}")
        result = await file_service.delete_document(document_id)
        if result:
            logger.info(f"文档删除成功 - Document ID: {document_id}")
            return JSONResponse(content={
                "success": True,
                "message": "文档删除成功"
            })
        else:
            logger.warning(f"要删除的文档不存在 - Document ID: {document_id}")
            return JSONResponse(
                status_code=404,
                content={
                    "success": False,
                    "message": "文档不存在"
                }
            )
    except Exception as e:
        logger.error(f"删除文档失败 - Document ID: {document_id}, Error: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"删除文档失败: {str(e)}"
            }
        )
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, BackgroundTasks, Depends, Query, Body
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime
import asyncio
import sys
import uuid
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.backend.services.document.output_service import DocumentOutputService
from src.backend.logger import get_logger

# 设置日志
logger = get_logger(__name__)

router = APIRouter(prefix="/api/document-output", tags=["document-output"])

class BatchProcessRequest(BaseModel):
    file_ids: List[str]
    output_format: str = "markdown"  # markdown, pdf, html, word
    merge_files: bool = False
    style_template: Optional[str] = None

class ProcessingStatus(BaseModel):
    task_id: str
    status: str  # pending, processing, completed, failed
    progress: float  # 0-100
    created_at: datetime
    updated_at: datetime
    error_message: Optional[str] = None

@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    service: DocumentOutputService = Depends()
) -> Dict[str, Any]:
    """上传单个文件并返回文件ID"""
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 开始上传文件: {file.filename}, 大小: {file.size if hasattr(file, 'size') else 'unknown'}")
    try:
        file_id = await service.save_uploaded_file(file)
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[{request_id}] 文件上传成功: {file_id}, 耗时: {duration}秒")
        return {
            "code": 0,
            "message": "success",
            "data": {"file_id": file_id},
            "request_id": request_id
        }
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.error(f"[{request_id}] 文件上传失败: {str(e)}, 耗时: {duration}秒", exc_info=True)
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/upload-batch")
async def upload_batch_files(
    files: List[UploadFile] = File(...),
    service: DocumentOutputService = Depends()
) -> Dict[str, Any]:
    """上传多个文件并返回文件ID列表"""
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 开始批量上传文件，数量: {len(files)}")
    for idx, file in enumerate(files):
        logger.info(f"[{request_id}] 文件 {idx + 1}: {file.filename}")
    try:
        file_ids = []
        for idx, file in enumerate(files, 1):
            file_id = await service.save_uploaded_file(file)
            file_ids.append(file_id)
            logger.info(f"[{request_id}] 文件 {idx}/{len(files)} 上传成功: {file_id}")
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[{request_id}] 批量上传完成，共 {len(files)} 个文件，耗时: {duration}秒")
        return {
            "code": 0,
            "message": "success",
            "data": {"file_ids": file_ids},
            "request_id": request_id
        }
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.error(f"[{request_id}] 批量上传失败: {str(e)}, 耗时: {duration}秒", exc_info=True)
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/process")
async def process_files(
    request: BatchProcessRequest,
    background_tasks: BackgroundTasks,
    service: DocumentOutputService = Depends()
) -> Dict[str, Any]:
    """创建并开始批量处理任务"""
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 开始创建批处理任务，文件数量: {len(request.file_ids)}")
    logger.info(f"[{request_id}] 处理参数: 格式={request.output_format}, 合并={request.merge_files}, 样式模板={request.style_template}")
    
    try:
        task_id = await service.create_batch_task(
            request.file_ids,
            request.output_format,
            request.merge_files,
            request.style_template
        )
        background_tasks.add_task(service.process_batch_task, task_id)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[{request_id}] 任务创建成功: {task_id}, 耗时: {duration}秒")
        
        return {
            "code": 0,
            "message": "success",
            "data": {"task_id": task_id},
            "request_id": request_id
        }
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.error(f"[{request_id}] 创建任务失败: {str(e)}, 耗时: {duration}秒", exc_info=True)
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/status/{task_id}")
async def get_processing_status(
    task_id: str,
    service: DocumentOutputService = Depends()
) -> Dict[str, Any]:
    """获取处理任务的状态"""
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 获取任务状态: {task_id}")
    try:
        status = await service.get_task_status(task_id)
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[{request_id}] 获取任务状态成功: {status['status']}, 耗时: {duration}秒")
        return {
            "code": 0,
            "message": "success",
            "data": ProcessingStatus(**status),
            "request_id": request_id
        }
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.error(f"[{request_id}] 获取任务状态失败: {str(e)}, 耗时: {duration}秒", exc_info=True)
        raise HTTPException(status_code=404, detail=str(e))

@router.delete("/cancel/{task_id}")
async def cancel_processing(
    task_id: str,
    service: DocumentOutputService = Depends()
) -> Dict[str, Any]:
    """取消正在进行的处理任务"""
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 开始取消任务: {task_id}")
    
    try:
        await service.cancel_task(task_id)
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[{request_id}] 任务取消成功, 耗时: {duration}秒")
        return {
            "code": 0,
            "message": "success",
            "data": {"message": "任务已取消"},
            "request_id": request_id
        }
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.error(f"[{request_id}] 取消任务失败: {str(e)}, 耗时: {duration}秒", exc_info=True)
        raise HTTPException(status_code=404, detail=str(e))

@router.get("/download/{task_id}")
async def download_processed_files(
    task_id: str,
    service: DocumentOutputService = Depends()
) -> Dict[str, Any]:
    """下载处理完成的文件"""
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 开始下载任务文件: {task_id}")
    
    try:
        files = await service.get_processed_files(task_id)
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[{request_id}] 文件下载成功, 文件数: {len(files) if isinstance(files, list) else 1}, 耗时: {duration}秒")
        return {
            "code": 0,
            "message": "success",
            "data": files,
            "request_id": request_id
        }
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.error(f"[{request_id}] 下载文件失败: {str(e)}, 耗时: {duration}秒", exc_info=True)
        raise HTTPException(status_code=404, detail=str(e))
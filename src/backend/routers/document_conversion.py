from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from src.backend.services.document_conversion_service import DocumentConversionService
from src.backend.services.document_style_service import DocumentStyleService
import json

router = APIRouter(
    prefix="/api/document-conversion",
    tags=["document-conversion"]
)

# 初始化服务
style_service = DocumentStyleService()
conversion_service = DocumentConversionService(style_service)

class DocumentData(BaseModel):
    """文档数据模型"""
    title: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    content: List[Dict[str, Any]]

class ConversionRequest(BaseModel):
    """转换请求模型"""
    document: DocumentData
    output_format: str
    output_path: Optional[str] = None

class BatchConversionRequest(BaseModel):
    """批量转换请求模型"""
    documents: List[DocumentData]
    output_format: str
    output_dir: Optional[str] = None
    merge: bool = False

@router.post("/convert")
async def convert_document(request: ConversionRequest):
    """转换单个文档"""
    try:
        # 转换文档
        converted_doc = conversion_service.merge_documents(
            [request.document.dict()],
            request.output_format
        )
        
        # 如果指定了输出路径，保存到文件
        if request.output_path:
            conversion_service.save_document(converted_doc, request.output_path)
            return {"saved_path": request.output_path}
        else:
            # 对于二进制内容（如PDF），返回base64编码
            if isinstance(converted_doc, bytes):
                import base64
                return {
                    "content": base64.b64encode(converted_doc).decode('utf-8'),
                    "encoding": "base64"
                }
            else:
                return {"content": converted_doc}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/convert/batch")
async def batch_convert_documents(request: BatchConversionRequest):
    """批量转换文档"""
    try:
        # 转换文档
        output_paths = conversion_service.batch_convert(
            [doc.dict() for doc in request.documents],
            request.output_format,
            request.output_dir or "data/output",
            request.merge
        )
        return {"saved_paths": output_paths}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/convert/file")
async def convert_file(
    file: UploadFile = File(...),
    output_format: str = "pdf",
    output_path: Optional[str] = None
):
    """转换上传的文件"""
    try:
        content = await file.read()
        document_data = json.loads(content)
        
        # 转换文档
        converted_doc = conversion_service.merge_documents(
            [document_data],
            output_format
        )
        
        # 如果指定了输出路径，保存到文件
        if output_path:
            conversion_service.save_document(converted_doc, output_path)
            return {"saved_path": output_path}
        else:
            # 对于二进制内容（如PDF），返回base64编码
            if isinstance(converted_doc, bytes):
                import base64
                return {
                    "content": base64.b64encode(converted_doc).decode('utf-8'),
                    "encoding": "base64"
                }
            else:
                return {"content": converted_doc}
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON format")
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/convert/files")
async def convert_files(
    files: List[UploadFile] = File(...),
    output_format: str = "pdf",
    output_dir: Optional[str] = None,
    merge: bool = False
):
    """批量转换上传的文件"""
    try:
        documents = []
        for file in files:
            content = await file.read()
            document_data = json.loads(content)
            documents.append(document_data)
            
        # 转换文档
        output_paths = conversion_service.batch_convert(
            documents,
            output_format,
            output_dir or "data/output",
            merge
        )
        return {"saved_paths": output_paths}
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON format")
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e)) 
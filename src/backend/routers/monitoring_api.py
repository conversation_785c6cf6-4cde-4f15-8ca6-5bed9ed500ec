"""
性能监控API路由
"""

from typing import Dict, List, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from datetime import datetime
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.backend.logger import get_logger
from src.backend.services.performance.monitor import PerformanceMonitorService

logger = get_logger(__name__)
router = APIRouter(prefix="/api/monitoring", tags=["monitoring"])

# 初始化服务
monitor_service = PerformanceMonitorService()

class MonitoringStatus(BaseModel):
    """监控状态响应模型"""
    is_running: bool
    last_check: Optional[datetime] = None
    alerts: List[str] = []

@router.post("/start")
async def start_monitoring() -> Dict[str, Any]:
    """启动性能监控"""
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 开始启动性能监控服务")
    
    try:
        await monitor_service.start_monitoring()
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[{request_id}] 性能监控服务启动成功，耗时: {duration}秒")
        return {
            "code": 0,
            "message": "success",
            "data": {"message": "Performance monitoring started"},
            "request_id": request_id
        }
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.error(f"[{request_id}] 启动监控服务失败: {str(e)}，耗时: {duration}秒", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"启动监控服务失败: {str(e)}"
        )

@router.post("/stop")
async def stop_monitoring() -> Dict[str, Any]:
    """停止性能监控"""
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 开始停止性能监控服务")
    
    try:
        await monitor_service.stop_monitoring()
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[{request_id}] 性能监控服务停止成功，耗时: {duration}秒")
        return {
            "code": 0,
            "message": "success",
            "data": {"message": "Performance monitoring stopped"},
            "request_id": request_id
        }
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.error(f"[{request_id}] 停止监控服务失败: {str(e)}，耗时: {duration}秒", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"停止监控服务失败: {str(e)}"
        )

@router.get("/status")
async def get_monitoring_status() -> Dict[str, Any]:
    """获取监控状态"""
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 开始获取监控服务状态")
    
    try:
        metrics = await monitor_service._get_current_metrics()
        alerts = await monitor_service._check_performance(metrics) if metrics else []
        
        status = MonitoringStatus(
            is_running=monitor_service.is_running,
            last_check=datetime.now() if metrics else None,
            alerts=alerts
        )
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[{request_id}] 获取监控状态成功，当前状态: {status.is_running}，告警数: {len(alerts)}，耗时: {duration}秒")
        return {
            "code": 0,
            "message": "success",
            "data": status.dict(),
            "request_id": request_id
        }
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.error(f"[{request_id}] 获取监控状态失败: {str(e)}，耗时: {duration}秒", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取监控状态失败: {str(e)}"
        )

@router.get("/history")
async def get_performance_history(days: int = 7) -> Dict[str, Any]:
    """获取性能历史数据"""
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 开始获取性能历史数据，查询天数: {days}")
    
    try:
        history = await monitor_service.get_performance_history(days)
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[{request_id}] 获取历史数据成功，数据点数量: {len(history)}，耗时: {duration}秒")
        return {
            "code": 0,
            "message": "success",
            "data": history,
            "request_id": request_id
        }
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.error(f"[{request_id}] 获取历史数据失败: {str(e)}，耗时: {duration}秒", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取历史数据失败: {str(e)}"
        )
"""
数据库优化API路由
提供数据库优化相关的API端点
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any, List
import logging
from datetime import datetime
import json
from motor.motor_asyncio import AsyncIOMotorClient

from src.backend.services.optimization.db_optimization_service import DBOptimizationService
from src.backend.logger import get_logger
from src.backend.config import get_settings

logger = get_logger(__name__)
router = APIRouter()

# 获取配置
config = get_settings()

# 创建MongoDB客户端
mongodb_client = AsyncIOMotorClient(config.get_mongodb_url())[config.MONGODB_DATABASE]

# 初始化数据库优化服务
db_optimization_service = DBOptimizationService(mongodb_client=mongodb_client)

@router.post("/indexes/create")
async def create_indexes() -> Dict[str, Any]:
    """
    创建数据库索引
    
    Returns:
        Dict[str, Any]: 操作结果
    """
    try:
        # 不再需要显式连接，因为已经在初始化时建立了连接
        await db_optimization_service.create_indexes()
        
        return {
            "success": True,
            "message": "Indexes created successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to create indexes: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create indexes: {str(e)}"
        )

@router.post("/query/optimize")
async def optimize_query(
    collection: str,
    query: Dict[str, Any]
) -> Dict[str, Any]:
    """
    优化查询
    
    Args:
        collection: 集合名称
        query: 查询条件
        
    Returns:
        Dict[str, Any]: 优化后的查询
    """
    try:
        optimized_query = await db_optimization_service.optimize_query(
            collection,
            query
        )
        
        return {
            "success": True,
            "data": optimized_query
        }
        
    except Exception as e:
        logger.error(f"Query optimization failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Query optimization failed: {str(e)}"
        )

@router.post("/query/analyze")
async def analyze_query(
    collection: str,
    query: Dict[str, Any]
) -> Dict[str, Any]:
    """
    分析查询性能
    
    Args:
        collection: 集合名称
        query: 查询条件
        
    Returns:
        Dict[str, Any]: 查询计划
    """
    try:
        analysis = await db_optimization_service.analyze_query(
            collection,
            query
        )
        
        return {
            "success": True,
            "data": analysis
        }
        
    except Exception as e:
        logger.error(f"Query analysis failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Query analysis failed: {str(e)}"
        )

@router.get("/stats/{collection}")
async def get_collection_stats(
    collection: str
) -> Dict[str, Any]:
    """
    获取集合统计信息
    
    Args:
        collection: 集合名称
        
    Returns:
        Dict[str, Any]: 统计信息
    """
    try:
        stats = await db_optimization_service.get_collection_stats(
            collection
        )
        
        return {
            "success": True,
            "data": stats
        }
        
    except Exception as e:
        logger.error(f"Failed to get collection stats: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get collection stats: {str(e)}"
        ) 
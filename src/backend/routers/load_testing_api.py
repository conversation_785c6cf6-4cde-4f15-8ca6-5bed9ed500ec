"""
负载测试API路由
提供负载测试相关的API端点
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any
from datetime import datetime
import uuid

from src.backend.services.load_testing.load_testing_service import LoadTestingService
from src.backend.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/api/load-testing", tags=["load-testing"])
@router.get("/results/{test_id}")
async def get_test_results(test_id: str) -> Dict[str, Any]:
    """获取指定测试的详细结果
    
    Args:
        test_id: 测试ID
        
    Returns:
        Dict[str, Any]: 测试的详细结果
    """
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 开始获取测试结果，测试ID: {test_id}")
    
    try:
        results = await load_testing_service.get_test_results(test_id)
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if not results:
            logger.warning(f"[{request_id}] 未找到测试结果，测试ID: {test_id}，耗时: {duration}秒")
            raise HTTPException(status_code=404, detail="测试结果不存在")
            
        logger.info(f"[{request_id}] 获取测试结果成功，耗时: {duration}秒")
        return {
            "code": 0,
            "message": "success",
            "data": results,
            "request_id": request_id
        }
        
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.error(f"[{request_id}] 获取测试结果失败: {str(e)}，耗时: {duration}秒", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取测试结果失败: {str(e)}"
        )

# 初始化服务
load_testing_service = LoadTestingService()

@router.post("/run")
async def run_load_test() -> Dict[str, Any]:
    """运行负载测试
    
    Returns:
        Dict[str, Any]: 测试结果，包含性能指标和统计数据
    """
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 开始执行负载测试")
    
    try:
        results = await load_testing_service.run_test()
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 记录测试结果的关键指标
        metrics = results.get("metrics", {})
        logger.info(
            f"[{request_id}] 负载测试完成，耗时: {duration}秒\n"
            f"平均响应时间: {metrics.get('avg_response_time', 0)}ms\n"
            f"请求总数: {metrics.get('total_requests', 0)}\n"
            f"成功率: {metrics.get('success_rate', 0)}%\n"
            f"每秒请求数: {metrics.get('requests_per_second', 0)}"
        )
        
        return {
            "code": 0,
            "message": "success",
            "data": results,
            "request_id": request_id
        }
        
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.error(f"[{request_id}] 负载测试失败: {str(e)}，耗时: {duration}秒", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"负载测试失败: {str(e)}"
        )
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from ..services.markdown_conversion_service import MarkdownConversionService
from ..services.document_style_service import DocumentStyleService
import json

router = APIRouter(
    prefix="/api/markdown",
    tags=["markdown"]
)

# 初始化服务
style_service = DocumentStyleService()
markdown_service = MarkdownConversionService(style_service)

class DocumentData(BaseModel):
    """文档数据模型"""
    title: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    content: List[Dict[str, Any]]

class BatchConversionRequest(BaseModel):
    """批量转换请求模型"""
    documents: List[DocumentData]
    output_dir: Optional[str] = None

@router.post("/convert")
async def convert_to_markdown(document: DocumentData):
    """将单个文档转换为Markdown格式"""
    try:
        markdown_content = markdown_service.convert_to_markdown(document.dict())
        return {"content": markdown_content}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/convert/batch")
async def batch_convert_to_markdown(request: BatchConversionRequest):
    """批量转换文档为Markdown格式"""
    try:
        markdown_contents = markdown_service.batch_convert_to_markdown(
            [doc.dict() for doc in request.documents]
        )
        
        if request.output_dir:
            saved_paths = markdown_service.batch_save_markdown(
                markdown_contents,
                request.output_dir
            )
            return {"saved_paths": saved_paths}
        else:
            return {"contents": markdown_contents}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/convert/file")
async def convert_file_to_markdown(
    file: UploadFile = File(...),
    output_path: Optional[str] = None
):
    """将上传的文件转换为Markdown格式"""
    try:
        content = await file.read()
        document_data = json.loads(content)
        markdown_content = markdown_service.convert_to_markdown(document_data)
        
        if output_path:
            markdown_service.save_markdown(markdown_content, output_path)
            return {"saved_path": output_path}
        else:
            return {"content": markdown_content}
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON format")
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/convert/files")
async def convert_files_to_markdown(
    files: List[UploadFile] = File(...),
    output_dir: Optional[str] = None
):
    """批量转换上传的文件为Markdown格式"""
    try:
        documents = []
        for file in files:
            content = await file.read()
            document_data = json.loads(content)
            documents.append(document_data)
            
        markdown_contents = markdown_service.batch_convert_to_markdown(documents)
        
        if output_dir:
            filenames = [f"{file.filename.rsplit('.', 1)[0]}.md" for file in files]
            saved_paths = markdown_service.batch_save_markdown(
                markdown_contents,
                output_dir,
                filenames
            )
            return {"saved_paths": saved_paths}
        else:
            return {"contents": markdown_contents}
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON format")
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e)) 
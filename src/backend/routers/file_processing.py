from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Body
from src.backend.services.file_processing_service import FileProcessingService
from src.backend.dependencies import get_file_processing_service
from src.backend.logger import get_logger
from datetime import datetime
import uuid
from pydantic import BaseModel

logger = get_logger(__name__)

router = APIRouter(prefix="/api/file-processing")

class AnnotationCreate(BaseModel):
    content: str

class Annotation(BaseModel):
    id: str
    content: str
    created_at: datetime

@router.get("/documents")
async def list_documents(
    file_processing_service: FileProcessingService = Depends(get_file_processing_service)
) -> Dict[str, Any]:
    """获取文档列表"""
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 开始获取文档列表")
    
    try:
        documents = await file_processing_service.list_documents()
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[{request_id}] 成功获取文档列表，共 {len(documents)} 个文档，耗时: {duration}秒")
        return {
            "code": 0,
            "message": "success",
            "data": documents,
            "request_id": request_id
        }
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.error(f"[{request_id}] 获取文档列表失败: {str(e)}，耗时: {duration}秒", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/documents/upload")
async def upload_document(
    file: UploadFile = File(...),
    file_processing_service: FileProcessingService = Depends(get_file_processing_service)
) -> Dict[str, Any]:
    """上传文档"""
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 开始上传文件: {file.filename}, 大小: {file.size if hasattr(file, 'size') else 'unknown'}")
    
    try:
        document = await file_processing_service.add_document(file)
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[{request_id}] 文件上传成功，耗时: {duration}秒")
        return {
            "code": 0,
            "message": "success",
            "data": {
                "_id": str(document["_id"]),
                "file_id": str(document["_id"]),
                "original_filename": document["filename"],
                "status": document["status"]
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/documents/{file_id}/annotations")
async def create_annotation(
    file_id: str,
    annotation: AnnotationCreate,
    file_processing_service: FileProcessingService = Depends(get_file_processing_service)
) -> Dict[str, Any]:
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 开始为文档 {file_id} 创建标注")
    
    try:
        new_annotation = {
            "id": str(uuid.uuid4()),
            "content": annotation.content,
            "created_at": datetime.now()
        }
        success = await file_processing_service.save_annotation(file_id, new_annotation)
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if success:
            logger.info(f"[{request_id}] 标注创建成功，耗时: {duration}秒")
        else:
            logger.error(f"[{request_id}] 标注创建失败，耗时: {duration}秒")
        if not success:
            raise HTTPException(status_code=500, detail="Failed to save annotation")
        return {"data": new_annotation}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/documents/{file_id}/annotations")
async def get_annotations(
    file_id: str,
    file_processing_service: FileProcessingService = Depends(get_file_processing_service)
) -> Dict[str, Any]:
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 开始获取文档 {file_id} 的标注")
    
    try:
        annotations = await file_processing_service.get_annotations(file_id)
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"[{request_id}] 获取标注成功，共 {len(annotations)} 条，耗时: {duration}秒")
        return {
            "code": 0,
            "message": "success",
            "data": annotations,
            "request_id": request_id
        }
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.error(f"[{request_id}] 获取标注失败: {str(e)}，耗时: {duration}秒", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/documents/{file_id}/annotations/{annotation_id}")
async def delete_annotation(
    file_id: str,
    annotation_id: str,
    file_processing_service: FileProcessingService = Depends(get_file_processing_service)
) -> Dict[str, Any]:
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 开始删除文档 {file_id} 的标注 {annotation_id}")
    
    try:
        success = await file_processing_service.delete_annotation(file_id, annotation_id)
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if not success:
            logger.warning(f"[{request_id}] 标注不存在，耗时: {duration}秒")
            raise HTTPException(status_code=404, detail="Annotation not found")
            
        logger.info(f"[{request_id}] 标注删除成功，耗时: {duration}秒")
        return {
            "code": 0,
            "message": "success",
            "data": {"message": "Annotation deleted successfully"},
            "request_id": request_id
        }
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.error(f"[{request_id}] 删除标注失败: {str(e)}，耗时: {duration}秒", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

# ... 保留现有的其他路由 ... 
"""
模型优化API路由
"""

from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, File, UploadFile
from pydantic import BaseModel
from datetime import datetime
import sys
from pathlib import Path
import io
from PIL import Image

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.backend.logger import get_logger
from src.backend.services.optimization.service import ModelOptimizationService

logger = get_logger(__name__)
router = APIRouter(prefix="/api/optimization", tags=["optimization"])

# 初始化服务
model_optimization_service = ModelOptimizationService()

@router.post("/optimize")
async def optimize_model() -> Dict[str, Any]:
    """优化模型，会返回优化过程的详细信息"""
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 开始模型优化")
    
    try:
        result = await model_optimization_service.optimize_model()
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 记录优化结果的关键指标
        metrics = result.get("metrics", {})
        logger.info(
            f"[{request_id}] 模型优化完成，耗时: {duration}秒\n"
            f"优化前大小: {metrics.get('original_size', 0)}MB\n"
            f"优化后大小: {metrics.get('optimized_size', 0)}MB\n"
            f"性能提升: {metrics.get('performance_improvement', 0)}%\n"
            f"精度损失: {metrics.get('accuracy_loss', 0)}%"
        )
        
        return {
            "code": 0,
            "message": "success",
            "data": result,
            "request_id": request_id
        }
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.error(f"[{request_id}] 模型优化失败: {str(e)}，耗时: {duration}秒", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status")
async def get_optimization_status():
    """
    获取优化状态
    """
    try:
        return await model_optimization_service.get_optimization_status()
    except Exception as e:
        logger.error(f"Failed to get optimization status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/predict")
async def predict_single(
    file: UploadFile = File(...)
) -> Dict[str, Any]:
    """
    单张图片预测
    
    Args:
        file: 上传的图片文件
        
    Returns:
        Dict[str, Any]: 预测结果
    """
    try:
        # 读取图片
        content = await file.read()
        image = Image.open(io.BytesIO(content))
        
        # 预测
        result = await model_optimization_service.predict_single(image)
        
        return {
            "success": True,
            "data": result
        }
        
    except Exception as e:
        logger.error(f"Prediction failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Prediction failed: {str(e)}"
        )

@router.post("/predict/batch")
async def predict_batch(
    files: List[UploadFile] = File(...)
) -> Dict[str, Any]:
    """
    批量预测
    
    Args:
        files: 上传的图片文件列表
        
    Returns:
        Dict[str, Any]: 预测结果列表
    """
    try:
        # 读取图片
        images = []
        for file in files:
            content = await file.read()
            image = Image.open(io.BytesIO(content))
            images.append(image)
            
        # 预测
        results = await model_optimization_service.predict_batch(images)
        
        return {
            "success": True,
            "data": results
        }
        
    except Exception as e:
        logger.error(f"Batch prediction failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Batch prediction failed: {str(e)}"
        )

@router.post("/warmup")
async def warmup_model() -> Dict[str, Any]:
    """
    预热模型
    
    Returns:
        Dict[str, Any]: 操作结果
    """
    try:
        await model_optimization_service.warmup()
        
        return {
            "success": True,
            "message": "Model warmup completed"
        }
        
    except Exception as e:
        logger.error(f"Model warmup failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Model warmup failed: {str(e)}"
        )

@router.get("/stats")
async def get_performance_stats() -> Dict[str, Any]:
    """获取性能统计信息"""
    start_time = datetime.now()
    request_id = str(uuid.uuid4())
    logger.info(f"[{request_id}] 开始获取性能统计信息")
    
    try:
        stats = await model_optimization_service.get_performance_stats()
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 记录关键性能指标
        logger.info(
            f"[{request_id}] 获取性能统计完成，耗时: {duration}秒\n"
            f"平均推理时间: {stats.get('avg_inference_time', 0)}ms\n"
            f"内存使用率: {stats.get('memory_usage', 0)}%\n"
            f"GPU利用率: {stats.get('gpu_utilization', 0)}%\n"
            f"每秒处理图片数: {stats.get('images_per_second', 0)}"
        )
        
        return {
            "code": 0,
            "message": "success",
            "data": stats,
            "request_id": request_id
        }
        
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.error(f"[{request_id}] 获取性能统计失败: {str(e)}，耗时: {duration}秒", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取性能统计失败: {str(e)}"
        )
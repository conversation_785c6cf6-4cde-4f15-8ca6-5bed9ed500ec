#!/usr/bin/env python3
"""
标注系统API端点 - FastAPI集成
提供标注任务管理、数据交换和工作流集成的RESTful API
"""

import sys
import os
from pathlib import Path
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Union, Any
import logging
import tempfile
import zipfile
import io
import shutil
from fastapi import APIRouter, HTTPException, BackgroundTasks, Query, Body, UploadFile, File, Depends
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel, Field

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.backend.services.annotation.service import (
    AnnotationService, 
    AnnotationStatus, 
    AnnotationType,
    AnnotationData,
    AnnotationTask,
    create_annotation_service
)
from src.backend.services.ocr.processor import OCRProcessor, OCRResult
from src.backend.services.data_transfer.export import DataExportService
from src.backend.models.mongodb import MongoDBManager
from src.backend.logger import get_logger

# 设置日志
logger = get_logger(__name__)

# 创建路由器
router = APIRouter(tags=["annotation"])

# 全局服务实例
annotation_service = None
ocr_processor = None

def get_annotation_service() -> AnnotationService:
    """获取标注服务实例"""
    global annotation_service
    if annotation_service is None:
        annotation_service = create_annotation_service()
    return annotation_service

def get_ocr_processor() -> OCRProcessor:
    """获取OCR处理器实例"""
    global ocr_processor
    if ocr_processor is None:
        ocr_processor = OCRProcessor(use_gpu=False)
    return ocr_processor

# Pydantic模型
class AnnotationTaskRequest(BaseModel):
    """创建标注任务请求"""
    file_id: str = Field(..., description="文件ID")
    priority: int = Field(default=1, description="任务优先级 (1-5)")
    assigned_to: Optional[str] = Field(default=None, description="分配给的标注员")
    due_date: Optional[datetime] = Field(default=None, description="截止日期")

class AnnotationUpdateRequest(BaseModel):
    """更新标注请求"""
    annotation_id: str = Field(..., description="标注ID")
    corrected_text: Optional[str] = Field(default=None, description="修正后的文本")
    bounding_box: Optional[List[List[int]]] = Field(default=None, description="边界框坐标")
    classification: Optional[str] = Field(default=None, description="分类标签")
    quality_score: Optional[float] = Field(default=None, description="质量评分 (0-1)")
    status: Optional[str] = Field(default=None, description="标注状态")

class AnnotationTaskResponse(BaseModel):
    """标注任务响应"""
    task_id: str
    file_id: str
    status: str
    priority: int
    assigned_to: Optional[str]
    created_at: datetime
    due_date: Optional[datetime]
    annotations_count: int
    completed_annotations: int
    progress: float

class AnnotationResponse(BaseModel):
    """标注响应"""
    annotation_id: str
    ocr_result_id: str
    annotation_type: str
    original_text: str
    corrected_text: Optional[str]
    bounding_box: Optional[List[List[int]]]
    classification: Optional[str]
    quality_score: Optional[float]
    confidence: Optional[float]
    status: str
    created_at: datetime
    updated_at: datetime

class AnnotationStatisticsResponse(BaseModel):
    """标注统计响应"""
    total_tasks: int
    pending_tasks: int
    completed_tasks: int
    total_annotations: int
    accuracy_improvement: float
    average_quality_score: float

class ExportRequest(BaseModel):
    """导出请求参数"""
    version: Optional[str] = Field(default=None, description="数据集版本")
    output_format: Optional[str] = Field(default="paddleocr", description="输出格式")
    include_images: bool = Field(default=False, description="是否包含图像文件")
    batch_size: Optional[int] = Field(default=100, description="批处理大小")

# API端点
@router.post("/tasks", response_model=AnnotationTaskResponse)
async def create_annotation_task(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(..., description="要标注的文件"),
    priority: int = Query(default=1, description="任务优先级"),
    assigned_to: Optional[str] = Query(default=None, description="分配给的标注员")
):
    """
    创建标注任务
    上传文件并自动进行OCR处理，然后创建标注任务
    """
    try:
        service = get_annotation_service()
        processor = get_ocr_processor()
        
        # 保存上传的文件
        file_id = str(uuid.uuid4())
        upload_dir = Path("uploads")
        upload_dir.mkdir(exist_ok=True)
        
        file_path = upload_dir / f"{file_id}_{file.filename}"
        with open(file_path, "wb") as f:
            content = await file.read()
            f.write(content)
        
        # 进行OCR处理
        ocr_results = processor.process_image(str(file_path))
        
        # 创建标注任务
        task = service.create_annotation_task(
            file_id=file_id,
            ocr_results=ocr_results,
            priority=priority,
            assigned_to=assigned_to
        )
        
        # 计算进度
        completed_annotations = len([a for a in task.annotations if a.status == AnnotationStatus.COMPLETED])
        progress = completed_annotations / len(task.annotations) if task.annotations else 0
        
        return AnnotationTaskResponse(
            task_id=task.task_id,
            file_id=task.file_id,
            status=task.status.value,
            priority=task.priority,
            assigned_to=task.assigned_to,
            created_at=task.created_at,
            due_date=task.due_date,
            annotations_count=len(task.annotations),
            completed_annotations=completed_annotations,
            progress=progress
        )
        
    except Exception as e:
        logger.error(f"创建标注任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建标注任务失败: {str(e)}")

@router.get("/tasks/{task_id}", response_model=Dict[str, Any])
async def get_annotation_task(task_id: str):
    """获取标注任务详情"""
    try:
        service = get_annotation_service()
        task = service.get_annotation_task(task_id)
        
        if not task:
            raise HTTPException(status_code=404, detail="标注任务不存在")
        
        # 转换为响应格式
        annotations = []
        for annotation in task.annotations:
            annotations.append(AnnotationResponse(
                annotation_id=annotation.annotation_id,
                ocr_result_id=annotation.ocr_result_id,
                annotation_type=annotation.annotation_type.value,
                original_text=annotation.original_text,
                corrected_text=annotation.corrected_text,
                bounding_box=annotation.bounding_box,
                classification=annotation.classification,
                quality_score=annotation.quality_score,
                confidence=annotation.confidence,
                status=annotation.status.value,
                created_at=annotation.created_at,
                updated_at=annotation.updated_at
            ))
        
        completed_annotations = len([a for a in task.annotations if a.status == AnnotationStatus.COMPLETED])
        progress = completed_annotations / len(task.annotations) if task.annotations else 0
        
        return {
            "task": AnnotationTaskResponse(
                task_id=task.task_id,
                file_id=task.file_id,
                status=task.status.value,
                priority=task.priority,
                assigned_to=task.assigned_to,
                created_at=task.created_at,
                due_date=task.due_date,
                annotations_count=len(task.annotations),
                completed_annotations=completed_annotations,
                progress=progress
            ),
            "annotations": annotations
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取标注任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取标注任务失败: {str(e)}")

@router.get("/tasks", response_model=List[AnnotationTaskResponse])
async def list_annotation_tasks(
    status: Optional[str] = Query(default=None, description="任务状态过滤"),
    assigned_to: Optional[str] = Query(default=None, description="分配给的标注员"),
    limit: int = Query(default=10, description="返回数量限制")
):
    """获取标注任务列表"""
    try:
        service = get_annotation_service()
        tasks = service.list_annotation_tasks(status=status, assigned_to=assigned_to, limit=limit)
        
        # 转换为响应格式
        response = []
        for task in tasks:
            completed_annotations = len([a for a in task.annotations if a.status == AnnotationStatus.COMPLETED])
            progress = completed_annotations / len(task.annotations) if task.annotations else 0
            
            response.append(AnnotationTaskResponse(
                task_id=task.task_id,
                file_id=task.file_id,
                status=task.status.value,
                priority=task.priority,
                assigned_to=task.assigned_to,
                created_at=task.created_at,
                due_date=task.due_date,
                annotations_count=len(task.annotations),
                completed_annotations=completed_annotations,
                progress=progress
            ))
            
        return response
        
    except Exception as e:
        logger.error(f"获取标注任务列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取标注任务列表失败: {str(e)}")

@router.put("/annotations", response_model=Dict[str, str])
async def update_annotation(update_request: AnnotationUpdateRequest):
    """更新标注"""
    try:
        service = get_annotation_service()
        service.update_annotation(
            annotation_id=update_request.annotation_id,
            corrected_text=update_request.corrected_text,
            bounding_box=update_request.bounding_box,
            classification=update_request.classification,
            quality_score=update_request.quality_score,
            status=update_request.status
        )
        return {"message": "标注更新成功"}
        
    except Exception as e:
        logger.error(f"更新标注失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新标注失败: {str(e)}")

@router.post("/tasks/{task_id}/submit", response_model=Dict[str, str])
async def submit_annotation_task(task_id: str):
    """提交标注任务"""
    try:
        service = get_annotation_service()
        service.submit_annotation_task(task_id)
        return {"message": "标注任务提交成功"}
        
    except Exception as e:
        logger.error(f"提交标注任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"提交标注任务失败: {str(e)}")

@router.get("/tasks/{task_id}/export", response_model=Dict[str, Any])
async def export_annotations(task_id: str):
    """导出标注数据"""
    try:
        service = get_annotation_service()
        export_data = service.export_annotations(task_id)
        return export_data
        
    except Exception as e:
        logger.error(f"导出标注数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出标注数据失败: {str(e)}")

@router.post("/tasks/import", response_model=Dict[str, str])
async def import_annotations(json_data: Dict[str, Any] = Body(...)):
    """导入标注数据"""
    try:
        service = get_annotation_service()
        service.import_annotations(json_data)
        return {"message": "标注数据导入成功"}
        
    except Exception as e:
        logger.error(f"导入标注数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"导入标注数据失败: {str(e)}")

@router.get("/statistics", response_model=AnnotationStatisticsResponse)
async def get_annotation_statistics():
    """获取标注统计信息"""
    try:
        service = get_annotation_service()
        stats = service.get_statistics()
        return AnnotationStatisticsResponse(
            total_tasks=stats["total_tasks"],
            pending_tasks=stats["pending_tasks"],
            completed_tasks=stats["completed_tasks"],
            total_annotations=stats["total_annotations"],
            accuracy_improvement=stats["accuracy_improvement"],
            average_quality_score=stats["average_quality_score"]
        )
        
    except Exception as e:
        logger.error(f"获取标注统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取标注统计信息失败: {str(e)}")

@router.get("/health")
async def annotation_health_check():
    """健康检查端点"""
    try:
        service = get_annotation_service()
        processor = get_ocr_processor()
        
        # 检查服务状态
        service_status = service.check_health()
        processor_status = processor.check_health()
        
        if service_status and processor_status:
            return {
                "status": "healthy",
                "service": "ok",
                "processor": "ok",
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "status": "unhealthy",
                "service": "ok" if service_status else "error",
                "processor": "ok" if processor_status else "error",
                "timestamp": datetime.now().isoformat()
            }
            
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        } 
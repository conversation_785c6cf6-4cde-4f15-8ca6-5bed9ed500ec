from fastapi import APIRouter, UploadFile, File, HTTPException, BackgroundTasks, Form, Body, Depends
from fastapi.responses import JSONResponse
from pathlib import Path
import shutil
import uuid
from typing import Dict, List, Optional, Any
import json
import asyncio
from datetime import datetime, timedelta

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.backend.services.ocr.processor import OCRProcessor, OCRResult
from src.backend.services.ocr.enhanced_annotation_guided_ocr import EnhancedAnnotationGuidedOCR
from pydantic import BaseModel, Field
from src.backend.services.ocr.batch_learning import BatchLearningProcessor, BatchLearningConfig, HistoricalDataMigrator

from src.backend.logger import get_logger
from src.backend.services.feedback.collection_service import FeedbackCollectionService
from src.backend.services.cache.decorators import cache, invalidate_cache

# 创建必要的目录
UPLOAD_DIR = Path("uploads")
PROCESSED_DIR = Path("processed")
TEMPLATES_DIR = Path("templates/annotations")
UPLOAD_DIR.mkdir(exist_ok=True)
PROCESSED_DIR.mkdir(exist_ok=True)
TEMPLATES_DIR.mkdir(parents=True, exist_ok=True)

class OCRResponse(BaseModel):
    """OCR 响应模型"""
    task_id: str
    status: str
    message: str
    results: Optional[List[Dict]] = None
    error: Optional[str] = None

class AnnotatedOCRResponse(BaseModel):
    """基于标注的OCR响应模型"""
    success: bool
    total_regions: int
    results: List[Dict]

class ProcessingTask:
    """处理任务状态管理"""
    def __init__(self):
        self.status = "pending"
        self.results = None
        self.error = None
        self.start_time = datetime.now()

# 任务状态存储
processing_tasks: Dict[str, ProcessingTask] = {}

# 批量学习任务状态存储
batch_learning_tasks: Dict[str, Dict] = {}

router = APIRouter(tags=["OCR"])

# 初始化服务
feedback_service = FeedbackCollectionService()

logger = get_logger(__name__)

async def get_ocr_processor():
    """获取 OCR 处理器实例"""
    return OCRProcessor(use_gpu=False)  # 根据实际情况设置 GPU 使用

async def get_enhanced_ocr_processor():
    """获取增强版 OCR 处理器实例"""
    return EnhancedAnnotationGuidedOCR()

async def process_file(task_id: str, file_path: Path, is_pdf: bool = False):
    """异步处理文件"""
    try:
        # 更新任务状态
        task = processing_tasks[task_id]
        task.status = "processing"
        
        # 获取 OCR 处理器实例
        ocr_processor = await get_ocr_processor()
        
        # 处理文件
        if is_pdf:
            results = ocr_processor.process_pdf(file_path)
        else:
            results = ocr_processor.process_image(file_path)
        
        # 将结果转换为可序列化的格式
        serialized_results = []
        for result in results:
            if hasattr(result, 'text'):
                # OCRResult 对象
                serialized_results.append({
                    "text": result.text,
                    "confidence": float(result.confidence),
                    "box": result.box,
                    "page": result.page,
                    "column": result.column
                })
            else:
                # 处理旧格式（字典）
                serialized_results.append({
                    "text": result.get("text", ""),
                    "confidence": float(result.get("confidence", 0)),
                    "box": result.get("box", []),
                    "page": result.get("page", 1),
                    "column": result.get("column", 1)
                })
        
        # 保存结果
        result_file = PROCESSED_DIR / f"{task_id}.json"
        with result_file.open("w", encoding="utf-8") as f:
            json.dump(serialized_results, f, ensure_ascii=False, indent=2)
        
        # 更新任务状态
        task.status = "completed"
        task.results = serialized_results
        
    except Exception as e:
        # 更新任务状态为错误
        task = processing_tasks[task_id]
        task.status = "failed"
        task.error = str(e)

@router.post("/process")
@cache(prefix="ocr:process", ttl=3600)
async def process_document(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
) -> OCRResponse:
    """
    处理上传的文档文件（支持图像和 PDF）- 默认OCR
    """
    # 生成任务 ID
    task_id = str(uuid.uuid4())
    
    # 检查文件类型
    file_extension = Path(file.filename).suffix.lower()
    if file_extension not in [".jpg", ".jpeg", ".png", ".pdf", ".heic", ".heif"]:
        raise HTTPException(
            status_code=400,
            detail="不支持的文件类型。仅支持 JPG、PNG、PDF、HEIC 和 HEIF 文件。"
        )
    
    # 保存文件
    file_path = UPLOAD_DIR / f"{task_id}{file_extension}"
    try:
        with file_path.open("wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"文件保存失败：{str(e)}"
        )
    
    # 创建任务
    processing_tasks[task_id] = ProcessingTask()
    
    # 在后台处理文件
    background_tasks.add_task(
        process_file,
        task_id,
        file_path,
        file_extension == ".pdf"
    )
    
    return OCRResponse(
        task_id=task_id,
        status="pending",
        message="文件已接收，正在处理中"
    )

@router.post("/annotated")
async def process_annotated_ocr(
    file: UploadFile = File(...),
    annotations: str = Form(...)
) -> AnnotatedOCRResponse:
    """
    基于用户标注进行OCR处理
    """
    print(f"🚀 [API] 收到标注OCR请求")
    print(f"   文件名: {file.filename}")
    print(f"   文件类型: {file.content_type}")
    print(f"   标注数据: {annotations[:200]}...")  # 只显示前200个字符
    
    try:
        # 验证文件类型
        if not file.content_type.startswith('image/'):
            print(f"❌ 文件类型验证失败: {file.content_type}")
            raise HTTPException(status_code=400, detail="只支持图像文件")
        
        print(f"✅ 文件类型验证通过")
        
        # 解析标注数据
        try:
            annotation_data = json.loads(annotations)
            print(f"✅ 标注数据解析成功，类型: {type(annotation_data)}")
        except json.JSONDecodeError as e:
            print(f"❌ 标注数据解析失败: {e}")
            raise HTTPException(status_code=400, detail="标注数据格式错误")
        
        # 保存上传的文件
        print(f"💾 保存上传文件...")
        file_path = await save_uploaded_file(file)
        print(f"✅ 文件保存成功: {file_path}")
        
        # 创建临时标注文件
        annotation_file_path = file_path.with_suffix('.json')
        print(f"📝 创建标注文件: {annotation_file_path}")
        
        # 处理标注数据格式
        if isinstance(annotation_data, list):
            # 如果直接是标注列表
            regions = annotation_data
            print(f"✅ 标注数据是列表格式，区域数量: {len(regions)}")
        elif isinstance(annotation_data, dict) and "regions" in annotation_data:
            # 如果是包含regions键的字典
            regions = annotation_data["regions"]
            print(f"✅ 标注数据是字典格式，区域数量: {len(regions)}")
        else:
            # 其他情况，尝试作为单个区域处理
            regions = [annotation_data] if annotation_data else []
            print(f"⚠️ 标注数据格式未知，作为单个区域处理: {len(regions)}")
        
        # 构建完整的标注数据结构
        full_annotation_data = {
            "image_path": str(file_path),
            "image_width": 0,  # 将在处理时更新
            "image_height": 0,  # 将在处理时更新
            "regions": regions,
            "created_at": datetime.now().isoformat(),
            "modified_at": datetime.now().isoformat()
        }
        
        print(f"📄 写入标注文件...")
        with open(annotation_file_path, 'w', encoding='utf-8') as f:
            json.dump(full_annotation_data, f, ensure_ascii=False, indent=2)
        print(f"✅ 标注文件写入成功")
        
        # 使用基于标注的OCR处理器
        try:
            from src.backend.services.ocr.annotation_guided_ocr import AnnotationGuidedOCR
            guided_ocr = AnnotationGuidedOCR()
            print("✅ AnnotationGuidedOCR 导入和初始化成功")  # 调试信息
        except Exception as e:
            print(f"❌ AnnotationGuidedOCR 异常: {type(e).__name__}: {e}")  # 调试信息
            # 如果标注OCR不可用，回退到默认OCR
            results = ocr_processor.process_image(str(file_path))
            file_path.unlink()
            
            if results and len(results) > 0:
                first_result = results[0]
                return AnnotatedOCRResponse(
                    success=True,
                    total_regions=1,
                    results=[{
                        "regionId": "default_1",
                        "regionType": "single_column",
                        "order": 1,
                        "bbox": [0, 0, first_result.image_width, first_result.image_height],
                        "text": first_result.text,
                        "confidence": first_result.confidence
                    }]
                )
            else:
                return AnnotatedOCRResponse(
                    success=False,
                    total_regions=0,
                    results=[]
                )
        
        # 处理OCR
        print(f"🔍 调用 process_with_annotations:")
        print(f"   图像路径: {file_path}")
        print(f"   标注文件路径: {annotation_file_path}")
        print(f"   标注文件存在: {annotation_file_path.exists()}")
        
        results = guided_ocr.process_with_annotations(
            str(file_path),
            str(annotation_file_path),
            create_annotation=False
        )
        
        print(f"🔍 process_with_annotations 返回结果数量: {len(results)}")
        
        # 转换结果格式
        ocr_results = []
        for result in results:
            ocr_results.append({
                "regionId": result.region_id,
                "regionType": result.region_type,
                "order": result.order,
                "bbox": result.bbox,
                "text": result.text,
                "confidence": result.confidence
            })
        
        print(f"✅ 结果转换完成，最终结果数量: {len(ocr_results)}")
        
        # 清理临时文件
        try:
            file_path.unlink()
            annotation_file_path.unlink()
            print(f"🗑️ 临时文件清理完成")
        except:
            print(f"⚠️ 临时文件清理失败")
            pass
        
        return AnnotatedOCRResponse(
            success=True,
            total_regions=len(ocr_results),
            results=ocr_results
        )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 标注OCR处理异常: {type(e).__name__}: {e}")
        raise HTTPException(status_code=500, detail=f"OCR处理失败: {str(e)}")

@router.get("/templates")
async def get_annotation_templates():
    """
    获取标注模板列表
    """
    try:
        templates = []
        for template_file in TEMPLATES_DIR.glob("*.json"):
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    template_data = json.load(f)
                
                templates.append({
                    "name": template_file.stem,
                    "filename": template_file.name,
                    "regions_count": len(template_data.get("regions", [])),
                    "created_at": template_data.get("created_at", ""),
                    "description": template_data.get("description", "")
                })
            except:
                continue
        
        return {
            "success": True,
            "templates": templates
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模板失败: {str(e)}")

@router.post("/templates")
async def save_annotation_template(
    name: str = Form(...),
    description: str = Form(""),
    annotations: str = Form(...)
):
    """
    保存标注模板
    """
    try:
        # 解析标注数据
        try:
            annotation_data = json.loads(annotations)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="标注数据格式错误")
        
        # 添加模板信息
        template_data = {
            "name": name,
            "description": description,
            "created_at": datetime.now().isoformat(),
            "regions": annotation_data.get("regions", [])
        }
        
        # 保存模板文件
        template_file = TEMPLATES_DIR / f"{name}.json"
        with open(template_file, 'w', encoding='utf-8') as f:
            json.dump(template_data, f, ensure_ascii=False, indent=2)
        
        return {
            "success": True,
            "message": "模板保存成功",
            "template_name": name,
            "regions_count": len(template_data["regions"])
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"保存模板失败: {str(e)}")

@router.get("/status/{task_id}")
async def get_task_status(task_id: str) -> OCRResponse:
    """
    获取任务处理状态
    """
    if task_id not in processing_tasks:
        raise HTTPException(
            status_code=404,
            detail="任务不存在"
        )
    
    task = processing_tasks[task_id]
    
    return OCRResponse(
        task_id=task_id,
        status=task.status,
        message="任务状态获取成功",
        results=task.results if task.status == "completed" else None,
        error=task.error if task.status == "failed" else None
    )

@router.get("/result/{task_id}")
async def get_task_result(task_id: str) -> OCRResponse:
    """
    获取任务处理结果
    """
    result_file = PROCESSED_DIR / f"{task_id}.json"
    
    if not result_file.exists():
        raise HTTPException(
            status_code=404,
            detail="结果文件不存在"
        )
    
    try:
        with result_file.open("r", encoding="utf-8") as f:
            results = json.load(f)
        
        return OCRResponse(
            task_id=task_id,
            status="completed",
            message="结果获取成功",
            results=results
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"结果读取失败：{str(e)}"
        )

@router.delete("/task/{task_id}")
async def delete_task(task_id: str):
    """
    删除任务及其相关文件
    """
    if task_id not in processing_tasks:
        raise HTTPException(
            status_code=404,
            detail="任务不存在"
        )
    
    # 删除任务状态
    del processing_tasks[task_id]
    
    # 删除相关文件
    for file_pattern in [f"{task_id}.*"]:
        for file_path in UPLOAD_DIR.glob(file_pattern):
            try:
                file_path.unlink()
            except:
                pass
        for file_path in PROCESSED_DIR.glob(file_pattern):
            try:
                file_path.unlink()
            except:
                pass
    
    return {"message": "任务删除成功"}

@router.get("/health")
async def health_check():
    """
    OCR服务健康检查
    """
    try:
        # 检查OCR处理器状态
        status = {
            "status": "healthy",
            "ocr_processor": "available",
            "annotation_ocr": "checking...",
            "templates_dir": str(TEMPLATES_DIR),
            "upload_dir": str(UPLOAD_DIR),
            "processed_dir": str(PROCESSED_DIR)
        }
        
        # 检查标注OCR是否可用
        try:
            from src.backend.services.ocr.annotation_guided_ocr import AnnotationGuidedOCR
            status["annotation_ocr"] = "available"
        except ImportError:
            status["annotation_ocr"] = "unavailable"
        
        return status
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }

async def save_uploaded_file(file: UploadFile) -> Path:
    """
    保存上传的文件到临时目录
    """
    # 生成唯一文件名
    file_extension = Path(file.filename).suffix if file.filename else ""
    temp_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = UPLOAD_DIR / temp_filename
    
    # 保存文件
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    return file_path 

class LearningFeedbackRequest(BaseModel):
    """学习反馈请求模型"""
    results: List[Dict]
    corrected_texts: Dict[str, str]
    image_path: str
    user_id: Optional[str] = None

class LearningReportResponse(BaseModel):
    """学习报告响应模型"""
    total_samples: int
    accuracy_improvement: str
    confidence_improvement: str
    region_performance: Dict[str, str]
    common_errors: List[Dict]

@router.post("/enhanced-annotated")
async def process_enhanced_annotated_ocr(
    file: UploadFile = File(...),
    annotations: str = Form(...)
) -> AnnotatedOCRResponse:
    """
    使用增强版OCR处理器进行基于标注的OCR处理（集成学习系统）
    """
    print(f"🚀 [API] 收到增强标注OCR请求")
    print(f"   文件名: {file.filename}")
    print(f"   文件类型: {file.content_type}")
    
    try:
        # 验证文件类型
        if not file.content_type.startswith('image/'):
            print(f"❌ 文件类型验证失败: {file.content_type}")
            raise HTTPException(status_code=400, detail="只支持图像文件")
        
        # 解析标注数据
        try:
            annotation_data = json.loads(annotations)
            print(f"✅ 标注数据解析成功")
        except json.JSONDecodeError as e:
            print(f"❌ 标注数据解析失败: {e}")
            raise HTTPException(status_code=400, detail="标注数据格式错误")
        
        # 保存上传的文件
        file_path = await save_uploaded_file(file)
        print(f"✅ 文件保存成功: {file_path}")
        
        # 创建临时标注文件
        annotation_file_path = file_path.with_suffix('.json')
        
        # 处理标注数据格式
        if isinstance(annotation_data, list):
            regions = annotation_data
        elif isinstance(annotation_data, dict) and 'regions' in annotation_data:
            regions = annotation_data['regions']
        else:
            raise HTTPException(status_code=400, detail="标注数据格式不正确")
        
        # 保存标注文件
        annotation_content = {
            "image_path": str(file_path),
            "regions": regions,
            "created_at": datetime.now().isoformat()
        }
        
        with open(annotation_file_path, 'w', encoding='utf-8') as f:
            json.dump(annotation_content, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 标注文件创建成功: {annotation_file_path}")
        
        # 使用增强版标注OCR处理器
        print(f"🚀 使用增强版OCR处理器...")
        results = get_enhanced_ocr_processor().process_with_annotations_and_learning(
            str(file_path),
            str(annotation_file_path),
            create_annotation=False,
            collect_training_data=True
        )
        
        if not results:
            print(f"❌ OCR处理失败")
            raise HTTPException(status_code=500, detail="OCR处理失败")
        
        print(f"✅ OCR处理成功，共 {len(results)} 个区域")
        
        # 转换结果格式
        formatted_results = []
        for result in results:
            formatted_results.append({
                "region_id": result.region_id,
                "region_type": result.region_type,
                "order": result.order,
                "bbox": result.bbox,
                "text": result.text,
                "confidence": float(result.confidence),
                "raw_ocr_results": result.raw_ocr_results
            })
        
        # 清理临时文件
        try:
            file_path.unlink()
            annotation_file_path.unlink()
        except Exception as e:
            print(f"⚠️ 清理临时文件失败: {e}")
        
        return AnnotatedOCRResponse(
            success=True,
            total_regions=len(results),
            results=formatted_results
        )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")

@router.post("/learning/feedback")
async def submit_learning_feedback(request: LearningFeedbackRequest):
    """
    提交学习反馈，用于训练OCR系统
    """
    try:
        logger.info(f"📚 收到学习反馈，修正文本数量: {len(request.corrected_texts)}")
        
        # 使用反馈收集服务收集反馈
        result = feedback_service.collect_feedback(
            request.results,
            request.corrected_texts,
            request.image_path,
            request.user_id
        )
        
        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["message"])
        
        # 收集反馈并学习
        samples = get_enhanced_ocr_processor().collect_feedback_and_learn(
            request.results,
            request.corrected_texts,
            request.image_path
        )
        
        logger.info(f"✅ 学习完成，收集了 {len(samples)} 个训练样本")
        
        return {
            "success": True,
            "message": result["message"],
            "samples_collected": result["samples_collected"]
        }
        
    except Exception as e:
        logger.error(f"❌ 学习反馈处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"学习反馈处理失败: {str(e)}")

@router.get("/learning/statistics")
async def get_learning_statistics(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
):
    """
    获取学习系统统计信息
    """
    try:
        # 转换日期字符串为datetime对象
        start = datetime.fromisoformat(start_date) if start_date else None
        end = datetime.fromisoformat(end_date) if end_date else None
        
        # 获取统计信息
        stats = feedback_service.get_feedback_statistics(start, end)
        
        return {
            "success": True,
            "statistics": stats
        }
        
    except Exception as e:
        logger.error(f"❌ 获取学习统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取学习统计失败: {str(e)}")

@router.get("/learning/region-performance/{region_type}")
async def get_region_performance(region_type: str):
    """
    获取特定区域类型的性能数据
    """
    try:
        performance = feedback_service.get_region_performance(region_type)
        
        return {
            "success": True,
            "performance": performance
        }
        
    except Exception as e:
        logger.error(f"❌ 获取区域性能数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取区域性能数据失败: {str(e)}")

@router.get("/learning/report")
async def get_learning_report() -> LearningReportResponse:
    """
    获取OCR学习系统报告
    """
    try:
        print(f"📊 生成学习报告...")
        
        report = get_enhanced_ocr_processor().get_learning_report()
        
        return LearningReportResponse(
            total_samples=report["total_samples"],
            accuracy_improvement=report["accuracy_improvement"],
            confidence_improvement=report["confidence_improvement"],
            region_performance=report["region_performance"],
            common_errors=report["common_errors"]
        )
        
    except Exception as e:
        print(f"❌ 生成学习报告失败: {e}")
        raise HTTPException(status_code=500, detail=f"生成学习报告失败: {str(e)}")

@router.get("/learning/region-analysis/{region_type}")
async def get_region_analysis(region_type: str):
    """
    获取特定区域类型的性能分析
    """
    try:
        print(f"📈 分析区域类型: {region_type}")
        
        analysis = get_enhanced_ocr_processor().analyze_region_performance(region_type)
        
        return {
            "success": True,
            "analysis": analysis
        }
        
    except Exception as e:
        print(f"❌ 区域分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"区域分析失败: {str(e)}")

@router.post("/learning/export")
async def export_learning_data(output_path: str = "data/learning_export.json"):
    """
    导出学习数据
    """
    try:
        print(f"📤 导出学习数据到: {output_path}")
        
        get_enhanced_ocr_processor().export_learning_data(output_path)
        
        return {
            "success": True,
            "message": f"学习数据已导出到: {output_path}",
            "export_path": output_path
        }
        
    except Exception as e:
        print(f"❌ 导出学习数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出学习数据失败: {str(e)}")

class BatchLearningRequest(BaseModel):
    """批量学习请求模型"""
    data_directory: str
    max_workers: int = 4
    batch_size: int = 10
    enable_preprocessing_optimization: bool = True

class BatchLearningResponse(BaseModel):
    """批量学习响应模型"""
    task_id: str
    status: str
    message: str
    stats: Optional[Dict] = None

class MigrationRequest(BaseModel):
    """数据迁移请求模型"""
    csv_file_path: str

@router.post("/batch-learning/start")
async def start_batch_learning(
    background_tasks: BackgroundTasks,
    request: BatchLearningRequest
) -> BatchLearningResponse:
    """
    启动批量学习任务
    """
    task_id = str(uuid.uuid4())
    
    # 验证数据目录
    if not os.path.exists(request.data_directory):
        raise HTTPException(
            status_code=400,
            detail=f"数据目录不存在: {request.data_directory}"
        )
    
    # 创建任务记录
    batch_learning_tasks[task_id] = {
        'status': 'pending',
        'start_time': datetime.now(),
        'stats': None,
        'error': None
    }
    
    # 在后台启动批量学习
    background_tasks.add_task(
        run_batch_learning,
        task_id,
        request
    )
    
    return BatchLearningResponse(
        task_id=task_id,
        status="pending",
        message="批量学习任务已启动"
    )

async def run_batch_learning(task_id: str, request: BatchLearningRequest):
    """运行批量学习任务"""
    try:
        # 更新任务状态
        batch_learning_tasks[task_id]['status'] = 'running'
        
        # 创建配置
        config = BatchLearningConfig(
            max_workers=request.max_workers,
            batch_size=request.batch_size,
            enable_preprocessing_optimization=request.enable_preprocessing_optimization,
            output_dir=f"data/batch_learning/{task_id}"
        )
        
        # 创建处理器并运行
        processor = BatchLearningProcessor(config)
        stats = await processor.process_historical_data(request.data_directory)
        
        # 更新任务状态
        batch_learning_tasks[task_id]['status'] = 'completed'
        batch_learning_tasks[task_id]['stats'] = stats
        batch_learning_tasks[task_id]['end_time'] = datetime.now()
        
    except Exception as e:
        # 更新任务状态为错误
        batch_learning_tasks[task_id]['status'] = 'failed'
        batch_learning_tasks[task_id]['error'] = str(e)

@router.get("/batch-learning/status/{task_id}")
async def get_batch_learning_status(task_id: str) -> BatchLearningResponse:
    """
    获取批量学习任务状态
    """
    if task_id not in batch_learning_tasks:
        raise HTTPException(
            status_code=404,
            detail="任务不存在"
        )
    
    task = batch_learning_tasks[task_id]
    
    return BatchLearningResponse(
        task_id=task_id,
        status=task['status'],
        message=f"任务状态: {task['status']}",
        stats=task.get('stats')
    )

@router.get("/batch-learning/tasks")
async def list_batch_learning_tasks():
    """
    列出所有批量学习任务
    """
    tasks = []
    for task_id, task_data in batch_learning_tasks.items():
        tasks.append({
            'task_id': task_id,
            'status': task_data['status'],
            'start_time': task_data['start_time'].isoformat() if task_data['start_time'] else None,
            'end_time': task_data.get('end_time').isoformat() if task_data.get('end_time') else None,
            'stats_summary': {
                'total_files': task_data.get('stats', {}).get('total_files', 0),
                'processed_files': task_data.get('stats', {}).get('processed_files', 0),
                'total_samples': task_data.get('stats', {}).get('total_samples', 0)
            } if task_data.get('stats') else None
        })
    
    return {'tasks': tasks}

@router.post("/batch-learning/migrate-csv")
async def migrate_from_csv(
    background_tasks: BackgroundTasks,
    request: MigrationRequest
):
    """
    从CSV文件迁移历史数据
    """
    task_id = str(uuid.uuid4())
    
    # 验证CSV文件
    if not os.path.exists(request.csv_file_path):
        raise HTTPException(
            status_code=400,
            detail=f"CSV文件不存在: {request.csv_file_path}"
        )
    
    # 创建任务记录
    batch_learning_tasks[task_id] = {
        'status': 'pending',
        'start_time': datetime.now(),
        'type': 'migration',
        'stats': None,
        'error': None
    }
    
    # 在后台运行迁移
    background_tasks.add_task(
        run_csv_migration,
        task_id,
        request.csv_file_path
    )
    
    return {
        'task_id': task_id,
        'status': 'pending',
        'message': 'CSV数据迁移任务已启动'
    }

async def run_csv_migration(task_id: str, csv_file_path: str):
    """运行CSV迁移任务"""
    try:
        # 更新任务状态
        batch_learning_tasks[task_id]['status'] = 'running'
        
        # 创建迁移器并运行
        migrator = HistoricalDataMigrator()
        result = await migrator.migrate_from_csv(csv_file_path)
        
        # 更新任务状态
        batch_learning_tasks[task_id]['status'] = 'completed'
        batch_learning_tasks[task_id]['stats'] = result
        batch_learning_tasks[task_id]['end_time'] = datetime.now()
        
    except Exception as e:
        # 更新任务状态为错误
        batch_learning_tasks[task_id]['status'] = 'failed'
        batch_learning_tasks[task_id]['error'] = str(e)

@router.delete("/batch-learning/task/{task_id}")
async def delete_batch_learning_task(task_id: str):
    """
    删除批量学习任务记录
    """
    if task_id not in batch_learning_tasks:
        raise HTTPException(
            status_code=404,
            detail="任务不存在"
        )
    
    del batch_learning_tasks[task_id]
    
    return {'message': '任务已删除'}

@router.post("/learning/optimize-preprocessing")
async def optimize_preprocessing_parameters():
    """
    优化预处理参数
    """
    try:
        # 使用增强OCR处理器优化参数
        result = get_enhanced_ocr_processor().optimize_preprocessing_parameters()
        
        return {
            'success': True,
            'message': '预处理参数优化完成',
            'optimized_parameters': result
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"预处理参数优化失败: {str(e)}"
        )

@router.get("/learning/preprocessing-analysis")
async def get_preprocessing_analysis():
    """
    获取预处理参数分析
    """
    try:
        analysis = get_enhanced_ocr_processor().learning_system.analyze_preprocessing_effectiveness()
        
        return {
            'success': True,
            'analysis': analysis
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取预处理分析失败: {str(e)}"
        )

@router.post("/batch")
@cache(prefix="ocr:batch", ttl=7200)
async def process_batch(
    files: List[UploadFile] = File(...),
    options: Dict[str, Any] = {}
) -> Dict[str, Any]:
    """
    批量处理多个图片的OCR请求
    
    Args:
        files: 上传的图片文件列表
        options: OCR处理选项
        
    Returns:
        Dict[str, Any]: OCR结果列表
    """
    try:
        results = []
        
        # 处理每个文件
        for file in files:
            content = await file.read()
            result = await get_ocr_processor().process_image(
                content,
                options
            )
            results.append({
                "filename": file.filename,
                "result": result
            })
        
        return {
            "success": True,
            "data": results
        }
        
    except Exception as e:
        logger.error(f"Batch OCR processing failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Batch OCR processing failed: {str(e)}"
        )

@router.post("/update-model")
@invalidate_cache(prefix="ocr")
async def update_model(
    model_path: str
) -> Dict[str, Any]:
    """
    更新OCR模型
    
    Args:
        model_path: 新模型的路径
        
    Returns:
        Dict[str, Any]: 更新结果
    """
    try:
        # 更新模型
        await get_ocr_processor().update_model(model_path)
        
        return {
            "success": True,
            "message": "Model updated successfully"
        }
        
    except Exception as e:
        logger.error(f"Model update failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Model update failed: {str(e)}"
        ) 
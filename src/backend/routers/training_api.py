"""
模型训练API路由
"""

from typing import Dict, List, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from datetime import datetime
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.backend.logger import get_logger
from src.backend.services.training.retraining import ModelRetrainingService

logger = get_logger(__name__)
router = APIRouter(prefix="/api/training", tags=["training"])

# 初始化服务
training_service = ModelRetrainingService()

@router.post("/retrain")
async def start_retraining(background_tasks: BackgroundTasks):
    """
    启动模型重训练
    """
    try:
        background_tasks.add_task(training_service.start_retraining)
        return {"message": "Model retraining started"}
    except Exception as e:
        logger.error(f"Error starting retraining: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error starting retraining: {str(e)}"
        )

@router.get("/status")
async def get_training_status():
    """
    获取训练状态
    """
    try:
        status = training_service.get_training_status()
        return status
    except Exception as e:
        logger.error(f"Error getting training status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting training status: {str(e)}"
        )

@router.post("/stop")
async def stop_training():
    """
    停止训练
    """
    try:
        training_service.stop_training()
        return {"message": "Training stopped"}
    except Exception as e:
        logger.error(f"Error stopping training: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error stopping training: {str(e)}"
        ) 
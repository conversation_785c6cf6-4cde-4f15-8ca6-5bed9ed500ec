"""
文档管理API路由
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, BackgroundTasks
from pathlib import Path
import uuid
import shutil
from typing import List, Dict, Any
import json
from datetime import datetime
import os
import asyncio
from src.backend.database.database import get_mongodb
from bson import ObjectId

router = APIRouter(tags=["Documents"])

# 数据存储目录
UPLOAD_DIR = Path("data/uploads")
PROCESSED_DIR = Path("data/processed")
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)
PROCESSED_DIR.mkdir(parents=True, exist_ok=True)

# 数据库连接
db = None

async def get_db():
    """获取数据库连接"""
    global db
    if db is None:
        db = await get_mongodb()
    return db

def cleanup_file(file_path: Path):
    """清理临时文件"""
    try:
        if file_path.exists():
            file_path.unlink()
    except Exception as e:
        print(f"清理文件失败: {file_path}, 错误: {e}")

@router.get("/documents")
async def get_documents():
    """
    获取文档列表
    """
    try:
        database = await get_db()
        # 使用 ocr_files 集合作为主要数据源
        collection = database["ocr_files"]

        # 获取所有文档，按上传时间倒序排列
        cursor = collection.find({}).sort("upload_time", -1)
        documents = []

        async for doc in cursor:
            # 转换ObjectId为字符串
            doc["_id"] = str(doc["_id"])

            # 转换datetime为字符串
            if "upload_time" in doc and hasattr(doc["upload_time"], "isoformat"):
                doc["upload_time"] = doc["upload_time"].isoformat()
            if "processed_time" in doc and hasattr(doc["processed_time"], "isoformat"):
                doc["processed_time"] = doc["processed_time"].isoformat()

            # 统一字段名称，确保前端兼容
            if "filename" in doc:
                doc["original_filename"] = doc.get("original_filename", doc["filename"])

            # 统一状态字段
            if doc.get("status") == "processed":
                doc["status"] = "completed"

            documents.append(doc)

        return {
            "success": True,
            "data": documents,
            "total": len(documents)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文档列表失败: {str(e)}")

@router.post("/documents/upload")
async def upload_document(
    file: UploadFile = File(...),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """
    上传文档
    """
    try:
        # 验证文件类型
        allowed_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.pdf', '.heic', '.heif'}
        file_extension = Path(file.filename).suffix.lower()
        
        if file_extension not in allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的文件类型: {file_extension}"
            )
        
        # 生成文件ID和保存路径
        file_id = str(uuid.uuid4())
        file_path = UPLOAD_DIR / f"{file_id}{file_extension}"
        
        # 保存文件
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 获取文件大小
        file_size = file_path.stat().st_size
        
        # 创建文档记录
        document = {
            "file_id": file_id,
            "original_filename": file.filename,
            "file_path": str(file_path),
            "file_size": file_size,
            "file_type": file.content_type,
            "status": "pending",
            "upload_time": datetime.now(),
            "metadata": {
                "format": file_extension.lstrip('.'),
                "size": file_size
            }
        }

        # 存储文档记录到数据库 (使用 ocr_files 集合)
        database = await get_db()
        collection = database["ocr_files"]
        result = await collection.insert_one(document)

        # 添加_id到返回的文档中
        document["_id"] = str(result.inserted_id)
        document["upload_time"] = document["upload_time"].isoformat()
        
        # 在后台处理OCR（这里简化处理）
        background_tasks.add_task(process_document_ocr, file_id, file_path)
        
        return {
            "success": True,
            "data": document,
            "message": "文档上传成功"
        }
        
    except Exception as e:
        # 清理文件
        if 'file_path' in locals() and file_path.exists():
            file_path.unlink()
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

async def process_document_ocr(file_id: str, file_path: Path):
    """
    后台处理文档OCR
    """
    try:
        database = await get_db()
        collection = database["ocr_files"]

        # 更新状态为处理中
        await collection.update_one(
            {"file_id": file_id},
            {"$set": {"status": "processing"}}
        )

        # 调用OCR处理
        try:
            # 导入OCR处理器
            from src.backend.services.ocr.processor import OCRProcessor

            # 创建OCR处理器实例
            ocr_processor = OCRProcessor()

            # 调用结构化处理
            result = ocr_processor.process_image_structured(str(file_path))

            # 保存OCR结果
            await collection.update_one(
                {"file_id": file_id},
                {"$set": {
                    "ocr_result": result,
                    "status": "completed",
                    "processed_time": datetime.now()
                }}
            )

        except Exception as ocr_error:
            print(f"OCR处理失败: {ocr_error}")
            # 如果OCR失败，仍然标记为完成，但没有OCR结果
            await collection.update_one(
                {"file_id": file_id},
                {"$set": {
                    "status": "completed",
                    "processed_time": datetime.now(),
                    "ocr_error": str(ocr_error)
                }}
            )

    except Exception as e:
        print(f"文档处理失败: {e}")
        # 更新状态为错误
        database = await get_db()
        collection = database["ocr_files"]
        await collection.update_one(
            {"file_id": file_id},
            {"$set": {
                "status": "error",
                "error": str(e)
            }}
        )

@router.get("/documents/{file_id}")
async def get_document(file_id: str):
    """
    获取单个文档信息
    """
    try:
        database = await get_db()
        # 首先尝试从 ocr_files 集合查询
        collection = database["ocr_files"]

        # 尝试通过file_id查询，如果失败则尝试通过ObjectId查询
        document = await collection.find_one({"file_id": file_id})
        if not document:
            try:
                # 尝试作为ObjectId查询
                document = await collection.find_one({"_id": ObjectId(file_id)})
            except:
                pass

        # 如果在 ocr_files 中没找到，尝试 documents 集合
        if not document:
            collection = database["documents"]
            document = await collection.find_one({"file_id": file_id})
            if not document:
                try:
                    document = await collection.find_one({"_id": ObjectId(file_id)})
                except:
                    pass

        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")

        # 转换ObjectId为字符串
        document["_id"] = str(document["_id"])

        # 转换datetime为字符串
        if "upload_time" in document and hasattr(document["upload_time"], "isoformat"):
            document["upload_time"] = document["upload_time"].isoformat()
        if "processed_time" in document and hasattr(document["processed_time"], "isoformat"):
            document["processed_time"] = document["processed_time"].isoformat()

        return {
            "success": True,
            "data": document
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文档失败: {str(e)}")

@router.delete("/documents/{file_id}")
async def delete_document(file_id: str):
    """
    删除文档
    """
    try:
        database = await get_db()
        # 首先尝试从 ocr_files 集合查询
        collection = database["ocr_files"]

        # 查找文档
        document = await collection.find_one({"file_id": file_id})
        if not document:
            # 如果在 ocr_files 中没找到，尝试 documents 集合
            collection = database["documents"]
            document = await collection.find_one({"file_id": file_id})

        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")

        # 删除文件
        file_path = Path(document["file_path"])
        if file_path.exists():
            file_path.unlink()

        # 删除数据库记录
        await collection.delete_one({"file_id": file_id})

        return {
            "success": True,
            "message": "文档删除成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

@router.get("/documents/{file_id}/ocr")
async def get_document_ocr(file_id: str):
    """
    获取文档的OCR识别结果
    """
    try:
        database = await get_db()
        # 首先尝试从 ocr_files 集合查询
        collection = database["ocr_files"]

        # 尝试通过file_id查询，如果失败则尝试通过ObjectId查询
        document = await collection.find_one({"file_id": file_id})
        if not document:
            try:
                # 尝试作为ObjectId查询
                document = await collection.find_one({"_id": ObjectId(file_id)})
            except:
                pass

        # 如果在 ocr_files 中没找到，尝试 documents 集合
        if not document:
            collection = database["documents"]
            document = await collection.find_one({"file_id": file_id})
            if not document:
                try:
                    document = await collection.find_one({"_id": ObjectId(file_id)})
                except:
                    pass

        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")

        # 检查是否有真实的OCR结果
        if "ocr_result" in document or "ocr_results" in document:
            # 转换datetime为字符串
            processed_time = document.get("processed_time")
            if processed_time and hasattr(processed_time, "isoformat"):
                processed_time = processed_time.isoformat()

            # 处理不同的OCR结果格式
            ocr_result = document.get("ocr_result") or document.get("ocr_results")

            # 如果是 ocr_files 集合的格式，需要转换
            if "ocr_results" in document and "models" in document["ocr_results"]:
                models = document["ocr_results"]["models"]
                # 优先使用 PP-OCRv5 的结果
                if "pp_ocrv5" in models and models["pp_ocrv5"].get("markdown_text"):
                    ocr_result = {
                        "markdown_text": models["pp_ocrv5"]["markdown_text"],
                        "total_regions": models["pp_ocrv5"].get("total_regions", 0),
                        "processing_method": "PP-OCRv5",
                        "image_size": document["ocr_results"].get("image_size", {})
                    }
                elif "pp_structurev3" in models and models["pp_structurev3"].get("markdown_text"):
                    ocr_result = {
                        "markdown_text": models["pp_structurev3"]["markdown_text"],
                        "total_regions": models["pp_structurev3"].get("total_regions", 0),
                        "processing_method": "PP-StructureV3",
                        "image_size": document["ocr_results"].get("image_size", {})
                    }
                else:
                    # 使用 ocr_text 字段
                    ocr_result = {
                        "markdown_text": document.get("ocr_text", ""),
                        "total_regions": 0,
                        "processing_method": "Unknown",
                        "image_size": document["ocr_results"].get("image_size", {})
                    }

            # 返回真实的OCR结果
            return {
                "success": True,
                "data": {
                    "file_id": file_id,
                    "original_filename": document.get("original_filename", document.get("filename", "")),
                    "ocr_result": ocr_result,
                    "processed_time": processed_time,
                    "processing_method": ocr_result.get("processing_method", "PP-StructureV3") if isinstance(ocr_result, dict) else "PP-StructureV3"
                }
            }
        elif "ocr_error" in document:
            # 返回OCR错误信息
            return {
                "success": False,
                "error": document["ocr_error"],
                "message": "OCR处理失败"
            }
        else:
            # 返回模拟OCR结果
            ocr_result = {
                "success": True,
                "data": {
                    "file_id": file_id,
                    "original_filename": document["original_filename"],
                    "ocr_text": f"这是文档 {document['original_filename']} 的OCR识别结果。\n\n包含文本内容和结构化信息。",
                    "regions": [
                        {
                            "type": "title",
                            "text": "文档标题",
                            "bbox": [100, 50, 400, 100],
                            "confidence": 0.95
                        },
                        {
                            "type": "text",
                            "text": "这是正文内容，包含了文档的主要信息。",
                            "bbox": [100, 120, 500, 200],
                            "confidence": 0.92
                        }
                    ],
                    "processed_time": datetime.now().isoformat(),
                    "processing_method": "PP-StructureV3 (模拟)"
                }
            }
            return ocr_result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取OCR结果失败: {str(e)}")

@router.post("/documents/{file_id}/regenerate")
async def regenerate_document(file_id: str, background_tasks: BackgroundTasks):
    """
    重新生成文档识别结果
    """
    try:
        database = await get_db()
        # 首先尝试从 ocr_files 集合查询
        collection = database["ocr_files"]

        document = await collection.find_one({"file_id": file_id})
        if not document:
            # 如果在 ocr_files 中没找到，尝试 documents 集合
            collection = database["documents"]
            document = await collection.find_one({"file_id": file_id})

        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")

        file_path = Path(document["file_path"])

        if not file_path.exists():
            raise HTTPException(status_code=404, detail="文档文件不存在")

        # 重置状态
        await collection.update_one(
            {"file_id": file_id},
            {"$set": {"status": "pending"}}
        )

        # 在后台重新处理
        background_tasks.add_task(process_document_ocr, file_id, file_path)

        return {
            "success": True,
            "message": "已开始重新生成识别结果"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重新生成失败: {str(e)}")

# 添加一些示例数据
async def init_sample_data():
    """初始化一些示例数据"""
    try:
        database = await get_db()
        collection = database["documents"]

        # 检查是否已有示例数据
        existing_count = await collection.count_documents({"file_id": {"$in": ["sample-1", "sample-2"]}})
        if existing_count > 0:
            return  # 已有示例数据，不重复添加

        sample_docs = [
            {
                "file_id": "sample-1",
                "original_filename": "示例文档1.png",
                "file_path": "data/uploads/sample-1.png",
                "file_size": 1024000,
                "file_type": "image/png",
                "status": "completed",
                "upload_time": datetime(2025, 6, 14, 10, 0, 0),
                "metadata": {
                    "format": "png",
                    "size": 1024000
                }
            },
            {
                "file_id": "sample-2",
                "original_filename": "示例文档2.jpg",
                "file_path": "data/uploads/sample-2.jpg",
                "file_size": 2048000,
                "file_type": "image/jpeg",
                "status": "completed",
                "upload_time": datetime(2025, 6, 14, 11, 0, 0),
                "metadata": {
                    "format": "jpg",
                    "size": 2048000
                }
            }
        ]

        # 插入示例数据
        await collection.insert_many(sample_docs)
        print("示例数据初始化完成")

    except Exception as e:
        print(f"初始化示例数据失败: {e}")

# 在应用启动时初始化示例数据
import asyncio
asyncio.create_task(init_sample_data())

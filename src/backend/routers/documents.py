"""
文档管理API路由
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, BackgroundTasks
from pathlib import Path
import uuid
import shutil
from typing import List, Dict, Any
import json
from datetime import datetime
import os
import asyncio

router = APIRouter(tags=["Documents"])

# 数据存储目录
UPLOAD_DIR = Path("data/uploads")
PROCESSED_DIR = Path("data/processed")
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)
PROCESSED_DIR.mkdir(parents=True, exist_ok=True)

# 简单的内存存储（生产环境应该使用数据库）
documents_store: Dict[str, Dict[str, Any]] = {}

def cleanup_file(file_path: Path):
    """清理临时文件"""
    try:
        if file_path.exists():
            file_path.unlink()
    except Exception as e:
        print(f"清理文件失败: {file_path}, 错误: {e}")

@router.get("/documents")
async def get_documents():
    """
    获取文档列表
    """
    try:
        # 返回所有文档
        documents = list(documents_store.values())
        
        # 按上传时间倒序排列
        documents.sort(key=lambda x: x.get('upload_time', ''), reverse=True)
        
        return {
            "success": True,
            "data": documents,
            "total": len(documents)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文档列表失败: {str(e)}")

@router.post("/documents/upload")
async def upload_document(
    file: UploadFile = File(...),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """
    上传文档
    """
    try:
        # 验证文件类型
        allowed_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.pdf', '.heic', '.heif'}
        file_extension = Path(file.filename).suffix.lower()
        
        if file_extension not in allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的文件类型: {file_extension}"
            )
        
        # 生成文件ID和保存路径
        file_id = str(uuid.uuid4())
        file_path = UPLOAD_DIR / f"{file_id}{file_extension}"
        
        # 保存文件
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 获取文件大小
        file_size = file_path.stat().st_size
        
        # 创建文档记录
        document = {
            "_id": file_id,
            "file_id": file_id,
            "original_filename": file.filename,
            "file_path": str(file_path),
            "file_size": file_size,
            "file_type": file.content_type,
            "status": "pending",
            "upload_time": datetime.now().isoformat(),
            "metadata": {
                "format": file_extension.lstrip('.'),
                "size": file_size
            }
        }
        
        # 存储文档记录
        documents_store[file_id] = document
        
        # 在后台处理OCR（这里简化处理）
        background_tasks.add_task(process_document_ocr, file_id, file_path)
        
        return {
            "success": True,
            "data": document,
            "message": "文档上传成功"
        }
        
    except Exception as e:
        # 清理文件
        if 'file_path' in locals() and file_path.exists():
            file_path.unlink()
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

async def process_document_ocr(file_id: str, file_path: Path):
    """
    后台处理文档OCR
    """
    try:
        # 更新状态为处理中
        if file_id in documents_store:
            documents_store[file_id]["status"] = "processing"

        # 调用OCR处理
        try:
            # 导入OCR处理器
            from src.backend.services.ocr.processor import OCRProcessor

            # 创建OCR处理器实例
            ocr_processor = OCRProcessor()

            # 调用结构化处理
            result = ocr_processor.process_image_structured(str(file_path))

            # 保存OCR结果
            if file_id in documents_store:
                documents_store[file_id]["ocr_result"] = result
                documents_store[file_id]["status"] = "completed"
                documents_store[file_id]["processed_time"] = datetime.now().isoformat()

        except Exception as ocr_error:
            print(f"OCR处理失败: {ocr_error}")
            # 如果OCR失败，仍然标记为完成，但没有OCR结果
            if file_id in documents_store:
                documents_store[file_id]["status"] = "completed"
                documents_store[file_id]["processed_time"] = datetime.now().isoformat()
                documents_store[file_id]["ocr_error"] = str(ocr_error)

    except Exception as e:
        print(f"文档处理失败: {e}")
        # 更新状态为错误
        if file_id in documents_store:
            documents_store[file_id]["status"] = "error"
            documents_store[file_id]["error"] = str(e)

@router.get("/documents/{file_id}")
async def get_document(file_id: str):
    """
    获取单个文档信息
    """
    if file_id not in documents_store:
        raise HTTPException(status_code=404, detail="文档不存在")
    
    return {
        "success": True,
        "data": documents_store[file_id]
    }

@router.delete("/documents/{file_id}")
async def delete_document(file_id: str):
    """
    删除文档
    """
    if file_id not in documents_store:
        raise HTTPException(status_code=404, detail="文档不存在")
    
    try:
        # 删除文件
        document = documents_store[file_id]
        file_path = Path(document["file_path"])
        if file_path.exists():
            file_path.unlink()
        
        # 删除记录
        del documents_store[file_id]
        
        return {
            "success": True,
            "message": "文档删除成功"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

@router.get("/documents/{file_id}/ocr")
async def get_document_ocr(file_id: str):
    """
    获取文档的OCR识别结果
    """
    if file_id not in documents_store:
        raise HTTPException(status_code=404, detail="文档不存在")

    try:
        document = documents_store[file_id]

        # 检查是否有真实的OCR结果
        if "ocr_result" in document:
            # 返回真实的OCR结果
            return {
                "success": True,
                "data": {
                    "file_id": file_id,
                    "original_filename": document["original_filename"],
                    "ocr_result": document["ocr_result"],
                    "processed_time": document.get("processed_time"),
                    "processing_method": "PP-StructureV3"
                }
            }
        elif "ocr_error" in document:
            # 返回OCR错误信息
            return {
                "success": False,
                "error": document["ocr_error"],
                "message": "OCR处理失败"
            }
        else:
            # 返回模拟OCR结果
            ocr_result = {
                "success": True,
                "data": {
                    "file_id": file_id,
                    "original_filename": document["original_filename"],
                    "ocr_text": f"这是文档 {document['original_filename']} 的OCR识别结果。\n\n包含文本内容和结构化信息。",
                    "regions": [
                        {
                            "type": "title",
                            "text": "文档标题",
                            "bbox": [100, 50, 400, 100],
                            "confidence": 0.95
                        },
                        {
                            "type": "text",
                            "text": "这是正文内容，包含了文档的主要信息。",
                            "bbox": [100, 120, 500, 200],
                            "confidence": 0.92
                        }
                    ],
                    "processed_time": datetime.now().isoformat(),
                    "processing_method": "PP-StructureV3 (模拟)"
                }
            }
            return ocr_result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取OCR结果失败: {str(e)}")

@router.post("/documents/{file_id}/regenerate")
async def regenerate_document(file_id: str, background_tasks: BackgroundTasks):
    """
    重新生成文档识别结果
    """
    if file_id not in documents_store:
        raise HTTPException(status_code=404, detail="文档不存在")

    try:
        document = documents_store[file_id]
        file_path = Path(document["file_path"])

        if not file_path.exists():
            raise HTTPException(status_code=404, detail="文档文件不存在")

        # 重置状态
        documents_store[file_id]["status"] = "pending"

        # 在后台重新处理
        background_tasks.add_task(process_document_ocr, file_id, file_path)

        return {
            "success": True,
            "message": "已开始重新生成识别结果"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重新生成失败: {str(e)}")

# 添加一些示例数据
def init_sample_data():
    """初始化一些示例数据"""
    sample_docs = [
        {
            "_id": "sample-1",
            "file_id": "sample-1", 
            "original_filename": "示例文档1.png",
            "file_path": "data/uploads/sample-1.png",
            "file_size": 1024000,
            "file_type": "image/png",
            "status": "completed",
            "upload_time": "2025-06-14T10:00:00",
            "metadata": {
                "format": "png",
                "size": 1024000
            }
        },
        {
            "_id": "sample-2", 
            "file_id": "sample-2",
            "original_filename": "示例文档2.jpg",
            "file_path": "data/uploads/sample-2.jpg", 
            "file_size": 2048000,
            "file_type": "image/jpeg",
            "status": "completed",
            "upload_time": "2025-06-14T11:00:00",
            "metadata": {
                "format": "jpg",
                "size": 2048000
            }
        }
    ]
    
    for doc in sample_docs:
        documents_store[doc["file_id"]] = doc

# 初始化示例数据
init_sample_data()

"""
错误分析API路由
"""

from typing import Dict, List, Optional
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from datetime import datetime, timedelta
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.backend.logger import get_logger
from src.backend.services.analysis.error import ErrorAnalysisService

logger = get_logger(__name__)
router = APIRouter(prefix="/api/analysis", tags=["analysis"])

# 初始化服务
error_analysis_service = ErrorAnalysisService()

class AnalysisRequest(BaseModel):
    """分析请求模型"""
    region_type: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    min_confidence: Optional[float] = None

@router.post("/errors")
async def analyze_errors(request: AnalysisRequest):
    """
    分析OCR错误并生成详细报告
    """
    try:
        report = error_analysis_service.analyze_errors(
            region_type=request.region_type,
            start_date=request.start_date,
            end_date=request.end_date,
            min_confidence=request.min_confidence
        )
        return report
    except Exception as e:
        logger.error(f"Error analyzing OCR errors: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error analyzing OCR errors: {str(e)}"
        )

@router.get("/errors/recent")
async def get_recent_errors(days: int = Query(7, description="过去几天的数据")):
    """
    获取最近一段时间的错误分析报告
    """
    try:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        report = error_analysis_service.analyze_errors(
            start_date=start_date,
            end_date=end_date
        )
        return report
    except Exception as e:
        logger.error(f"Error getting recent error analysis: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting recent error analysis: {str(e)}"
        )

@router.get("/errors/by-region/{region_type}")
async def get_region_errors(
    region_type: str,
    days: int = Query(30, description="过去几天的数据"),
    min_confidence: Optional[float] = Query(None, description="最小置信度")
):
    """
    获取特定区域类型的错误分析报告
    """
    try:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        report = error_analysis_service.analyze_errors(
            region_type=region_type,
            start_date=start_date,
            end_date=end_date,
            min_confidence=min_confidence
        )
        return report
    except Exception as e:
        logger.error(f"Error analyzing region errors: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error analyzing region errors: {str(e)}"
        )

@router.get("/errors")
async def get_error_analysis():
    """
    获取错误分析报告
    """
    try:
        return await error_analysis_service.get_error_analysis()
    except Exception as e:
        logger.error(f"Failed to get error analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/performance")
async def get_performance_analysis():
    """
    获取性能分析报告
    """
    try:
        return await error_analysis_service.get_performance_analysis()
    except Exception as e:
        logger.error(f"Failed to get performance analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/trends")
async def get_error_trends():
    """
    获取错误趋势分析
    """
    try:
        return await error_analysis_service.get_error_trends()
    except Exception as e:
        logger.error(f"Failed to get error trends: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 
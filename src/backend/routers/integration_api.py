"""
集成API路由
"""

from typing import Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from src.backend.services.ocr.integrator import OCRAnnotationIntegrator
from src.backend.database.database import Database
from src.backend.logger import get_logger

router = APIRouter()
integrator = OCRAnnotationIntegrator()

class TaskCreationRequest(BaseModel):
    """任务创建请求"""
    task_name: str
    description: Optional[str] = None
    files: List[str]

@router.post("/tasks/")
async def create_task(request: TaskCreationRequest):
    """创建新的OCR任务"""
    try:
        task_id = await integrator.create_task(
            task_name=request.task_name,
            description=request.description,
            files=request.files
        )
        return {"task_id": str(task_id)}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/tasks/{task_id}")
async def get_task(task_id: str):
    """获取任务详情"""
    try:
        task = await integrator.get_task(task_id)
        return task
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))

@router.put("/tasks/{task_id}")
async def update_task(task_id: str, request: TaskCreationRequest):
    """更新任务信息"""
    try:
        await integrator.update_task(
            task_id=task_id,
            task_name=request.task_name,
            description=request.description,
            files=request.files
        )
        return {"status": "success"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/tasks/{task_id}/status")
async def get_task_status(task_id: str):
    """获取任务状态"""
    try:
        status = await integrator.get_task_status(task_id)
        return {"status": status}
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))

@router.get("/tasks/{task_id}/results")
async def get_task_results(task_id: str):
    """获取任务结果"""
    try:
        results = await integrator.get_task_results(task_id)
        return results
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))

@router.post("/tasks/{task_id}/process")
async def process_task(task_id: str):
    """处理任务"""
    try:
        await integrator.process_task(task_id)
        return {"status": "processing"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/tasks/{task_id}/annotations")
async def get_task_annotations(task_id: str):
    """获取任务标注结果"""
    try:
        annotations = await integrator.get_task_annotations(task_id)
        return annotations
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e)) 
from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any
from src.backend.services.document_style_service import DocumentStyleService
from pydantic import BaseModel

router = APIRouter(
    prefix="/api/document-style",
    tags=["document-style"]
)

# 初始化服务
style_service = DocumentStyleService()

class TemplateData(BaseModel):
    """样式模板数据模型"""
    table: Dict[str, Any]
    chart: Dict[str, Any]

@router.get("/templates", response_model=List[str])
async def get_templates():
    """获取所有可用的样式模板"""
    try:
        return style_service.get_available_templates()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/templates/{template_name}", response_model=Dict[str, Any])
async def get_template(template_name: str):
    """获取指定样式模板的详细信息"""
    try:
        return style_service.load_template(template_name)
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))

@router.post("/templates/{template_name}")
async def create_template(template_name: str, template_data: TemplateData):
    """创建新的样式模板"""
    try:
        style_service.create_custom_template(template_name, template_data.dict())
        return {"message": f"Template {template_name} created successfully"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/templates/{template_name}")
async def update_template(template_name: str, template_data: TemplateData):
    """更新现有的样式模板"""
    try:
        style_service.update_template(template_name, template_data.dict())
        return {"message": f"Template {template_name} updated successfully"}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/templates/{template_name}")
async def delete_template(template_name: str):
    """删除样式模板"""
    try:
        style_service.delete_template(template_name)
        return {"message": f"Template {template_name} deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 
import hashlib
from pathlib import Path
from PIL import Image
import io
import logging

logger = logging.getLogger(__name__)

def get_file_hash(file_content: bytes) -> str:
    """计算文件内容的SHA256哈希值"""
    return hashlib.sha256(file_content).hexdigest()

def create_thumbnail(image_content: bytes, size: tuple[int, int] = (200, 200)) -> bytes:
    """创建图片缩略图
    
    Args:
        image_content: 原始图片内容
        size: 缩略图尺寸 (width, height)
        
    Returns:
        bytes: 缩略图内容
    """
    try:
        # 从二进制内容创建图片对象
        image = Image.open(io.BytesIO(image_content))
        
        # 创建缩略图
        image.thumbnail(size)
        
        # 将缩略图转换为bytes
        output = io.BytesIO()
        image.save(output, format=image.format or 'JPEG')
        return output.getvalue()
        
    except Exception as e:
        logger.error(f"创建缩略图失败: {str(e)}")
        return image_content  # 如果失败则返回原图 
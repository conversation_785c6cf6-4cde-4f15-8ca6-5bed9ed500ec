"""
共享消息队列模块
提供RabbitMQ消息队列的管理和操作
"""

import json
import aio_pika
from typing import Any, Callable, Dict, Optional
from functools import wraps
import asyncio
from .config import get_rabbitmq_settings
from .logger import get_logger
from .metrics import update_queue_size

logger = get_logger("queue")

class QueueManager:
    """消息队列管理类"""
    
    _connection: Optional[aio_pika.Connection] = None
    _channel: Optional[aio_pika.Channel] = None
    _queues: Dict[str, aio_pika.Queue] = {}
    
    @classmethod
    async def get_connection(cls) -> aio_pika.Connection:
        """获取RabbitMQ连接"""
        if cls._connection is None or cls._connection.is_closed:
            rabbitmq_settings = get_rabbitmq_settings()
            try:
                cls._connection = await aio_pika.connect_robust(
                    rabbitmq_settings["uri"]
                )
                logger.info("Successfully connected to RabbitMQ")
            except Exception as e:
                logger.error(f"Failed to connect to RabbitMQ: {str(e)}")
                raise
        return cls._connection
    
    @classmethod
    async def get_channel(cls) -> aio_pika.Channel:
        """获取RabbitMQ通道"""
        if cls._channel is None or cls._channel.is_closed:
            connection = await cls.get_connection()
            cls._channel = await connection.channel()
            await cls._channel.set_qos(prefetch_count=1)
            logger.info("Successfully created RabbitMQ channel")
        return cls._channel
    
    @classmethod
    async def get_queue(cls, queue_name: str) -> aio_pika.Queue:
        """
        获取或创建队列
        
        Args:
            queue_name: 队列名称
        """
        if queue_name not in cls._queues:
            channel = await cls.get_channel()
            cls._queues[queue_name] = await channel.declare_queue(
                queue_name,
                durable=True
            )
            logger.info(f"Successfully declared queue: {queue_name}")
        return cls._queues[queue_name]
    
    @classmethod
    async def publish_message(
        cls,
        queue_name: str,
        message: Dict[str, Any],
        priority: int = 0
    ):
        """
        发布消息到队列
        
        Args:
            queue_name: 队列名称
            message: 消息内容
            priority: 消息优先级（0-9，9为最高）
        """
        try:
            channel = await cls.get_channel()
            queue = await cls.get_queue(queue_name)
            
            # 将消息转换为JSON字符串
            message_body = json.dumps(message).encode()
            
            # 创建消息
            message = aio_pika.Message(
                body=message_body,
                delivery_mode=aio_pika.DeliveryMode.PERSISTENT,
                priority=priority
            )
            
            # 发送消息
            await channel.default_exchange.publish(
                message,
                routing_key=queue_name
            )
            
            # 更新队列大小指标
            queue_info = await queue.declare(passive=True)
            update_queue_size("queue_manager", queue_name, queue_info.message_count)
            
            logger.info(f"Successfully published message to queue: {queue_name}")
        except Exception as e:
            logger.error(f"Failed to publish message to queue {queue_name}: {str(e)}")
            raise
    
    @classmethod
    async def consume_messages(
        cls,
        queue_name: str,
        callback: Callable[[Dict[str, Any]], Any]
    ):
        """
        消费队列消息
        
        Args:
            queue_name: 队列名称
            callback: 消息处理回调函数
        """
        try:
            queue = await cls.get_queue(queue_name)
            
            async def process_message(message: aio_pika.IncomingMessage):
                async with message.process():
                    try:
                        # 解析消息内容
                        message_data = json.loads(message.body.decode())
                        
                        # 调用回调函数处理消息
                        await callback(message_data)
                        
                        # 更新队列大小指标
                        queue_info = await queue.declare(passive=True)
                        update_queue_size("queue_manager", queue_name, queue_info.message_count)
                        
                        logger.info(f"Successfully processed message from queue: {queue_name}")
                    except Exception as e:
                        logger.error(f"Failed to process message from queue {queue_name}: {str(e)}")
                        # 消息处理失败，可以选择重新入队或发送到死信队列
                        raise
            
            # 开始消费消息
            await queue.consume(process_message)
            logger.info(f"Started consuming messages from queue: {queue_name}")
            
            # 保持消费者运行
            try:
                await asyncio.Future()  # 永久等待
            except asyncio.CancelledError:
                logger.info(f"Stopped consuming messages from queue: {queue_name}")
        except Exception as e:
            logger.error(f"Failed to consume messages from queue {queue_name}: {str(e)}")
            raise
    
    @classmethod
    async def close(cls):
        """关闭RabbitMQ连接"""
        if cls._channel and not cls._channel.is_closed:
            await cls._channel.close()
            logger.info("Closed RabbitMQ channel")
        
        if cls._connection and not cls._connection.is_closed:
            await cls._connection.close()
            logger.info("Closed RabbitMQ connection")

# 队列依赖项
async def get_queue_manager():
    """队列管理器依赖项"""
    try:
        connection = await QueueManager.get_connection()
        yield QueueManager
    finally:
        await QueueManager.close() 
"""
FastAPI中间件
"""

import time
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from src.common.metrics import API_REQUEST_LATENCY, API_REQUEST_COUNT
from src.common.logger import get_logger

logger = get_logger("middleware")

class MetricsMiddleware(BaseHTTPMiddleware):
    """收集API请求指标的中间件"""
    
    def __init__(self, app: ASGIApp):
        """初始化中间件"""
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并收集指标"""
        start_time = time.time()
        
        # 获取请求信息
        method = request.method
        path = request.url.path
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 记录成功请求
            API_REQUEST_COUNT.labels(
                endpoint=path,
                method=method,
                status="success"
            ).inc()
            
        except Exception as e:
            # 记录失败请求
            API_REQUEST_COUNT.labels(
                endpoint=path,
                method=method,
                status="error"
            ).inc()
            
            logger.error(f"Request failed: {str(e)}")
            raise
        
        finally:
            # 记录请求延迟
            duration = time.time() - start_time
            API_REQUEST_LATENCY.labels(
                endpoint=path,
                method=method
            ).observe(duration)
        
        return response 
"""
共享监控指标模块
提供统一的Prometheus指标收集功能
"""

from prometheus_client import Counter, Histogram, Gauge, Summary
from typing import Dict
from functools import wraps
import time

# 请求计数器
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['service', 'method', 'endpoint', 'status']
)

# 请求延迟直方图
REQUEST_LATENCY = Histogram(
    'http_request_duration_seconds',
    'HTTP request latency',
    ['service', 'method', 'endpoint']
)

# 文件处理计数器
FILE_PROCESS_COUNT = Counter(
    'file_process_total',
    'Total processed files',
    ['service', 'status', 'file_type']
)

# OCR处理时间
OCR_PROCESS_TIME = Histogram(
    'ocr_process_duration_seconds',
    'OCR processing time',
    ['service', 'model', 'file_type']
)

# 队列大小
QUEUE_SIZE = Gauge(
    'queue_size',
    'Current queue size',
    ['service', 'queue_name']
)

# 系统资源使用
SYSTEM_MEMORY = Gauge(
    'system_memory_bytes',
    'Current system memory usage',
    ['service']
)

SYSTEM_CPU = Gauge(
    'system_cpu_usage',
    'Current system CPU usage',
    ['service']
)

# API响应时间统计
API_RESPONSE_TIME = Summary(
    'api_response_time_seconds',
    'API response time',
    ['service', 'endpoint']
)

# 批处理任务计数器
BATCH_TASK_COUNT = Counter(
    "batch_task_total",
    "Total number of batch tasks",
    ["status"]  # created, completed, failed, cancelled
)

# 文件处理时间直方图
FILE_PROCESS_TIME = Histogram(
    "file_process_duration_seconds",
    "Time spent processing files",
    ["type"]  # ocr, quality_check
)

# 批处理任务进度指标
BATCH_TASK_PROGRESS = Gauge(
    "batch_task_progress",
    "Progress of batch tasks",
    ["batch_id"]
)

# 当前活跃批处理任务数
ACTIVE_BATCH_TASKS = Gauge(
    "active_batch_tasks",
    "Number of currently active batch tasks"
)

# 文件处理错误计数器
FILE_ERROR_COUNT = Counter(
    "file_error_total",
    "Total number of file processing errors",
    ["error_type"]  # validation_error, ocr_error, quality_error
)

# 系统资源使用指标
SYSTEM_MEMORY_USAGE = Gauge(
    "system_memory_usage_bytes",
    "Current system memory usage in bytes"
)

CPU_USAGE = Gauge(
    "system_cpu_usage_percent",
    "Current CPU usage percentage"
)

# API请求延迟直方图
API_REQUEST_LATENCY = Histogram(
    "api_request_duration_seconds",
    "API request latency in seconds",
    ["endpoint", "method"]
)

# API请求计数器
API_REQUEST_COUNT = Counter(
    "api_request_total",
    "Total number of API requests",
    ["endpoint", "method", "status"]
)

def track_request_metrics(service_name: str):
    """
    请求指标跟踪装饰器
    
    Args:
        service_name: 服务名称
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                response = await func(*args, **kwargs)
                status = response.status_code
            except Exception as e:
                status = 500
                raise e
            finally:
                duration = time.time() - start_time
                
                # 更新请求计数
                REQUEST_COUNT.labels(
                    service=service_name,
                    method=kwargs.get('method', 'unknown'),
                    endpoint=kwargs.get('endpoint', 'unknown'),
                    status=status
                ).inc()
                
                # 更新请求延迟
                REQUEST_LATENCY.labels(
                    service=service_name,
                    method=kwargs.get('method', 'unknown'),
                    endpoint=kwargs.get('endpoint', 'unknown')
                ).observe(duration)
            
            return response
        return wrapper
    return decorator

def track_file_processing(service_name: str, file_type: str):
    """
    文件处理指标跟踪装饰器
    
    Args:
        service_name: 服务名称
        file_type: 文件类型
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                result = await func(*args, **kwargs)
                status = 'success'
            except Exception as e:
                status = 'error'
                raise e
            finally:
                FILE_PROCESS_COUNT.labels(
                    service=service_name,
                    status=status,
                    file_type=file_type
                ).inc()
            return result
        return wrapper
    return decorator

def track_ocr_processing(service_name: str, model: str, file_type: str):
    """
    OCR处理指标跟踪装饰器
    
    Args:
        service_name: 服务名称
        model: 模型名称
        file_type: 文件类型
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = await func(*args, **kwargs)
            finally:
                duration = time.time() - start_time
                OCR_PROCESS_TIME.labels(
                    service=service_name,
                    model=model,
                    file_type=file_type
                ).observe(duration)
            
            return result
        return wrapper
    return decorator

def update_queue_size(service_name: str, queue_name: str, size: int):
    """
    更新队列大小指标
    
    Args:
        service_name: 服务名称
        queue_name: 队列名称
        size: 队列大小
    """
    QUEUE_SIZE.labels(
        service=service_name,
        queue_name=queue_name
    ).set(size)

def update_system_metrics(service_name: str, memory_usage: float, cpu_usage: float):
    """
    更新系统资源使用指标
    
    Args:
        service_name: 服务名称
        memory_usage: 内存使用量（字节）
        cpu_usage: CPU使用率（百分比）
    """
    SYSTEM_MEMORY.labels(service=service_name).set(memory_usage)
    SYSTEM_CPU.labels(service=service_name).set(cpu_usage)

def track_api_response_time(service_name: str, endpoint: str):
    """
    API响应时间跟踪装饰器
    
    Args:
        service_name: 服务名称
        endpoint: API端点
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = await func(*args, **kwargs)
            finally:
                duration = time.time() - start_time
                API_RESPONSE_TIME.labels(
                    service=service_name,
                    endpoint=endpoint
                ).observe(duration)
            
            return result
        return wrapper
    return decorator 
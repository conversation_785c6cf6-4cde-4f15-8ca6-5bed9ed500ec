#!/usr/bin/env python3
"""
前端项目启动脚本
自动检查依赖并启动开发服务器
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_colored(text: str, color: str = 'white') -> None:
    """打印彩色文本"""
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'white': '\033[97m',
        'end': '\033[0m'
    }
    print(f"{colors.get(color, colors['white'])}{text}{colors['end']}")

def run_command(command: str, cwd: str = None) -> tuple[int, str, str]:
    """运行shell命令并返回结果"""
    try:
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=cwd,
            text=True
        )
        stdout, stderr = process.communicate()
        return process.returncode, stdout, stderr
    except Exception as e:
        return 1, '', str(e)

def check_node_installed() -> bool:
    """检查是否安装了Node.js"""
    code, _, _ = run_command('node --version')
    return code == 0

def check_npm_installed() -> bool:
    """检查是否安装了npm"""
    code, _, _ = run_command('npm --version')
    return code == 0

def main():
    # 获取前端目录的绝对路径
    script_dir = Path(__file__).resolve().parent
    frontend_dir = script_dir.parent / 'frontend'
    
    if not frontend_dir.exists():
        print_colored("错误：找不到前端目录！", 'red')
        print(f"期望路径: {frontend_dir}")
        sys.exit(1)

    # 检查Node.js和npm
    if not check_node_installed():
        print_colored("错误：未安装Node.js！请先安装Node.js", 'red')
        print_colored("可以从 https://nodejs.org 下载安装", 'yellow')
        sys.exit(1)

    if not check_npm_installed():
        print_colored("错误：未安装npm！请先安装npm", 'red')
        sys.exit(1)

    # 切换到前端目录
    os.chdir(frontend_dir)
    print_colored("正在检查前端依赖...", 'blue')

    # 安装依赖
    code, stdout, stderr = run_command('npm install', cwd=str(frontend_dir))
    if code != 0:
        print_colored("安装依赖失败！", 'red')
        print_colored(stderr, 'red')
        sys.exit(1)
    
    print_colored("依赖安装完成！", 'green')
    print_colored("正在启动开发服务器...", 'blue')

    # 启动开发服务器
    try:
        process = subprocess.Popen(
            'npm run dev',
            shell=True,
            cwd=str(frontend_dir)
        )

        # 等待服务器启动
        time.sleep(2)
        print_colored("\n开发服务器已启动！", 'green')
        print_colored("访问地址: http://localhost:3000", 'blue')
        print_colored("按 Ctrl+C 停止服务器\n", 'yellow')

        # 保持脚本运行
        process.wait()

    except KeyboardInterrupt:
        print_colored("\n正在停止服务器...", 'yellow')
        process.terminate()
        process.wait()
        print_colored("服务器已停止", 'green')
    except Exception as e:
        print_colored(f"\n启动服务器时出错：{e}", 'red')
        sys.exit(1)

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print_colored("\n操作已取消", 'yellow')
        sys.exit(0) 
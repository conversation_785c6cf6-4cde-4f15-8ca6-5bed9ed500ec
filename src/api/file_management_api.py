"""
文件管理API路由
提供文件管理和状态跟踪的API接口
"""

from fastapi import APIRouter, Depends, Query, HTTPException
from typing import Optional, Dict
from pymongo import MongoClient

from src.services.file_management_service import FileManagementService
from src.database.mongodb import get_mongodb_client

router = APIRouter(
    prefix="/api/files",
    tags=["files"]
)

def get_file_service(
    mongodb_client: MongoClient = Depends(get_mongodb_client)
) -> FileManagementService:
    """获取文件管理服务实例"""
    return FileManagementService(mongodb_client)

@router.get("/list")
async def list_files(
    status: Optional[str] = None,
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    sort_by: str = "created_at",
    sort_order: int = Query(-1, ge=-1, le=1),
    file_service: FileManagementService = Depends(get_file_service)
) -> Dict:
    """
    获取文件列表
    
    参数:
        status: 可选的状态过滤
        page: 页码（从1开始）
        limit: 每页数量（最大100）
        sort_by: 排序字段
        sort_order: 排序方向（1升序，-1降序）
    """
    result = file_service.list_files(
        status=status,
        page=page,
        limit=limit,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
        
    return result["data"]

@router.get("/{file_id}")
async def get_file_details(
    file_id: str,
    file_service: FileManagementService = Depends(get_file_service)
) -> Dict:
    """
    获取文件详细信息
    
    参数:
        file_id: 文件ID
    """
    result = file_service.get_file_details(file_id)
    
    if not result["success"]:
        if result["error"] == "文件不存在":
            raise HTTPException(status_code=404, detail=result["error"])
        raise HTTPException(status_code=500, detail=result["error"])
        
    return result["data"]

@router.put("/{file_id}/status")
async def update_file_status(
    file_id: str,
    status: str,
    message: Optional[str] = "",
    metadata: Optional[Dict] = None,
    file_service: FileManagementService = Depends(get_file_service)
) -> Dict:
    """
    更新文件状态
    
    参数:
        file_id: 文件ID
        status: 新状态
        message: 状态更新说明
        metadata: 可选的元数据更新
    """
    result = file_service.update_file_status(
        file_id=file_id,
        status=status,
        message=message,
        metadata=metadata
    )
    
    if not result["success"]:
        if result["error"] == "文件不存在或状态未更新":
            raise HTTPException(status_code=404, detail=result["error"])
        raise HTTPException(status_code=500, detail=result["error"])
        
    return result["data"]

@router.delete("/{file_id}")
async def delete_file(
    file_id: str,
    file_service: FileManagementService = Depends(get_file_service)
) -> Dict:
    """
    删除文件（软删除）
    
    参数:
        file_id: 文件ID
    """
    result = file_service.delete_file(file_id)
    
    if not result["success"]:
        if result["error"] == "文件不存在或状态未更新":
            raise HTTPException(status_code=404, detail=result["error"])
        raise HTTPException(status_code=500, detail=result["error"])
        
    return result["data"]

@router.get("/statistics")
async def get_file_statistics(
    file_service: FileManagementService = Depends(get_file_service)
) -> Dict:
    """获取文件统计信息"""
    result = file_service.get_file_statistics()
    
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
        
    return result["data"] 
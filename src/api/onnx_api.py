"""
ONNX模型API
"""

from typing import Dict, List, Optional, Any
from fastapi import FastAPI, HTTPException, UploadFile, File, Query
from pydantic import BaseModel
import numpy as np
import io
import cv2
import time

from src.services.onnx_model_manager import ONNXModelManager
from src.services.ab_testing_service import ABTestingService

app = FastAPI(title="ONNX Model API")
model_manager = ONNXModelManager()
ab_testing = ABTestingService()

class ModelInfo(BaseModel):
    """模型信息模型"""
    name: str
    version: str
    format: str = "onnx"
    config: Optional[Dict[str, Any]] = None

class InferenceRequest(BaseModel):
    """推理请求模型"""
    model_name: str
    version: str
    inputs: Dict[str, List[float]]

class InferenceResponse(BaseModel):
    """推理响应模型"""
    outputs: Dict[str, List[float]]
    model_name: str
    version: str

class ABTestCreate(BaseModel):
    """A/B测试创建请求模型"""
    name: str
    model_a: Dict[str, str]
    model_b: Dict[str, str]
    traffic_split: float = 0.5
    description: Optional[str] = None
    metrics: Optional[List[str]] = None

class OptimizeRequest(BaseModel):
    """优化请求模型"""
    num_threads: Optional[int] = None
    execution_mode: Optional[str] = None

@app.post("/models/load")
async def load_model(model_info: ModelInfo):
    """
    加载模型
    """
    try:
        await model_manager.load_model(
            model_name=model_info.name,
            version=model_info.version
        )
        return {"message": f"模型加载成功: {model_info.name} v{model_info.version}"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/models/unload")
async def unload_model(model_info: ModelInfo):
    """
    卸载模型
    """
    try:
        await model_manager.unload_model(
            model_name=model_info.name,
            version=model_info.version
        )
        return {"message": f"模型卸载成功: {model_info.name} v{model_info.version}"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/models")
async def list_models(model_name: Optional[str] = None, version: Optional[str] = None):
    """
    获取模型列表
    """
    try:
        models = await model_manager.get_model_info(model_name, version)
        return {"models": models}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/inference")
async def run_inference(request: InferenceRequest):
    """
    运行模型推理
    """
    try:
        # 将输入转换为numpy数组
        inputs = {
            name: np.array(value, dtype=np.float32)
            for name, value in request.inputs.items()
        }
        
        # 运行推理
        outputs = await model_manager.run_inference(
            model_name=request.model_name,
            version=request.version,
            inputs=inputs
        )
        
        # 将输出转换为列表
        response = {
            name: output.tolist()
            for name, output in outputs.items()
        }
        
        return InferenceResponse(
            outputs=response,
            model_name=request.model_name,
            version=request.version
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/inference/image")
async def run_image_inference(
    model_name: str,
    version: str,
    image: UploadFile = File(...)
):
    """
    运行图像推理
    """
    try:
        # 读取图像
        contents = await image.read()
        nparr = np.frombuffer(contents, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if img is None:
            raise ValueError("无法读取图像")
            
        # 预处理图像（根据模型要求调整）
        img = cv2.resize(img, (224, 224))  # 示例尺寸
        img = img.astype(np.float32) / 255.0
        img = np.transpose(img, (2, 0, 1))  # HWC to CHW
        img = np.expand_dims(img, axis=0)  # 添加batch维度
        
        # 运行推理
        inputs = {"input": img}
        outputs = await model_manager.run_inference(
            model_name=model_name,
            version=version,
            inputs=inputs
        )
        
        # 将输出转换为列表
        response = {
            name: output.tolist()
            for name, output in outputs.items()
        }
        
        return InferenceResponse(
            outputs=response,
            model_name=model_name,
            version=version
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/health")
async def health_check():
    """
    健康检查
    """
    return {"status": "healthy"}

@app.post("/ab-tests")
async def create_ab_test(test_info: ABTestCreate):
    """
    创建A/B测试
    """
    try:
        test_id = await ab_testing.create_test(
            name=test_info.name,
            model_a=test_info.model_a,
            model_b=test_info.model_b,
            traffic_split=test_info.traffic_split,
            description=test_info.description,
            metrics=test_info.metrics
        )
        return {"test_id": test_id}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/ab-tests/{test_id}/inference")
async def run_ab_test_inference(
    test_id: str,
    request: InferenceRequest
):
    """
    运行A/B测试推理
    """
    try:
        # 获取要使用的模型
        model_name, version = await ab_testing.get_model_for_request(test_id)
        
        # 记录开始时间
        start_time = time.time()
        
        # 将输入转换为numpy数组
        inputs = {
            name: np.array(value, dtype=np.float32)
            for name, value in request.inputs.items()
        }
        
        # 运行推理
        outputs = await model_manager.run_inference(
            model_name=model_name,
            version=version,
            inputs=inputs
        )
        
        # 计算延迟
        latency = time.time() - start_time
        
        # 记录指标
        await ab_testing.record_metrics(
            test_id=test_id,
            model_name=model_name,
            version=version,
            metrics={"latency": latency}
        )
        
        # 将输出转换为列表
        response = {
            name: output.tolist()
            for name, output in outputs.items()
        }
        
        return InferenceResponse(
            outputs=response,
            model_name=model_name,
            version=version
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/ab-tests/{test_id}/image")
async def run_ab_test_image_inference(
    test_id: str,
    image: UploadFile = File(...)
):
    """
    运行A/B测试图像推理
    """
    try:
        # 获取要使用的模型
        model_name, version = await ab_testing.get_model_for_request(test_id)
        
        # 记录开始时间
        start_time = time.time()
        
        # 读取图像
        contents = await image.read()
        nparr = np.frombuffer(contents, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if img is None:
            raise ValueError("无法读取图像")
            
        # 预处理图像（根据模型要求调整）
        img = cv2.resize(img, (224, 224))  # 示例尺寸
        img = img.astype(np.float32) / 255.0
        img = np.transpose(img, (2, 0, 1))  # HWC to CHW
        img = np.expand_dims(img, axis=0)  # 添加batch维度
        
        # 运行推理
        inputs = {"input": img}
        outputs = await model_manager.run_inference(
            model_name=model_name,
            version=version,
            inputs=inputs
        )
        
        # 计算延迟
        latency = time.time() - start_time
        
        # 记录指标
        await ab_testing.record_metrics(
            test_id=test_id,
            model_name=model_name,
            version=version,
            metrics={"latency": latency}
        )
        
        # 将输出转换为列表
        response = {
            name: output.tolist()
            for name, output in outputs.items()
        }
        
        return InferenceResponse(
            outputs=response,
            model_name=model_name,
            version=version
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/ab-tests/{test_id}/results")
async def get_ab_test_results(test_id: str):
    """
    获取A/B测试结果
    """
    try:
        results = await ab_testing.get_test_results(test_id)
        return results
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/ab-tests/{test_id}/stop")
async def stop_ab_test(test_id: str):
    """
    停止A/B测试
    """
    try:
        await ab_testing.stop_test(test_id)
        return {"message": f"测试已停止: {test_id}"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/models/{model_name}/{version}/optimize")
async def optimize_model(
    model_name: str,
    version: str,
    request: OptimizeRequest
):
    """
    优化模型性能
    """
    try:
        result = await model_manager.optimize_model(
            model_name=model_name,
            version=version,
            num_threads=request.num_threads,
            execution_mode=request.execution_mode
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/inference/batch")
async def run_batch_inference(
    request: InferenceRequest,
    batch_size: int = Query(default=32, gt=0)
):
    """
    运行批处理推理
    """
    try:
        # 将输入转换为numpy数组
        inputs = {
            name: np.array(value, dtype=np.float32)
            for name, value in request.inputs.items()
        }
        
        # 运行批处理推理
        outputs = await model_manager.run_inference(
            model_name=request.model_name,
            version=request.version,
            inputs=inputs,
            batch_size=batch_size
        )
        
        # 将输出转换为列表
        response = {
            name: output.tolist()
            for name, output in outputs.items()
        }
        
        return InferenceResponse(
            outputs=response,
            model_name=request.model_name,
            version=request.version
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/inference/image/batch")
async def run_batch_image_inference(
    model_name: str,
    version: str,
    images: List[UploadFile] = File(...),
    batch_size: int = Query(default=32, gt=0)
):
    """
    运行批处理图像推理
    """
    try:
        # 读取所有图像
        all_images = []
        for image in images:
            contents = await image.read()
            nparr = np.frombuffer(contents, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if img is None:
                raise ValueError(f"无法读取图像: {image.filename}")
                
            # 预处理图像
            img = cv2.resize(img, (224, 224))  # 示例尺寸
            img = img.astype(np.float32) / 255.0
            img = np.transpose(img, (2, 0, 1))  # HWC to CHW
            all_images.append(img)
            
        # 将图像堆叠为批次
        batch_input = np.stack(all_images)
        
        # 运行批处理推理
        outputs = await model_manager.run_inference(
            model_name=model_name,
            version=version,
            inputs={"input": batch_input},
            batch_size=batch_size
        )
        
        # 将输出转换为列表
        response = {
            name: output.tolist()
            for name, output in outputs.items()
        }
        
        return InferenceResponse(
            outputs=response,
            model_name=model_name,
            version=version
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e)) 
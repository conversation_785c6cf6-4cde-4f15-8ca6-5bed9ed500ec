"""
ONNX Runtime API路由
"""

import os
from typing import Dict, List, Optional
import numpy as np
from fastapi import APIRouter, HTTPException, UploadFile, File
from pydantic import BaseModel

from src.models.onnx_runtime_service import ONNXRuntimeService


class PredictRequest(BaseModel):
    """推理请求"""
    input_data: List[List[float]]


class PredictResponse(BaseModel):
    """推理响应"""
    output: List[List[float]]
    error: Optional[str] = None


router = APIRouter(prefix="/onnx", tags=["onnx"])
service = ONNXRuntimeService(work_dir="work")


@router.post("/models/{version_id}/deploy")
async def deploy_model(version_id: str, model_file: UploadFile = File(...)):
    """部署模型"""
    try:
        # 保存上传的模型文件
        model_path = f"work/uploads/{model_file.filename}"
        os.makedirs(os.path.dirname(model_path), exist_ok=True)
        
        content = await model_file.read()
        with open(model_path, "wb") as f:
            f.write(content)
        
        # 部署模型
        success = service.deploy_model(model_path, version_id)
        if not success:
            raise HTTPException(status_code=500, detail="模型部署失败")
        
        return {"message": "模型部署成功"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/models/{version_id}/predict", response_model=PredictResponse)
async def predict(version_id: str, request: PredictRequest):
    """执行推理"""
    try:
        # 转换输入数据
        input_data = np.array(request.input_data, dtype=np.float32)
        
        # 执行推理
        output, error = service.predict(version_id, input_data)
        
        if error:
            return PredictResponse(output=[], error=error)
        
        # 转换输出数据
        output_list = output.tolist()
        return PredictResponse(output=output_list)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/models")
async def list_models():
    """列出模型"""
    try:
        models = service.list_models()
        return {"models": models}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/models/{version_id}")
async def remove_model(version_id: str):
    """移除模型"""
    try:
        success = service.remove_model(version_id)
        if not success:
            raise HTTPException(status_code=404, detail="模型不存在")
            
        return {"message": "模型移除成功"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 
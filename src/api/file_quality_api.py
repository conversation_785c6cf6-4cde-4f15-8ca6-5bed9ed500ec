#!/usr/bin/env python3
"""
文件质量检查API
"""

from fastapi import APIRouter, HTTPException, UploadFile, File
from fastapi.responses import JSONResponse
from pathlib import Path
import tempfile
import os
from typing import Dict, List, Optional
from pydantic import BaseModel

from src.services.file_quality_checker import FileQualityChecker

router = APIRouter(prefix="/api/file-quality", tags=["文件质量"])

class QualityThresholds(BaseModel):
    """质量阈值配置模型"""
    resolution: Optional[Dict[str, int]] = None
    blur: Optional[Dict[str, float]] = None
    brightness: Optional[Dict[str, float]] = None
    contrast: Optional[Dict[str, float]] = None
    noise: Optional[Dict[str, float]] = None
    file_size: Optional[Dict[str, float]] = None

@router.post("/check", summary="检查文件质量")
async def check_file_quality(
    file: UploadFile = File(..., description="要检查的文件"),
    thresholds: Optional[QualityThresholds] = None
) -> JSONResponse:
    """
    检查上传文件的质量
    
    Args:
        file: 上传的文件
        thresholds: 可选的质量阈值配置
        
    Returns:
        JSONResponse: 质量检查结果
    """
    try:
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # 创建质量检查器
            checker = FileQualityChecker()
            
            # 如果提供了阈值配置，更新检查器的阈值
            if thresholds:
                checker.set_quality_thresholds(thresholds.dict(exclude_none=True))
            
            # 执行质量检查
            passed, report = checker.check_file_quality(temp_file_path)
            
            # 获取改进建议
            suggestions = checker.get_improvement_suggestions(report)
            
            return JSONResponse(
                content={
                    "success": True,
                    "passed": passed,
                    "report": report,
                    "suggestions": suggestions
                },
                status_code=200
            )
            
        finally:
            # 清理临时文件
            os.unlink(temp_file_path)
            
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"文件质量检查失败: {str(e)}"
        )

@router.get("/thresholds", summary="获取当前质量阈值")
async def get_quality_thresholds() -> JSONResponse:
    """
    获取当前的质量检查阈值配置
    
    Returns:
        JSONResponse: 当前的阈值配置
    """
    try:
        checker = FileQualityChecker()
        thresholds = checker.get_quality_thresholds()
        
        return JSONResponse(
            content={
                "success": True,
                "thresholds": thresholds
            },
            status_code=200
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取质量阈值失败: {str(e)}"
        )

@router.put("/thresholds", summary="更新质量阈值")
async def update_quality_thresholds(
    thresholds: QualityThresholds
) -> JSONResponse:
    """
    更新质量检查阈值配置
    
    Args:
        thresholds: 新的阈值配置
        
    Returns:
        JSONResponse: 更新后的阈值配置
    """
    try:
        checker = FileQualityChecker()
        
        # 更新阈值
        checker.set_quality_thresholds(thresholds.dict(exclude_none=True))
        
        # 获取更新后的阈值
        updated_thresholds = checker.get_quality_thresholds()
        
        return JSONResponse(
            content={
                "success": True,
                "message": "质量阈值更新成功",
                "thresholds": updated_thresholds
            },
            status_code=200
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"更新质量阈值失败: {str(e)}"
        ) 
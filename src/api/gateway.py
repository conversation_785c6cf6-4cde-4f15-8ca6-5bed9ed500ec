"""
API网关
"""

# ... existing code ...

# 添加导出系统路由
EXPORT_SERVICE_URL = "http://export-service:8006"

@app.post("/api/projects/{project_id}/export")
async def export_project(
    project_id: str,
    request: dict,
    background_tasks: BackgroundTasks
):
    """
    导出项目数据
    """
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{EXPORT_SERVICE_URL}/projects/{project_id}/export",
                json=request
            )
            response.raise_for_status()
            return response.json()
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/projects/{project_id}/versions")
async def list_versions(project_id: str):
    """
    列出项目的所有数据集版本
    """
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{EXPORT_SERVICE_URL}/projects/{project_id}/versions"
            )
            response.raise_for_status()
            return response.json()
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/projects/{project_id}/versions/{version}")
async def get_version_info(project_id: str, version: str):
    """
    获取数据集版本信息
    """
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{EXPORT_SERVICE_URL}/projects/{project_id}/versions/{version}"
            )
            response.raise_for_status()
            return response.json()
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.delete("/api/projects/{project_id}/versions/{version}")
async def delete_version(project_id: str, version: str):
    """
    删除数据集版本
    """
    try:
        async with httpx.AsyncClient() as client:
            response = await client.delete(
                f"{EXPORT_SERVICE_URL}/projects/{project_id}/versions/{version}"
            )
            response.raise_for_status()
            return response.json()
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/projects/{project_id}/versions/{version}/validate")
async def validate_dataset(
    project_id: str,
    version: str,
    output_file: Optional[str] = None
):
    """
    验证数据集
    """
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{EXPORT_SERVICE_URL}/projects/{project_id}/versions/{version}/validate",
                params={"output_file": output_file} if output_file else None
            )
            response.raise_for_status()
            return response.json()
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e)) 
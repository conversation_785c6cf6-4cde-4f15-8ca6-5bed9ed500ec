"""
导出系统API
"""

from typing import Dict, List, Optional, Any
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel
import os
from datetime import datetime

from src.backend.services.data_export.data_export_service import DataExportService
from src.services.dataset_manager import DatasetManager
from src.services.data_validator import DataValidator

app = FastAPI(title="Export System API")
export_service = DataExportService()
dataset_manager = DatasetManager()
data_validator = DataValidator()

class ExportRequest(BaseModel):
    """导出请求模型"""
    project_id: str
    version: str
    description: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    train_ratio: float = 0.7
    val_ratio: float = 0.2
    test_ratio: float = 0.1
    random_seed: Optional[int] = None
    batch_size: int = 100

class ValidationReport(BaseModel):
    """验证报告模型"""
    project_id: str
    version: str
    timestamp: str
    statistics: Dict[str, Any]
    issues: List[Dict[str, Any]]

@app.post("/projects/{project_id}/export")
async def export_project(
    project_id: str,
    request: ExportRequest,
    background_tasks: BackgroundTasks
):
    """
    导出项目数据
    """
    try:
        # 创建数据集版本
        version_id = await dataset_manager.create_dataset_version(
            project_id=project_id,
            version=request.version,
            description=request.description,
            metadata=request.metadata
        )
        
        # 划分数据集
        split_result = await dataset_manager.split_dataset(
            project_id=project_id,
            version=request.version,
            train_ratio=request.train_ratio,
            val_ratio=request.val_ratio,
            test_ratio=request.test_ratio,
            random_seed=request.random_seed
        )
        
        # 在后台执行导出任务
        background_tasks.add_task(
            dataset_manager.export_dataset_splits,
            project_id=project_id,
            version=request.version,
            output_dir=os.path.join(".taskmaster", "exports"),
            batch_size=request.batch_size
        )
        
        return {
            "message": "导出任务已启动",
            "version_id": version_id,
            "split_result": split_result
        }
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/projects/{project_id}/versions")
async def list_versions(project_id: str):
    """
    列出项目的所有数据集版本
    """
    try:
        versions = await dataset_manager.list_versions(project_id)
        return versions
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/projects/{project_id}/versions/{version}")
async def get_version_info(project_id: str, version: str):
    """
    获取数据集版本信息
    """
    try:
        version_info = await dataset_manager.get_version_info(
            project_id,
            version
        )
        if not version_info:
            raise HTTPException(status_code=404, detail="版本不存在")
        return version_info
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.delete("/projects/{project_id}/versions/{version}")
async def delete_version(project_id: str, version: str):
    """
    删除数据集版本
    """
    try:
        success = await dataset_manager.delete_version(project_id, version)
        if not success:
            raise HTTPException(status_code=404, detail="版本不存在")
        return {"message": "版本已删除"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/projects/{project_id}/versions/{version}/validate")
async def validate_dataset(
    project_id: str,
    version: str,
    output_file: Optional[str] = None
):
    """
    验证数据集
    """
    try:
        report = await data_validator.generate_validation_report(
            project_id,
            version,
            output_file
        )
        return report
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e)) 
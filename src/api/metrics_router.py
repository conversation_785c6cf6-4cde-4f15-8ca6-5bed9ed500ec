"""
指标API路由
"""

from fastapi import APIRouter, HTTPException
from fastapi.responses import PlainTextResponse

from src.api.onnx_runtime_router import service


router = APIRouter(prefix="/metrics", tags=["metrics"])


@router.get("/model/{version_id}")
async def get_model_metrics(version_id: str):
    """获取模型指标"""
    try:
        metrics = service.metrics_collector.get_model_metrics(version_id)
        if metrics is None:
            raise HTTPException(status_code=404, detail="模型不存在")
            
        return metrics
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/all")
async def get_all_metrics():
    """获取所有指标"""
    try:
        return service.metrics_collector.get_all_metrics()
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/prometheus", response_class=PlainTextResponse)
async def get_prometheus_metrics():
    """获取Prometheus格式指标"""
    try:
        return service.metrics_collector.export_prometheus_metrics()
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 
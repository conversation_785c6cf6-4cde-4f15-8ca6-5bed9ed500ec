from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .routes import onnx_runtime

app = FastAPI(title="OCR Service API")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加路由
app.include_router(onnx_runtime.router)

@app.get("/")
async def root():
    """API根路径。"""
    return {
        "message": "Welcome to OCR Service API",
        "version": "1.0.0",
        "docs_url": "/docs"
    } 
from typing import Dict, List, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
import numpy as np
from pathlib import Path
from src.onnx_runtime.service import ONNXRuntimeService
from src.onnx_runtime.config import ONNXRuntimeConfig

router = APIRouter(prefix="/onnx", tags=["onnx-runtime"])

# 全局变量用于存储测试环境的路径
_test_model_dir: Optional[Path] = None
_test_cache_dir: Optional[Path] = None

def set_test_dirs(model_dir: Path, cache_dir: Path):
    """设置测试环境的目录。"""
    global _test_model_dir, _test_cache_dir
    _test_model_dir = model_dir
    _test_cache_dir = cache_dir

def get_onnx_service() -> ONNXRuntimeService:
    """获取ONNX Runtime服务实例。"""
    global _test_model_dir, _test_cache_dir
    
    # 使用测试目录或默认目录
    model_dir = _test_model_dir if _test_model_dir else Path("models")
    cache_dir = _test_cache_dir if _test_cache_dir else Path("cache")
    
    config = ONNXRuntimeConfig(
        model_dir=model_dir,
        cache_dir=cache_dir,
        providers=['CPUExecutionProvider'],
        num_threads=4,
        enable_optimization=True,
        batch_size=1,
        timeout_ms=5000,
        model_versions={'test-model': ['v1', 'v2']},
        default_version={'test-model': 'v1'}
    )
    return ONNXRuntimeService(config)

# 请求/响应模型
class PredictRequest(BaseModel):
    """预测请求模型。"""
    inputs: Dict[str, List[List[float]]]
    model_name: str
    version: Optional[str] = None

class PredictResponse(BaseModel):
    """预测响应模型。"""
    outputs: Dict[str, List[List[float]]]
    model_name: str
    version: str

class ModelInfo(BaseModel):
    """模型信息模型。"""
    name: str
    versions: List[str]
    default_version: str
    input_names: List[str]
    output_names: List[str]
    input_shapes: Dict[str, List[int]]
    output_shapes: Dict[str, List[int]]

class ModelsResponse(BaseModel):
    """可用模型响应模型。"""
    models: Dict[str, List[str]]

@router.post("/predict", response_model=PredictResponse)
async def predict(
    request: PredictRequest,
    service: ONNXRuntimeService = Depends(get_onnx_service)
):
    """使用模型进行预测。"""
    try:
        # 转换输入数据为numpy数组
        inputs = {
            name: np.array(data, dtype=np.float32)
            for name, data in request.inputs.items()
        }
        
        # 进行预测
        outputs = service.predict(
            model_name=request.model_name,
            inputs=inputs,
            version=request.version
        )
        
        # 转换输出数据为列表
        outputs_list = {
            name: data.tolist()
            for name, data in outputs.items()
        }
        
        return PredictResponse(
            outputs=outputs_list,
            model_name=request.model_name,
            version=request.version or service.config.default_version[request.model_name]
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except KeyError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/models", response_model=List[ModelInfo])
async def list_models(service: ONNXRuntimeService = Depends(get_onnx_service)) -> List[ModelInfo]:
    """列出所有可用的模型。"""
    try:
        models = []
        for name, versions in service.config.model_versions.items():
            try:
                # 获取模型信息
                info = service.get_model_info(name)
                if info:
                    models.append(ModelInfo(**info))
            except Exception as e:
                # 如果获取模型信息失败，记录错误并继续
                service.logger.error(f"Failed to get info for model {name}: {e}")
        return models
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/models/{model_name}/info", response_model=ModelInfo)
async def get_model_info(
    model_name: str,
    service: ONNXRuntimeService = Depends(get_onnx_service)
) -> ModelInfo:
    """获取模型的详细信息。"""
    try:
        # 获取模型信息
        info = service.get_model_info(model_name)
        if not info:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")
        return ModelInfo(**info)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/models/{model_name}/unload")
async def unload_model(
    model_name: str,
    service: ONNXRuntimeService = Depends(get_onnx_service)
) -> Dict[str, str]:
    """卸载指定的模型。"""
    try:
        if model_name not in service.config.model_versions:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")
        service.unload_model(model_name)
        return {"message": f"Model {model_name} unloaded successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 
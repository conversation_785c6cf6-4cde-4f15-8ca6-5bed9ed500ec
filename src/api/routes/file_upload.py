import os
import shutil
from pathlib import Path
from typing import Optional
from fastapi import UploadFile, HTTPException
import onnx

async def validate_model_file(file: UploadFile) -> bool:
    """验证上传的模型文件。"""
    try:
        # 读取文件内容
        content = await file.read()
        # 验证是否为有效的ONNX模型
        onnx.load_from_string(content)
        # 重置文件指针
        await file.seek(0)
        return True
    except Exception as e:
        return False

async def save_uploaded_file(file: UploadFile, target_path: Path) -> bool:
    """保存上传的文件。"""
    try:
        # 确保目标目录存在
        target_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存文件
        with open(target_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        return True
    except Exception as e:
        return False

async def process_model_upload(file: UploadFile, model_name: str, version: str, upload_dir: Path) -> bool:
    """处理模型上传。"""
    try:
        # 验证模型文件
        if not await validate_model_file(file):
            return False
        
        # 构建目标路径
        version_dir = upload_dir / model_name / version
        target_path = version_dir / f"{model_name}.onnx"
        
        # 检查目录是否存在，如果不存在则创建
        version_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存文件
        return await save_uploaded_file(file, target_path)
    except Exception as e:
        print(f"模型上传处理失败: {str(e)}")  # 添加错误日志
        return False 
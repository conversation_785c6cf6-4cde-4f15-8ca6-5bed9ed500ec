"""
文件上传路由

处理多格式文件的上传、验证和存储。
"""

import os
from typing import List, Dict, Optional
from fastapi import APIRouter, UploadFile, File, HTTPException, BackgroundTasks, Form, Query
from fastapi.responses import JSONResponse
from pymongo import MongoClient
from datetime import datetime
import magic
import aiofiles
from pathlib import Path
import asyncio
from tqdm import tqdm
import hashlib
from ..services.file_processor import FileProcessor
from bson import ObjectId

router = APIRouter(prefix="/files", tags=["files"])

# 配置
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
CHUNK_SIZE = 1024 * 1024  # 1MB
CONCURRENT_UPLOADS = 3  # 并发上传数量

# 支持的文件类型
SUPPORTED_MIME_TYPES = {
    'image/jpeg': '.jpg',
    'image/png': '.png',
    'application/pdf': '.pdf',
    'image/heic': '.heic',
    'image/heif': '.heif'
}

# MongoDB连接
client = MongoClient('mongodb://localhost:27017')
db = client.agent_test
files_collection = db.files

# 文件处理器实例
file_processor = FileProcessor(client)

class UploadManager:
    def __init__(self):
        self.upload_dir = Path("uploads/files")
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        self.semaphore = asyncio.Semaphore(CONCURRENT_UPLOADS)
    
    async def save_file(self, file: UploadFile) -> Dict[str, any]:
        """
        保存上传的文件
        """
        # 生成文件ID和唯一文件名
        file_id = str(ObjectId())
        unique_filename = f"{file_id}_{file.filename}"
        file_path = self.upload_dir / unique_filename
        
        # 初始化文件信息
        file_info = {
            "_id": ObjectId(file_id),
            "original_name": file.filename,
            "path": str(file_path),
            "size": 0,
            "hash": hashlib.sha256(),
            "status": "uploading",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        # 保存文件信息到数据库
        files_collection.insert_one(file_info)
        
        try:
            # 分块保存文件
            async with aiofiles.open(file_path, 'wb') as f:
                while chunk := await file.read(CHUNK_SIZE):
                    await f.write(chunk)
                    file_info["hash"].update(chunk)
                    file_info["size"] += len(chunk)
                    
                    if file_info["size"] > MAX_FILE_SIZE:
                        raise HTTPException(
                            status_code=413,
                            detail="文件大小超过限制"
                        )
            
            # 获取文件类型
            mime_type = magic.from_file(str(file_path), mime=True)
            if mime_type not in SUPPORTED_MIME_TYPES:
                raise HTTPException(
                    status_code=415,
                    detail=f"不支持的文件类型: {mime_type}"
                )
            
            # 更新文件信息
            update_data = {
                "status": "uploaded",
                "mime_type": mime_type,
                "hash": file_info["hash"].hexdigest(),
                "extension": SUPPORTED_MIME_TYPES[mime_type],
                "updated_at": datetime.utcnow()
            }
            
            files_collection.update_one(
                {"_id": file_info["_id"]},
                {"$set": update_data}
            )
            
            return {**file_info, **update_data}
            
        except Exception as e:
            # 清理文件和数据库记录
            if file_path.exists():
                file_path.unlink()
            files_collection.delete_one({"_id": file_info["_id"]})
            raise HTTPException(
                status_code=500,
                detail=f"文件上传失败: {str(e)}"
            )

upload_manager = UploadManager()

@router.post("/upload")
async def upload_file(
    background_tasks: BackgroundTasks,
    files: List[UploadFile] = File(...),
) -> List[Dict[str, any]]:
    """
    上传一个或多个文件
    """
    results = []
    
    for file in files:
        async with upload_manager.semaphore:
            # 保存文件
            file_info = await upload_manager.save_file(file)
            
            # 添加后台处理任务
            background_tasks.add_task(
                file_processor.process_file,
                file_info
            )
            
            results.append(file_info)
    
    return results

@router.get("/list")
async def list_files(
    status: Optional[str] = None,
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    sort_by: str = "created_at",
    sort_order: int = -1
) -> Dict[str, any]:
    """
    获取文件列表
    """
    # 构建查询条件
    query = {}
    if status:
        query["status"] = status
    
    # 计算总数
    total = files_collection.count_documents(query)
    
    # 获取分页数据
    files = files_collection.find(query) \
        .sort(sort_by, sort_order) \
        .skip((page - 1) * limit) \
        .limit(limit)
    
    return {
        "total": total,
        "page": page,
        "limit": limit,
        "files": list(files)
    }

@router.get("/{file_id}")
async def get_file(file_id: str) -> Dict[str, any]:
    """
    获取文件详情
    """
    try:
        file_info = files_collection.find_one({"_id": ObjectId(file_id)})
        if not file_info:
            raise HTTPException(
                status_code=404,
                detail="文件不存在"
            )
        return file_info
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取文件信息失败: {str(e)}"
        )

@router.delete("/{file_id}")
async def delete_file(file_id: str) -> Dict[str, str]:
    """
    删除文件
    """
    try:
        file_info = files_collection.find_one({"_id": ObjectId(file_id)})
        if not file_info:
            raise HTTPException(
                status_code=404,
                detail="文件不存在"
            )
        
        # 删除文件
        file_path = Path(file_info["path"])
        if file_path.exists():
            file_path.unlink()
        
        # 删除预览和缩略图
        if "metadata" in file_info:
            metadata = file_info["metadata"]
            if "preview_path" in metadata:
                preview_path = Path(metadata["preview_path"])
                if preview_path.exists():
                    preview_path.unlink()
            if "thumbnail_path" in metadata:
                thumbnail_path = Path(metadata["thumbnail_path"])
                if thumbnail_path.exists():
                    thumbnail_path.unlink()
        
        # 删除数据库记录
        files_collection.delete_one({"_id": ObjectId(file_id)})
        
        return {"message": "文件删除成功"}
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"删除文件失败: {str(e)}"
        ) 
"""
项目初始化脚本
"""

import os
from pathlib import Path


def init_project():
    """初始化项目目录结构"""
    # 创建基本目录
    dirs = [
        "src/api",
        "src/models",
        "src/monitoring",
        "src/config",
        "tests",
        "work/uploads",
        "work/models",
        "work/metrics",
        "work/logs"
    ]
    
    for dir_path in dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    print("项目目录结构已创建")


if __name__ == "__main__":
    init_project() 
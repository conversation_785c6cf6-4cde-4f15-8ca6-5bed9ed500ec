"""
系统资源监控
"""

import asyncio
import psutil
from src.common.metrics import SYSTEM_MEMORY_USAGE, CPU_USAGE
from src.common.logger import get_logger

logger = get_logger("system_metrics")

async def collect_system_metrics():
    """收集系统资源使用指标"""
    while True:
        try:
            # 收集内存使用情况
            memory = psutil.virtual_memory()
            SYSTEM_MEMORY_USAGE.set(memory.used)
            
            # 收集CPU使用情况
            cpu_percent = psutil.cpu_percent(interval=1)
            CPU_USAGE.set(cpu_percent)
            
            logger.debug(f"Memory usage: {memory.used} bytes, CPU usage: {cpu_percent}%")
            
        except Exception as e:
            logger.error(f"Failed to collect system metrics: {str(e)}")
        
        # 每5秒收集一次
        await asyncio.sleep(5)

async def start_metrics_collection():
    """启动系统指标收集"""
    logger.info("Starting system metrics collection")
    await collect_system_metrics()

if __name__ == "__main__":
    asyncio.run(start_metrics_collection()) 
"""
指标收集器

用于收集和导出ONNX Runtime服务的性能指标。
"""

import os
import time
import json
import threading
from pathlib import Path
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
import numpy as np
import psutil


@dataclass
class ModelMetrics:
    """模型指标"""
    version_id: str
    total_requests: int = 0
    error_count: int = 0
    success_rate: float = 100.0
    avg_latency_ms: float = 0.0
    min_latency_ms: float = float('inf')
    max_latency_ms: float = 0.0
    last_error: Optional[str] = None
    last_update: float = 0.0


class MetricsCollector:
    """指标收集器"""
    
    def __init__(
        self,
        metrics_dir: str,
        export_interval: int = 60,
        max_history: int = 1000
    ):
        """
        初始化指标收集器
        
        Args:
            metrics_dir: 指标存储目录
            export_interval: 导出间隔（秒）
            max_history: 最大历史记录数
        """
        self.metrics_dir = Path(metrics_dir)
        self.metrics_dir.mkdir(parents=True, exist_ok=True)
        
        self.export_interval = export_interval
        self.max_history = max_history
        
        self._metrics: Dict[str, ModelMetrics] = {}
        self._inference_history: List[Dict] = []
        self._lock = threading.Lock()
        
        # 启动导出线程
        self._running = True
        self._export_thread = threading.Thread(target=self._export_loop)
        self._export_thread.daemon = True
        self._export_thread.start()
    
    def stop(self):
        """停止导出线程"""
        self._running = False
        if self._export_thread.is_alive():
            self._export_thread.join()
    
    def record_inference(
        self,
        model_version: str,
        start_time: float,
        input_shape: List[int],
        output_shape: List[int],
        error: Optional[str] = None
    ):
        """
        记录推理指标
        
        Args:
            model_version: 模型版本
            start_time: 开始时间戳
            input_shape: 输入形状
            output_shape: 输出形状
            error: 错误信息
        """
        end_time = time.time()
        latency_ms = (end_time - start_time) * 1000
        
        with self._lock:
            # 更新模型指标
            if model_version not in self._metrics:
                self._metrics[model_version] = ModelMetrics(version_id=model_version)
            
            metrics = self._metrics[model_version]
            metrics.total_requests += 1
            
            if error:
                metrics.error_count += 1
                metrics.last_error = error
            
            metrics.success_rate = (
                (metrics.total_requests - metrics.error_count)
                / metrics.total_requests
                * 100
            )
            
            # 更新延迟统计
            if not error:
                metrics.min_latency_ms = min(metrics.min_latency_ms, latency_ms)
                metrics.max_latency_ms = max(metrics.max_latency_ms, latency_ms)
                
                # 计算移动平均
                if metrics.avg_latency_ms == 0:
                    metrics.avg_latency_ms = latency_ms
                else:
                    alpha = 0.1  # 平滑因子
                    metrics.avg_latency_ms = (
                        (1 - alpha) * metrics.avg_latency_ms
                        + alpha * latency_ms
                    )
            
            metrics.last_update = end_time
            
            # 记录详细信息
            self._inference_history.append({
                "timestamp": end_time,
                "model_version": model_version,
                "latency_ms": latency_ms,
                "input_shape": input_shape,
                "output_shape": output_shape,
                "error": error
            })
            
            # 限制历史记录数量
            if len(self._inference_history) > self.max_history:
                self._inference_history = self._inference_history[-self.max_history:]
    
    def get_model_metrics(self, model_version: str) -> Optional[ModelMetrics]:
        """
        获取模型指标
        
        Args:
            model_version: 模型版本
            
        Returns:
            模型指标对象
        """
        with self._lock:
            return self._metrics.get(model_version)
    
    def get_all_metrics(self) -> Dict[str, ModelMetrics]:
        """
        获取所有指标
        
        Returns:
            所有模型的指标
        """
        with self._lock:
            return self._metrics.copy()
    
    def export_prometheus_metrics(self) -> str:
        """
        导出Prometheus格式指标
        
        Returns:
            Prometheus格式的指标字符串
        """
        lines = []
        with self._lock:
            # 添加指标说明
            lines.extend([
                "# HELP onnx_model_requests_total 模型总请求数",
                "# TYPE onnx_model_requests_total counter",
                "# HELP onnx_model_errors_total 模型错误总数",
                "# TYPE onnx_model_errors_total counter",
                "# HELP onnx_model_success_rate 模型成功率",
                "# TYPE onnx_model_success_rate gauge",
                "# HELP onnx_model_latency_ms 模型延迟（毫秒）",
                "# TYPE onnx_model_latency_ms gauge"
            ])
            
            # 添加指标数据
            for version, metrics in self._metrics.items():
                lines.extend([
                    f'onnx_model_requests_total{{version="{version}"}} {metrics.total_requests}',
                    f'onnx_model_errors_total{{version="{version}"}} {metrics.error_count}',
                    f'onnx_model_success_rate{{version="{version}"}} {metrics.success_rate:.2f}',
                    f'onnx_model_latency_ms{{version="{version}",type="min"}} {metrics.min_latency_ms:.2f}',
                    f'onnx_model_latency_ms{{version="{version}",type="avg"}} {metrics.avg_latency_ms:.2f}',
                    f'onnx_model_latency_ms{{version="{version}",type="max"}} {metrics.max_latency_ms:.2f}'
                ])
        
        return "\n".join(lines)
    
    def _export_loop(self):
        """指标导出循环"""
        while self._running:
            try:
                self._export_metrics()
            except Exception as e:
                print(f"导出指标失败: {e}")
            
            time.sleep(self.export_interval)
    
    def _export_metrics(self):
        """导出指标到文件"""
        timestamp = int(time.time())
        
        with self._lock:
            # 导出模型指标
            metrics_file = self.metrics_dir / f"model_metrics_{timestamp}.json"
            with open(metrics_file, "w") as f:
                json.dump(
                    {k: asdict(v) for k, v in self._metrics.items()},
                    f,
                    indent=2
                )
            
            # 导出推理历史
            history_file = self.metrics_dir / f"inference_details_{timestamp}.json"
            with open(history_file, "w") as f:
                json.dump(self._inference_history, f, indent=2)
            
            # 清理旧文件
            for pattern in ["model_metrics_*.json", "inference_details_*.json"]:
                files = sorted(self.metrics_dir.glob(pattern))
                if len(files) > 10:  # 保留最新的10个文件
                    for f in files[:-10]:
                        f.unlink()

    def __del__(self) -> None:
        """析构函数"""
        self.stop() 
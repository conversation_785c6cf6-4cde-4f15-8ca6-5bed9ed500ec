"""
FastAPI应用程序
"""

import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from api.onnx_runtime_router import router as onnx_router
from api.metrics_router import router as metrics_router
from api.file_upload_router import router as file_router


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 创建应用
app = FastAPI(
    title="ONNX Runtime服务",
    description="提供ONNX模型部署和推理服务",
    version="0.1.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(onnx_router)
app.include_router(metrics_router)
app.include_router(file_router)


@app.get("/")
async def root():
    """根路由"""
    return {
        "message": "ONNX Runtime服务正在运行",
        "docs_url": "/docs",
        "metrics_url": "/metrics",
        "files_url": "/files"
    } 
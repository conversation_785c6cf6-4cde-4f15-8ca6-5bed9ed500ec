"""
性能监控路由器
提供性能监控相关的API接口
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse

from src.services.performance_monitoring_service import PerformanceMonitoringService
from src.dependencies import get_performance_monitoring_service
from src.common.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(
    prefix="/monitoring",
    tags=["monitoring"]
)

@router.get("/metrics/system")
async def get_system_metrics(
    service: PerformanceMonitoringService = Depends(get_performance_monitoring_service)
) -> Dict:
    """Get current system metrics"""
    try:
        metrics = await service.collect_system_metrics()
        if not metrics:
            raise HTTPException(status_code=500, detail="Failed to collect system metrics")
        return metrics
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/metrics/database")
async def get_database_metrics(
    service: PerformanceMonitoringService = Depends(get_performance_monitoring_service)
) -> Dict:
    """Get current database metrics"""
    try:
        metrics = await service.collect_database_metrics()
        if not metrics:
            raise HTTPException(status_code=500, detail="Failed to collect database metrics")
        return metrics
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/metrics/cache")
async def get_cache_metrics(
    service: PerformanceMonitoringService = Depends(get_performance_monitoring_service)
) -> Dict:
    """Get current cache metrics"""
    try:
        metrics = await service.collect_cache_metrics()
        if not metrics:
            raise HTTPException(status_code=500, detail="Failed to collect cache metrics")
        return metrics
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/metrics/historical")
async def get_historical_metrics(
    start_time: Optional[datetime] = Query(None, description="Start time for historical data"),
    end_time: Optional[datetime] = Query(None, description="End time for historical data"),
    metric_types: Optional[List[str]] = Query(None, description="Types of metrics to retrieve"),
    service: PerformanceMonitoringService = Depends(get_performance_monitoring_service)
) -> List[Dict]:
    """Get historical performance metrics"""
    try:
        metrics = await service.get_historical_metrics(start_time, end_time, metric_types)
        return metrics
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/analysis")
async def analyze_performance(
    start_time: datetime = Query(..., description="Start time for analysis period"),
    end_time: datetime = Query(..., description="End time for analysis period"),
    service: PerformanceMonitoringService = Depends(get_performance_monitoring_service)
) -> Dict:
    """Analyze performance metrics for a time period"""
    try:
        analysis = await service.analyze_performance(start_time, end_time)
        if "error" in analysis:
            raise HTTPException(status_code=500, detail=analysis["error"])
        return analysis
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/collection/start")
async def start_metrics_collection(
    service: PerformanceMonitoringService = Depends(get_performance_monitoring_service)
) -> JSONResponse:
    """Start collecting performance metrics"""
    try:
        await service.start_collection()
        return JSONResponse(content={"message": "Started performance metrics collection"})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/collection/stop")
async def stop_metrics_collection(
    service: PerformanceMonitoringService = Depends(get_performance_monitoring_service)
) -> JSONResponse:
    """Stop collecting performance metrics"""
    try:
        await service.stop_collection()
        return JSONResponse(content={"message": "Stopped performance metrics collection"})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 
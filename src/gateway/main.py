"""
API网关主模块
"""

from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import logging
import time
from typing import Callable
import traceback

from backend.logger import get_logger
from src.backend.routers import file_processing_api, db_optimization_api, ocr_label
from src.backend.database import connect_to_mongo, close_mongo_connection

logger = get_logger("gateway")

app = FastAPI(title="文档处理API")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册启动和关闭事件
@app.on_event("startup")
async def startup_event():
    await connect_to_mongo()

@app.on_event("shutdown")
async def shutdown_event():
    await close_mongo_connection()

# 注册路由
app.include_router(file_processing_api.router, prefix="/api/documents", tags=["documents"])
app.include_router(db_optimization_api.router, prefix="/api/db", tags=["database"])
app.include_router(ocr_label.router, prefix="/api/ocr", tags=["ocr"])

@app.get("/")
async def root():
    return {"message": "OCR智能文档处理系统API服务"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"} 
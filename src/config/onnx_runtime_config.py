"""
ONNX Runtime配置

提供ONNX Runtime服务的配置管理。
"""

import os
from pathlib import Path
from typing import List, Optional
import yaml
from pydantic import BaseModel, Field


class ModelStorageConfig(BaseModel):
    """模型存储配置"""
    base_dir: str = "work/models"
    versions_dir: str = Field(default="versions", description="版本目录")
    deployments_dir: str = Field(default="deployments", description="部署目录")


class InferenceConfig(BaseModel):
    """推理配置"""
    default_batch_size: int = Field(default=1, description="默认批处理大小")
    enable_dynamic_shapes: bool = Field(default=True, description="是否启用动态形状")
    num_threads: int = Field(default=4, description="线程数")
    enable_mem_pattern: bool = Field(default=True, description="是否启用内存优化")
    enable_mem_reuse: bool = Field(default=True, description="是否启用内存重用")


class LoggingConfig(BaseModel):
    """日志配置"""
    level: str = "INFO"
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日志格式"
    )
    file: Optional[str] = "work/logs/onnx_runtime.log"


class ABTestingConfig(BaseModel):
    """A/B测试配置"""
    default_traffic_split: dict = Field(
        default={
            "primary": 0.9,
            "secondary": 0.1
        },
        description="默认流量分配"
    )
    min_requests: int = Field(default=1000, description="最小请求数阈值")
    metrics_interval: int = Field(default=300, description="指标收集间隔（秒）")


class MonitoringConfig(BaseModel):
    """监控配置"""
    export_interval: int = 60  # 秒
    max_history: int = 1000  # 最大历史记录数
    metrics_dir: str = "work/metrics"


class ONNXRuntimeConfig(BaseModel):
    """ONNX Runtime配置"""
    model_storage: ModelStorageConfig = ModelStorageConfig()
    providers: List[str] = Field(default=["CPUExecutionProvider"], description="执行提供程序")
    inference: InferenceConfig = Field(default_factory=InferenceConfig)
    logging: LoggingConfig = LoggingConfig()
    ab_testing: ABTestingConfig = Field(default_factory=ABTestingConfig)
    monitoring: MonitoringConfig = MonitoringConfig()


def load_config(config_path: Optional[str] = None) -> ONNXRuntimeConfig:
    """
    加载配置

    Args:
        config_path: 配置文件路径，如果为None则使用默认配置

    Returns:
        ONNXRuntimeConfig: 配置对象
    """
    if config_path is None:
        return ONNXRuntimeConfig()
    
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    with open(config_path, "r", encoding="utf-8") as f:
        config_dict = yaml.safe_load(f)
    
    return ONNXRuntimeConfig(**config_dict)


def _create_directories(config: ONNXRuntimeConfig) -> None:
    """
    创建必要的目录

    Args:
        config: 配置对象
    """
    # 创建模型存储目录
    Path(config.model_storage.base_dir).mkdir(parents=True, exist_ok=True)
    Path(os.path.join(
        config.model_storage.base_dir,
        config.model_storage.versions_dir
    )).mkdir(exist_ok=True)
    Path(os.path.join(
        config.model_storage.base_dir,
        config.model_storage.deployments_dir
    )).mkdir(exist_ok=True)

    # 创建日志目录
    Path(os.path.dirname(config.logging.file)).mkdir(parents=True, exist_ok=True)

    # 创建指标存储目录
    if config.monitoring.enable_performance_metrics:
        Path(config.monitoring.metrics_path).mkdir(parents=True, exist_ok=True) 
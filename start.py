#!/usr/bin/env python3
"""
统一的启动脚本
可以同时启动前端和后端服务
"""

import os
import sys
import subprocess
import time
import socket
import psutil
from pathlib import Path
import threading
import uvicorn

def print_colored(text: str, color: str = 'white') -> None:
    """打印彩色文本"""
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'white': '\033[97m',
        'end': '\033[0m'
    }
    print(f"{colors.get(color, colors['white'])}{text}{colors['end']}")

def kill_process_on_port(port: int) -> None:
    """杀掉指定端口的进程"""
    try:
        # 使用lsof命令查找占用端口的进程（对macOS和Linux都有效）
        result = subprocess.run(['lsof', '-i', f':{port}'], capture_output=True, text=True)
        if result.stdout:
            # 提取PID（第二行第2列）
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:  # 确保有进程信息
                pid = lines[1].split()[1]
                print_colored(f"发现端口 {port} 被进程 {pid} 占用，正在终止...", "yellow")
                # 终止进程
                subprocess.run(['kill', '-9', pid])
                print_colored(f"已终止进程 {pid}", "green")
                # 给进程一点时间完全释放端口
                time.sleep(1)
    except subprocess.SubprocessError as e:
        print_colored(f"检查端口 {port} 时出错: {e}", "red")

def run_command(command: str, cwd: str = None) -> tuple[int, str, str]:
    """运行shell命令并返回结果"""
    try:
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=cwd,
            text=True
        )
        stdout, stderr = process.communicate()
        return process.returncode, stdout, stderr
    except Exception as e:
        return 1, "", str(e)

def check_port_available(port: int) -> bool:
    """检查端口是否可用"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('localhost', port))
            s.close()
            return True
        except:
            return False

def check_node_npm() -> bool:
    """检查Node.js和npm是否已安装"""
    print_colored("检查Node.js和npm...", "blue")
    
    # 检查node
    ret_node, stdout_node, _ = run_command("node --version")
    if ret_node != 0:
        print_colored("未检测到Node.js，请先安装Node.js", "red")
        return False
    
    # 检查npm
    ret_npm, stdout_npm, _ = run_command("npm --version")
    if ret_npm != 0:
        print_colored("未检测到npm，请先安装npm", "red")
        return False
    
    print_colored(f"Node.js版本: {stdout_node.strip()}", "green")
    print_colored(f"npm版本: {stdout_npm.strip()}", "green")
    return True

def check_next_config(frontend_dir: str) -> bool:
    """检查和修复next.config.js"""
    config_path = os.path.join(frontend_dir, "next.config.js")
    if not os.path.exists(config_path):
        print_colored("创建next.config.js...", "yellow")
        with open(config_path, "w") as f:
            f.write("""/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  experimental: {
    serverActions: true,
  }
}

module.exports = nextConfig
""")
        return True
    return True

def check_project_structure(frontend_dir: str) -> bool:
    """检查项目结构"""
    # 检查必要的目录
    required_dirs = [
        "src/app",
        "src/components",
        "public"
    ]
    
    for dir_path in required_dirs:
        full_path = os.path.join(frontend_dir, dir_path)
        if not os.path.exists(full_path):
            print_colored(f"创建目录: {dir_path}", "yellow")
            os.makedirs(full_path, exist_ok=True)
    
    # 检查必要的文件
    if not os.path.exists(os.path.join(frontend_dir, "src/app/layout.tsx")):
        print_colored("创建基本的layout.tsx...", "yellow")
        with open(os.path.join(frontend_dir, "src/app/layout.tsx"), "w") as f:
            f.write("""export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh">
      <body>{children}</body>
    </html>
  )
}
""")
    
    if not os.path.exists(os.path.join(frontend_dir, "src/app/page.tsx")):
        print_colored("创建基本的page.tsx...", "yellow")
        with open(os.path.join(frontend_dir, "src/app/page.tsx"), "w") as f:
            f.write("""export default function Home() {
  return (
    <main>
      <h1>欢迎使用</h1>
    </main>
  )
}
""")
    
    return True

def install_dependencies(frontend_dir: str) -> bool:
    """安装前端依赖"""
    print_colored("\n正在检查并安装依赖...", "blue")
    
    # 检查package.json是否存在
    if not os.path.exists(os.path.join(frontend_dir, "package.json")):
        print_colored("错误：找不到package.json文件", "red")
        return False
    
    # 检查node_modules是否存在
    if os.path.exists(os.path.join(frontend_dir, "node_modules")):
        print_colored("node_modules已存在，检查依赖更新...", "green")
        ret, stdout, stderr = run_command("npm install", frontend_dir)
    else:
        print_colored("正在安装依赖，这可能需要几分钟...", "yellow")
        ret, stdout, stderr = run_command("npm install", frontend_dir)
    
    if ret != 0:
        print_colored("依赖安装失败！", "red")
        print_colored(f"错误信息：\n{stderr}", "red")
        return False
    
    print_colored("依赖安装成功！", "green")
    return True

def start_frontend(frontend_dir: str, stop_event: threading.Event) -> None:
    """启动前端开发服务器"""
    print_colored("\n正在启动前端开发服务器...", "blue")

    # 固定使用3000端口
    port = 3000
    if not check_port_available(port):
        kill_process_on_port(port)
        time.sleep(1)
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env["PORT"] = str(port)
        
        process = subprocess.Popen(
            "npm run dev",
            shell=True,
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True,
            env=env
        )

        print_colored(f"\n✨ 前端开发服务器已启动！访问 http://localhost:{port}", "green")

        while not stop_event.is_set():
            output = process.stdout.readline()
            if output:
                print(output.strip())
            if process.poll() is not None:
                break

        process.terminate()
        process.wait()
        print_colored("前端服务器已停止", "yellow")

    except Exception as e:
        print_colored(f"\n前端服务器错误：{str(e)}", "red")
        if 'process' in locals():
            process.terminate()
            process.wait()

def start_backend(project_root: Path, stop_event: threading.Event) -> None:
    """启动后端服务器"""
    print_colored("\n正在启动后端服务器...", "blue")
    
    # 添加项目根目录到路径
    src_path = project_root / "src"
    sys.path.insert(0, str(project_root))
    sys.path.insert(0, str(src_path))

    print_colored(f"项目根目录: {project_root}", "white")
    print_colored(f"源代码目录: {src_path}", "white")

    # 检查并清理后端端口
    BACKEND_PORT = 8000
    if not check_port_available(BACKEND_PORT):
        kill_process_on_port(BACKEND_PORT)
        time.sleep(1)

    try:
        print_colored("正在启动后端服务器...", "blue")
        config = uvicorn.Config(
            "gateway.main:app",
            host="0.0.0.0",
            port=BACKEND_PORT,
            reload=False,
            log_level="info"
        )
        server = uvicorn.Server(config)
        
        # 修改服务器的运行方法，使其可以响应停止事件
        def run_server():
            server.run()
            
        server_thread = threading.Thread(target=run_server)
        server_thread.start()
        
        print_colored(f"\n✨ 后端服务器已启动！访问 http://localhost:{BACKEND_PORT}", "green")
        
        # 等待停止事件
        while not stop_event.is_set():
            time.sleep(1)
            
        # 停止服务器
        server.should_exit = True
        server_thread.join()
        print_colored("后端服务器已停止", "yellow")
        
    except Exception as e:
        print_colored(f"后端服务器启动失败: {str(e)}", "red")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    # 获取项目根目录
    project_root = Path(__file__).parent
    frontend_dir = project_root / "src" / "frontend"
    
    # 检查前端目录是否存在
    if not os.path.exists(frontend_dir):
        print_colored(f"错误：找不到前端目录 {frontend_dir}", "red")
        sys.exit(1)
    
    # 检查环境
    if not check_node_npm():
        sys.exit(1)
    
    # 检查项目结构
    if not check_project_structure(frontend_dir):
        sys.exit(1)
    
    # 检查next.config.js
    if not check_next_config(frontend_dir):
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies(frontend_dir):
        sys.exit(1)

    # 创建停止事件
    stop_event = threading.Event()

    try:
        # 创建并启动前端和后端线程
        frontend_thread = threading.Thread(
            target=start_frontend,
            args=(frontend_dir, stop_event)
        )
        backend_thread = threading.Thread(
            target=start_backend,
            args=(project_root, stop_event)
        )

        frontend_thread.start()
        backend_thread.start()

        print_colored("\n🚀 服务已启动！按 Ctrl+C 停止所有服务\n", "green")

        # 等待用户中断
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print_colored("\n正在停止所有服务...", "yellow")
            stop_event.set()
            frontend_thread.join()
            backend_thread.join()
            print_colored("所有服务已停止", "green")

    except Exception as e:
        print_colored(f"\n发生错误：{str(e)}", "red")
        stop_event.set()
        if 'frontend_thread' in locals() and frontend_thread.is_alive():
            frontend_thread.join()
        if 'backend_thread' in locals() and backend_thread.is_alive():
            backend_thread.join()
        sys.exit(1)

if __name__ == "__main__":
    main() 
{"name": "sake-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-tabs": "^1.0.4", "@types/uuid": "^10.0.0", "clsx": "^2.1.1", "heic2any": "^0.0.4", "lucide-react": "^0.515.0", "next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.1", "task-master-ai": "^0.16.2", "uuid": "^11.1.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "postcss": "^8.5.5", "tailwindcss": "^3.4.17", "typescript": "^5.3.3"}}
groups:
  - name: ocr_system_alerts
    rules:
      # 系统资源告警
      - alert: HighCPUUsage
        expr: cpu_usage > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "高CPU使用率"
          description: "CPU使用率超过80%已持续5分钟"

      - alert: HighMemoryUsage
        expr: system_memory_usage / 1024 / 1024 / 1024 > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "高内存使用率"
          description: "内存使用率超过80%已持续5分钟"

      # 队列告警
      - alert: LargeQueueSize
        expr: queue_size{queue="batch_processing"} > 1000
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "队列积压"
          description: "批处理队列中有超过1000个任务待处理"

      # API性能告警
      - alert: HighAPILatency
        expr: rate(api_request_latency_seconds_sum[5m]) / rate(api_request_latency_seconds_count[5m]) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "API响应延迟过高"
          description: "API平均响应时间超过1秒"

      # 错误率告警
      - alert: HighErrorRate
        expr: rate(file_process_total{status="failed"}[5m]) / rate(file_process_total{status=~"failed|success"}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "高错误率"
          description: "文件处理错误率超过10%"

      # 批处理任务告警
      - alert: StuckBatchTask
        expr: batch_task_progress < 1 and (time() - batch_task_start_time) > 3600
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "批处理任务卡住"
          description: "批处理任务运行超过1小时但尚未完成" 
version: '3.8'

services:
  # PaddleLabel服务
  paddlelabel:
    image: paddlepaddle/paddlelabel:latest
    container_name: paddlelabel
    restart: unless-stopped
    ports:
      - "8089:8089"
    volumes:
      - ./data/paddlelabel:/app/data
    environment:
      - PADDLE_LABEL_PORT=8089
      - PADDLE_LABEL_HOST=0.0.0.0
    networks:
      - app_network

  # API网关服务
  gateway:
    build:
      context: .
      dockerfile: docker/gateway/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - MONGODB_URI=mongodb://mongodb:27017/agent_test
      - REDIS_URI=redis://redis:6379
      - RABBITMQ_URI=amqp://rabbitmq:5672
    depends_on:
      - mongodb
      - redis
      - rabbitmq

  # 文件上传服务
  upload:
    build:
      context: .
      dockerfile: docker/upload/Dockerfile
    environment:
      - MONGODB_URI=mongodb://mongodb:27017
      - MINIO_ENDPOINT=minio:9000
      - RABBITMQ_URI=amqp://rabbitmq:5672
    depends_on:
      - mongodb
      - minio
      - rabbitmq

  # 文件管理服务
  file-management:
    build:
      context: .
      dockerfile: docker/file-management/Dockerfile
    environment:
      - MONGODB_URI=mongodb://mongodb:27017
      - REDIS_URI=redis://redis:6379
    depends_on:
      - mongodb
      - redis

  # OCR服务
  ocr:
    build:
      context: .
      dockerfile: docker/ocr/Dockerfile
    environment:
      - MONGODB_URI=mongodb://mongodb:27017
      - RABBITMQ_URI=amqp://rabbitmq:5672
    depends_on:
      - mongodb
      - rabbitmq

  # 质量检测服务
  quality-check:
    build:
      context: .
      dockerfile: docker/quality-check/Dockerfile
    environment:
      - MONGODB_URI=mongodb://mongodb:27017
      - RABBITMQ_URI=amqp://rabbitmq:5672
    depends_on:
      - mongodb
      - rabbitmq

  # 批处理服务
  batch_service:
    build:
      context: .
      dockerfile: docker/batch_service/Dockerfile
    environment:
      - MONGODB_URI=mongodb://mongodb:27017/agent_test
      - RABBITMQ_URI=amqp://rabbitmq:5672
      - REDIS_URI=redis://redis:6379
    depends_on:
      - mongodb
      - rabbitmq
      - redis

  # 批处理消费者
  batch_consumer:
    build:
      context: .
      dockerfile: docker/batch_consumer/Dockerfile
    environment:
      - MONGODB_URI=mongodb://mongodb:27017/agent_test
      - RABBITMQ_URI=amqp://rabbitmq:5672
      - REDIS_URI=redis://redis:6379
    depends_on:
      - mongodb
      - rabbitmq
      - redis

  # MongoDB数据库
  mongodb:
    image: mongo:latest
    container_name: mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    volumes:
      - ./data/mongodb:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
    networks:
      - app_network

  # Redis缓存
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

  # MinIO对象存储
  minio:
    image: minio/minio
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    command: server /data --console-address ":9001"

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - ./docker/prometheus/rules:/etc/prometheus/rules
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'

  # Grafana监控面板
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    volumes:
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
      - grafana_data:/var/lib/grafana
    depends_on:
      - prometheus

  # AlertManager告警管理
  alertmanager:
    image: prom/alertmanager:latest
    ports:
      - "9093:9093"
    volumes:
      - ./docker/alertmanager:/etc/alertmanager
    command:
      - '--config.file=/etc/alertmanager/config.yml'
      - '--storage.path=/alertmanager'

  # Node Exporter系统指标收集
  node_exporter:
    image: prom/node-exporter:latest
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|host|etc)($$|/)'

networks:
  app_network:
    driver: bridge

volumes:
  mongodb_data:
  redis_data:
  rabbitmq_data:
  minio_data:
  prometheus_data:
  grafana_data:
  paddlelabel_data:
  uploads: 
#!/usr/bin/env python3
"""
创建测试图片文件
"""

import sys
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import os

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

def create_test_image(filename, text, size=(800, 600)):
    """创建测试图片"""
    # 创建白色背景图片
    img = Image.new('RGB', size, color='white')
    draw = ImageDraw.Draw(img)
    
    # 尝试使用系统字体
    try:
        # 在macOS上尝试使用系统字体
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 40)
    except:
        try:
            # 尝试其他常见字体
            font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", 40)
        except:
            # 使用默认字体
            font = ImageFont.load_default()
    
    # 绘制文本
    text_bbox = draw.textbbox((0, 0), text, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2
    
    draw.text((x, y), text, fill='black', font=font)
    
    # 绘制边框
    draw.rectangle([10, 10, size[0]-10, size[1]-10], outline='blue', width=3)
    
    return img

def main():
    # 确保上传目录存在
    uploads_dir = Path("data/uploads")
    uploads_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建测试图片
    test_images = [
        ("test1.png", "测试文档1\nOCR识别结果"),
        ("test2.png", "测试文档2\n文档处理完成"),
        ("test3.png", "测试文档3\n标注图片示例")
    ]
    
    for filename, text in test_images:
        img_path = uploads_dir / filename
        img = create_test_image(filename, text)
        img.save(img_path)
        print(f"✅ 创建测试图片: {img_path}")
    
    print(f"\n📁 测试图片已保存到: {uploads_dir}")
    print("🔗 图片访问URL:")
    for filename, _ in test_images:
        print(f"   http://localhost:8000/uploads/{filename}")

if __name__ == "__main__":
    main()

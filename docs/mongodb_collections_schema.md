# MongoDB Collections Schema Documentation

## 数据库概览
- **数据库名称**: agent_test
- **集合数量**: 7个核心集合
- **数据存储**: 文档型存储，支持复杂嵌套结构
- **索引策略**: 高性能复合索引和时间范围索引

## 集合详细设计

### 1. ocr_files Collection
**用途**: 存储上传的原始文件信息和处理状态

**Schema结构**:
```javascript
{
  _id: ObjectId,                    // MongoDB自动生成的主键
  file_id: String,                  // 唯一文件标识符 (UUID)
  original_filename: String,        // 原始文件名
  file_path: String,                // 文件存储路径
  file_size: Number,                // 文件大小（字节）
  file_type: String,                // 文件类型 (jpg, png, pdf, heic, heif)
  mime_type: String,                // MIME类型
  file_hash: String,                // 文件MD5哈希值，用于去重
  upload_time: Date,                // 上传时间
  status: String,                   // 处理状态: pending, processing, completed, failed
  metadata: {                       // 文件元数据
    width: Number,                  // 图像宽度
    height: Number,                 // 图像高度
    pages: Number,                  // PDF页数（如果是PDF）
    format: String,                 // 图像格式详细信息
    color_mode: String              // 颜色模式 (RGB, CMYK, Grayscale)
  },
  processing_history: [{            // 处理历史记录
    timestamp: Date,                // 处理时间戳
    status: String,                 // 当时的状态
    message: String,                // 状态描述信息
    processing_time: Number         // 处理耗时（毫秒）
  }],
  created_at: Date,                 // 创建时间
  updated_at: Date                  // 最后更新时间
}
```

**索引设计**:
```javascript
// 唯一索引
{ file_id: 1 }                      // 唯一文件标识符
{ file_hash: 1 }                    // 文件哈希，防止重复上传

// 复合索引
{ status: 1, upload_time: -1 }      // 按状态和时间查询
{ file_type: 1, created_at: -1 }    // 按文件类型和创建时间查询
```

### 2. ocr_results Collection
**用途**: 存储OCR识别的详细结果

**Schema结构**:
```javascript
{
  _id: ObjectId,                    // MongoDB自动生成的主键
  result_id: String,                // 唯一结果标识符 (UUID)
  file_id: String,                  // 关联的文件ID
  annotation_id: String,            // 关联的标注ID（可选）
  processing_type: String,          // 处理类型: basic, annotated, enhanced
  page_number: Number,              // 页码（从1开始）
  regions: [{                       // 识别区域数组
    region_id: String,              // 区域唯一标识符
    region_type: String,            // 区域类型 (title, paragraph, table, etc.)
    bbox: {                         // 边界框坐标
      x: Number,                    // 左上角X坐标
      y: Number,                    // 左上角Y坐标
      width: Number,                // 宽度
      height: Number                // 高度
    },
    text: String,                   // 识别的文本内容
    confidence: Number,             // 置信度 (0-1)
    order: Number,                  // 阅读顺序
    raw_ocr_results: [{             // 原始OCR结果
      text: String,                 // 原始识别文本
      confidence: Number,           // 原始置信度
      bbox: [Number],               // 原始边界框 [x, y, width, height]
      char_boxes: [[Number]]        // 字符级边界框（可选）
    }],
    preprocessing_params: {         // 预处理参数
      clahe_clip_limit: Number,     // CLAHE限制值
      adaptive_threshold_block_size: Number,  // 自适应阈值块大小
      adaptive_threshold_c: Number, // 自适应阈值常数
      morphology_kernel_size: [Number],       // 形态学核大小
      morphology_operation: String  // 形态学操作类型
    }
  }],
  processing_stats: {               // 处理统计信息
    total_regions: Number,          // 总区域数
    avg_confidence: Number,         // 平均置信度
    processing_time: Number,        // 处理时间（毫秒）
    ocr_engine_version: String      // OCR引擎版本
  },
  created_at: Date,                 // 创建时间
  updated_at: Date                  // 最后更新时间
}
```

**索引设计**:
```javascript
// 唯一索引
{ result_id: 1 }                    // 唯一结果标识符

// 复合索引
{ file_id: 1, page_number: 1 }      // 按文件和页码查询
{ processing_type: 1, created_at: -1 }  // 按处理类型和时间查询
{ "regions.region_type": 1 }        // 按区域类型查询
```

### 3. ocr_annotations Collection
**用途**: 存储人工标注的区域信息

**Schema结构**:
```javascript
{
  _id: ObjectId,                    // MongoDB自动生成的主键
  annotation_id: String,            // 唯一标注标识符 (UUID)
  file_id: String,                  // 关联的文件ID
  annotation_name: String,          // 标注名称
  page_number: Number,              // 页码（从1开始）
  regions: [{                       // 标注区域数组
    region_id: String,              // 区域唯一标识符
    region_type: String,            // 区域类型 (title, paragraph, table, etc.)
    bbox: {                         // 边界框坐标
      x: Number,                    // 左上角X坐标
      y: Number,                    // 左上角Y坐标
      width: Number,                // 宽度
      height: Number                // 高度
    },
    order: Number,                  // 阅读顺序
    confidence: Number,             // 标注置信度
    notes: String,                  // 标注备注（可选）
    created_by: String              // 标注者（可选）
  }],
  template_info: {                  // 模板信息
    is_template: Boolean,           // 是否为模板
    template_name: String,          // 模板名称
    template_description: String,   // 模板描述
    applicable_file_types: [String] // 适用的文件类型
  },
  created_at: Date,                 // 创建时间
  updated_at: Date,                 // 最后更新时间
  created_by: String                // 创建者
}
```

**索引设计**:
```javascript
// 唯一索引
{ annotation_id: 1 }                // 唯一标注标识符

// 复合索引
{ file_id: 1, page_number: 1 }      // 按文件和页码查询
{ "template_info.is_template": 1, "template_info.template_name": 1 }  // 模板查询
{ created_by: 1, created_at: -1 }   // 按创建者和时间查询
```

### 4. ocr_training_samples Collection
**用途**: 存储学习系统的训练样本

**Schema结构**:
```javascript
{
  _id: ObjectId,                    // MongoDB自动生成的主键
  sample_id: String,                // 唯一样本标识符 (UUID)
  file_id: String,                  // 关联的文件ID
  result_id: String,                // 关联的结果ID
  region_id: String,                // 关联的区域ID
  region_type: String,              // 区域类型
  region_bbox: {                    // 区域边界框
    x: Number,                      // 左上角X坐标
    y: Number,                      // 左上角Y坐标
    width: Number,                  // 宽度
    height: Number                  // 高度
  },
  ground_truth_text: String,        // 人工修正的正确文本
  ocr_predicted_text: String,       // OCR识别的文本
  confidence: Number,               // OCR置信度
  preprocessing_params: {           // 预处理参数
    clahe_clip_limit: Number,       // CLAHE限制值
    adaptive_threshold_block_size: Number,  // 自适应阈值块大小
    adaptive_threshold_c: Number,   // 自适应阈值常数
    morphology_kernel_size: [Number],       // 形态学核大小
    morphology_operation: String    // 形态学操作类型
  },
  accuracy_metrics: {               // 准确率指标
    character_accuracy: Number,     // 字符级准确率
    word_accuracy: Number,          // 词级准确率
    edit_distance: Number           // 编辑距离
  },
  image_hash: String,               // 图像哈希值
  feedback_source: String,          // 反馈来源 (manual, automatic)
  created_at: Date,                 // 创建时间
  updated_at: Date                  // 最后更新时间
}
```

**索引设计**:
```javascript
// 唯一索引
{ sample_id: 1 }                    // 唯一样本标识符

// 复合索引
{ region_type: 1, created_at: -1 }  // 按区域类型和时间查询
{ file_id: 1, region_id: 1 }        // 按文件和区域查询
{ feedback_source: 1, "accuracy_metrics.character_accuracy": -1 }  // 按反馈源和准确率查询
```

### 5. ocr_preprocessing_effectiveness Collection
**用途**: 存储不同预处理参数的效果评估

**Schema结构**:
```javascript
{
  _id: ObjectId,                    // MongoDB自动生成的主键
  effectiveness_id: String,         // 唯一效果记录标识符 (UUID)
  region_type: String,              // 区域类型
  preprocessing_params: {           // 预处理参数组合
    clahe_clip_limit: Number,       // CLAHE限制值
    adaptive_threshold_block_size: Number,  // 自适应阈值块大小
    adaptive_threshold_c: Number,   // 自适应阈值常数
    morphology_kernel_size: [Number],       // 形态学核大小
    morphology_operation: String    // 形态学操作类型
  },
  performance_metrics: {            // 性能指标
    avg_accuracy: Number,           // 平均准确率
    avg_confidence: Number,         // 平均置信度
    sample_count: Number,           // 样本数量
    combined_score: Number          // 综合评分
  },
  evaluation_period: {              // 评估周期
    start_date: Date,               // 开始日期
    end_date: Date                  // 结束日期
  },
  created_at: Date,                 // 创建时间
  updated_at: Date                  // 最后更新时间
}
```

**索引设计**:
```javascript
// 唯一索引
{ effectiveness_id: 1 }             // 唯一效果记录标识符

// 复合索引
{ region_type: 1, "performance_metrics.combined_score": -1 }  // 按区域类型和评分查询
{ "evaluation_period.start_date": 1, "evaluation_period.end_date": 1 }  // 按时间范围查询
```

### 6. ocr_error_patterns Collection
**用途**: 存储识别的错误模式和统计信息

**Schema结构**:
```javascript
{
  _id: ObjectId,                    // MongoDB自动生成的主键
  pattern_id: String,               // 唯一错误模式标识符 (UUID)
  region_type: String,              // 区域类型
  error_type: String,               // 错误类型 (substitution, insertion, deletion)
  error_pattern: {                  // 错误模式详情
    original_text: String,          // 原始错误文本
    corrected_text: String,         // 修正后文本
    error_description: String,      // 错误描述
    pattern_regex: String           // 错误模式正则表达式
  },
  statistics: {                     // 统计信息
    frequency: Number,              // 出现频率
    first_occurrence: Date,         // 首次出现时间
    last_occurrence: Date,          // 最后出现时间
    affected_samples: [String]      // 受影响的样本ID列表
  },
  correction_suggestions: [{        // 修正建议
    suggestion: String,             // 建议内容
    confidence: Number,             // 建议置信度
    success_rate: Number            // 成功率
  }],
  created_at: Date,                 // 创建时间
  updated_at: Date                  // 最后更新时间
}
```

**索引设计**:
```javascript
// 唯一索引
{ pattern_id: 1 }                   // 唯一错误模式标识符

// 复合索引
{ region_type: 1, error_type: 1 }   // 按区域类型和错误类型查询
{ "statistics.frequency": -1, "statistics.last_occurrence": -1 }  // 按频率和时间查询
```

### 7. ocr_learning_reports Collection
**用途**: 存储学习系统生成的报告

**Schema结构**:
```javascript
{
  _id: ObjectId,                    // MongoDB自动生成的主键
  report_id: String,                // 唯一报告标识符 (UUID)
  report_type: String,              // 报告类型 (daily, weekly, monthly, custom)
  time_range: {                     // 时间范围
    start_date: Date,               // 开始日期
    end_date: Date                  // 结束日期
  },
  metrics: {                        // 指标数据
    total_samples: Number,          // 总样本数
    avg_accuracy_improvement: Number,        // 平均准确率改进
    avg_confidence_improvement: Number,      // 平均置信度改进
    processing_time_improvement: Number,     // 处理时间改进
    region_performance: [{          // 各区域类型性能
      region_type: String,          // 区域类型
      accuracy: Number,             // 准确率
      confidence: Number,           // 置信度
      sample_count: Number          // 样本数量
    }]
  },
  recommendations: [{               // 推荐建议
    type: String,                   // 建议类型
    description: String,            // 建议描述
    priority: String,               // 优先级 (high, medium, low)
    estimated_impact: Number        // 预估影响
  }],
  generated_at: Date,               // 生成时间
  generated_by: String              // 生成者
}
```

**索引设计**:
```javascript
// 唯一索引
{ report_id: 1 }                    // 唯一报告标识符

// 复合索引
{ report_type: 1, generated_at: -1 }        // 按报告类型和生成时间查询
{ "time_range.start_date": 1, "time_range.end_date": 1 }  // 按时间范围查询
```

## 数据关系图

```
ocr_files (1) -----> (N) ocr_results
    |                       |
    |                       |
    v                       v
ocr_annotations (1) --> (N) ocr_training_samples
                              |
                              v
                        ocr_preprocessing_effectiveness
                              |
                              v
                        ocr_error_patterns
                              |
                              v
                        ocr_learning_reports
```

## 性能优化策略

### 1. 索引优化
- **唯一索引**: 确保数据唯一性和快速查找
- **复合索引**: 支持多字段查询优化
- **时间范围索引**: 优化时间序列查询

### 2. 查询优化
- 使用聚合管道进行复杂分析
- 实现查询结果缓存
- 分页查询大数据集

### 3. 存储优化
- 文档大小控制（避免超过16MB限制）
- 合理的嵌套深度
- 数据压缩和归档策略

### 4. 连接管理
- 连接池配置
- 自动重连机制
- 读写分离（如果使用副本集）

## 数据一致性保障

### 1. 事务支持
- 多文档事务操作
- 原子性保证
- 回滚机制

### 2. 数据验证
- Schema验证
- 数据类型检查
- 业务规则验证

### 3. 备份策略
- 定期全量备份
- 增量备份
- 异地备份

## 扩展性设计

### 1. 水平扩展
- 分片策略
- 分片键选择
- 负载均衡

### 2. 垂直扩展
- 硬件升级
- 内存优化
- CPU优化

### 3. 监控和维护
- 性能监控
- 慢查询分析
- 容量规划 
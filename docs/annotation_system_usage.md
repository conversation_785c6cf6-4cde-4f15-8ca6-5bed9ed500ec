# OCR人工标注系统使用说明

## 系统概述

OCR人工标注系统是一个基于Web的智能文档识别平台，集成了先进的OCR技术和人工标注干预功能，能够处理复杂布局的文档并提供高精度的文字识别结果。

## 主要功能

### 1. 智能图片上传
- **支持格式**：JPG、PNG、HEIC、HEIF、PDF等多种格式
- **自动预处理**：图像尺寸调整、格式转换
- **实时预览**：上传后立即显示图片内容

### 2. 人工标注功能
- **交互式标注**：鼠标拖拽绘制识别区域
- **多种区域类型**：
  - 单栏文本 (按键1)
  - 左栏文本 (按键2) 
  - 右栏文本 (按键3)
  - 表格 (按键4)
  - 图像 (按键5)
  - 页眉 (按键6)
  - 页脚 (按键7)
  - 特殊区域 (按键8)

### 3. OCR识别模式
- **默认OCR**：无需标注，自动识别整个图片
- **标注OCR**：基于用户标注的区域进行精确识别
- **批量处理**：支持多文件同时处理

### 4. 结果管理
- **结构化输出**：按区域类型和阅读顺序组织文本
- **置信度显示**：每个区域的识别置信度
- **多格式导出**：TXT、JSON等格式
- **标注保存**：标注信息可保存为模板重复使用

## 使用流程

### 步骤1：访问系统
1. 打开浏览器访问 `http://localhost:3000`
2. 点击导航栏中的"OCR标注"进入标注页面

### 步骤2：上传图片
1. 点击"上传图片"按钮
2. 选择要处理的图片文件
3. 系统自动加载并显示图片

### 步骤3：选择处理模式

#### 默认OCR模式
- 直接点击"默认OCR"按钮
- 系统自动识别整个图片内容
- 适用于简单布局的文档

#### 人工标注模式
1. **选择区域类型**：点击区域类型按钮或使用数字键1-8
2. **绘制标注区域**：
   - 在图片上按住鼠标左键拖拽
   - 绘制矩形框覆盖要识别的区域
   - 松开鼠标完成标注
3. **重复标注**：为不同区域重复上述步骤
4. **调整标注**：
   - 按U键撤销最后一个标注
   - 按C键清除所有标注
   - 点击垃圾桶图标删除特定标注
5. **执行识别**：点击"标注OCR"按钮开始处理

### 步骤4：查看结果
1. **结果展示**：右侧面板显示识别结果
2. **区域信息**：每个结果包含区域类型、顺序和置信度
3. **文本内容**：显示识别出的文字内容

### 步骤5：导出和保存
1. **导出结果**：点击"导出结果"下载TXT文件
2. **保存标注**：点击"保存标注"下载JSON标注文件
3. **标注模板**：保存的标注可用于相似文档的快速处理

## 操作技巧

### 键盘快捷键
- **数字键1-8**：快速切换区域类型
- **U键**：撤销最后一个标注
- **C键**：清除所有标注

### 标注最佳实践
1. **从上到下，从左到右**：按阅读顺序进行标注
2. **区域不重叠**：避免标注区域相互重叠
3. **边界准确**：标注框应紧贴文字边界
4. **类型匹配**：选择正确的区域类型以获得最佳识别效果

### 复杂布局处理
1. **多栏文档**：
   - 分别标注左栏和右栏
   - 注意保持正确的阅读顺序
2. **表格内容**：
   - 使用表格类型标注
   - 可以按行或按列进行标注
3. **图文混排**：
   - 文字区域使用文本类型
   - 图片区域使用图像类型
   - 图片中的文字可单独标注

## 故障排除

### 常见问题

#### 1. 图片无法上传
- **检查格式**：确保文件格式受支持
- **检查大小**：过大的文件可能需要压缩
- **网络连接**：确保网络连接正常

#### 2. OCR识别效果不佳
- **图片质量**：使用高清晰度的图片
- **标注精度**：确保标注框准确覆盖文字区域
- **区域类型**：选择正确的区域类型

#### 3. 标注操作不响应
- **浏览器兼容性**：建议使用Chrome、Firefox等现代浏览器
- **页面刷新**：尝试刷新页面重新开始
- **清除缓存**：清除浏览器缓存后重试

### 性能优化建议
1. **图片预处理**：上传前适当压缩图片大小
2. **分批处理**：大量文档建议分批处理
3. **标注复用**：保存常用的标注模板

## 技术支持

### 系统要求
- **浏览器**：Chrome 80+、Firefox 75+、Safari 13+
- **网络**：稳定的网络连接
- **设备**：支持鼠标操作的设备

### 联系方式
如遇到技术问题，请通过以下方式联系：
- 查看系统日志：浏览器开发者工具Console面板
- 检查服务状态：访问 `http://localhost:8000/ocr/health`

## 更新日志

### v1.0.0 (当前版本)
- ✅ 基础OCR识别功能
- ✅ 人工标注系统
- ✅ 多格式图片支持
- ✅ 结果导出功能
- ✅ 标注模板保存

### 计划功能
- 🔄 批量处理优化
- 🔄 AI辅助标注
- 🔄 云端存储集成
- 🔄 移动端适配

---

*最后更新：2024年12月* 
# MongoDB 配置说明

## 概述

本项目使用 MongoDB 作为主要数据存储，通过 `.env` 文件进行配置。配置系统基于 `pydantic_settings`，支持环境变量自动读取和类型验证。

## 配置文件位置

- **配置文件**: `src/database/config.py`
- **环境变量文件**: `.env`（项目根目录）
- **示例配置**: 参考下方配置示例

## 环境变量配置

在项目根目录创建 `.env` 文件，配置以下参数：

### 基本配置（必需）

```bash
# MongoDB 连接URL
MONGODB_URL=mongodb://localhost:55142/

# 数据库名称
MONGODB_DATABASE=agent_test
```

### 认证配置（可选）

如果您的 MongoDB 需要认证：

```bash
# 用户名和密码
MONGODB_USERNAME=your_username
MONGODB_PASSWORD=your_password
```

### 连接池配置（可选）

```bash
# 连接池最大连接数
MONGODB_MAX_POOL_SIZE=100

# 连接池最小连接数
MONGODB_MIN_POOL_SIZE=10

# 连接最大空闲时间（毫秒）
MONGODB_MAX_IDLE_TIME_MS=30000

# 连接超时时间（毫秒）
MONGODB_CONNECT_TIMEOUT_MS=10000

# 服务器选择超时时间（毫秒）
MONGODB_SERVER_SELECTION_TIMEOUT_MS=5000
```

### 集合名称配置（可选）

```bash
# 自定义集合名称（默认值已经很好，通常不需要修改）
OCR_FILES_COLLECTION=ocr_files
OCR_RESULTS_COLLECTION=ocr_results
OCR_ANNOTATIONS_COLLECTION=ocr_annotations
OCR_TRAINING_SAMPLES_COLLECTION=ocr_training_samples
OCR_PREPROCESSING_EFFECTIVENESS_COLLECTION=ocr_preprocessing_effectiveness
OCR_ERROR_PATTERNS_COLLECTION=ocr_error_patterns
OCR_LEARNING_REPORTS_COLLECTION=ocr_learning_reports
```

## 常见配置场景

### 1. 本地开发环境

```bash
MONGODB_URL=mongodb://localhost:55142/
MONGODB_DATABASE=agent_test
```

### 2. MongoDB Atlas 云服务

```bash
MONGODB_URL=mongodb+srv://username:<EMAIL>/
MONGODB_DATABASE=agent_test
```

### 3. 自定义端口

```bash
MONGODB_URL=mongodb://localhost:55142/
MONGODB_DATABASE=agent_test
```

### 4. 副本集配置

```bash
MONGODB_URL=mongodb://host1:27017,host2:27017,host3:27017/?replicaSet=myReplicaSet
MONGODB_DATABASE=agent_test
```

### 5. 带认证的本地环境

```bash
MONGODB_URL=mongodb://localhost:55142/
MONGODB_DATABASE=agent_test
MONGODB_USERNAME=admin
MONGODB_PASSWORD=your_secure_password
```

## 配置验证

### 检查配置是否正确

运行以下脚本验证配置：

```bash
# 初始化数据库（会显示连接信息）
python3 scripts/init_mongodb.py

# 创建集合和索引（会显示连接URL）
python3 scripts/create_mongodb_collections.py
```

### 配置类使用示例

```python
from src.database.config import db_settings

# 获取完整的连接URL（包含认证信息）
connection_url = db_settings.get_mongodb_url()

# 获取数据库名称
database_name = db_settings.MONGODB_DATABASE

# 获取集合名称
files_collection = db_settings.OCR_FILES_COLLECTION
```

## 安全注意事项

1. **不要提交 `.env` 文件到版本控制系统**
   - `.env` 文件已添加到 `.gitignore`
   - 包含敏感信息如密码和连接字符串

2. **生产环境建议**
   - 使用强密码
   - 启用 MongoDB 认证
   - 配置防火墙规则
   - 使用 TLS/SSL 加密连接

3. **连接字符串安全**
   - 避免在日志中输出完整连接字符串
   - 使用环境变量而不是硬编码

## 故障排除

### 常见错误

1. **连接被拒绝**
   ```
   pymongo.errors.ServerSelectionTimeoutError
   ```
   - 检查 MongoDB 服务是否启动
   - 验证连接URL和端口是否正确

2. **认证失败**
   ```
   pymongo.errors.OperationFailure: Authentication failed
   ```
   - 检查用户名和密码是否正确
   - 确认用户有访问指定数据库的权限

3. **数据库不存在**
   - MongoDB 会自动创建不存在的数据库
   - 确保有足够的权限创建数据库

### 调试技巧

1. **启用详细日志**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **测试连接**
   ```python
   from pymongo import MongoClient
   from src.database.config import db_settings
   
   client = MongoClient(db_settings.get_mongodb_url())
   print(client.server_info())
   ```

## 相关文档

- [MongoDB 数据模型设计](mongodb_data_model.md)
- [MongoDB 实现总结](mongodb_implementation_summary.md)
- [数据库初始化脚本使用说明](../scripts/init_mongodb.py)
- [集合创建脚本使用说明](../scripts/create_mongodb_collections.py) 
# OCR人工标注系统使用指南

## 概述

人工标注系统允许用户通过交互式界面手动圈画和标注文档区域，从而大大提高OCR处理复杂布局文档的准确性。系统包含两个主要组件：

1. **交互式标注工具** - 用于手动标注文档区域
2. **基于标注的OCR处理器** - 利用标注信息进行精确OCR

## 系统架构

```
图像输入 → 交互式标注 → 标注文件(.json) → 基于标注的OCR → 结构化输出
```

## 功能特性

### 交互式标注工具

- **多种区域类型支持**：
  - 单栏文本 (single_column)
  - 左栏文本 (left_column) 
  - 右栏文本 (right_column)
  - 表格 (table)
  - 图像 (image)
  - 页眉 (header)
  - 页脚 (footer)
  - 特殊区域 (special)

- **交互式操作**：
  - 鼠标拖拽绘制矩形区域
  - 键盘快捷键切换区域类型
  - 实时预览和编辑
  - 撤销/重做功能

- **格式支持**：
  - 支持HEIC/HEIF格式（iPhone照片）
  - 支持常见图像格式（JPG、PNG、BMP等）
  - 自动缩放大图像以适应屏幕

### 基于标注的OCR处理器

- **区域特定处理**：
  - 根据区域类型选择最佳预处理策略
  - 表格结构重建
  - 列文本特殊处理
  - 图像区域文字提取

- **结构化输出**：
  - 按阅读顺序组织文本
  - 保留区域类型信息
  - 提供置信度评估
  - 生成多种格式输出

## 安装和设置

### 依赖安装

```bash
# 安装Python依赖
pip3 install -r requirements.txt

# 确保安装了OpenCV的GUI支持
pip3 install opencv-python[headless]==false
```

### 系统要求

- Python 3.8+
- OpenCV with GUI support
- 足够的内存处理大图像
- 显示器（用于交互式标注）

## 使用方法

### 方法1：使用测试脚本（推荐）

```bash
# 完整流程（标注 + OCR）
python3 test_annotation_system.py pic/IMG_1320.heic

# 仅进行标注
python3 test_annotation_system.py pic/IMG_1320.heic annotate

# 仅进行OCR（需要已有标注文件）
python3 test_annotation_system.py pic/IMG_1320.heic ocr
```

### 方法2：直接使用组件

#### 交互式标注

```bash
cd src/ocr
python3 interactive_annotator.py ../../pic/IMG_1320.heic
```

#### 基于标注的OCR

```bash
cd src/ocr  
python3 annotation_guided_ocr.py ../../pic/IMG_1320.heic
```

### 方法3：在代码中使用

```python
from src.ocr.interactive_annotator import InteractiveAnnotator
from src.ocr.annotation_guided_ocr import AnnotationGuidedOCR

# 创建标注
annotator = InteractiveAnnotator()
annotator.start_annotation("image.jpg")

# 基于标注进行OCR
guided_ocr = AnnotationGuidedOCR()
results = guided_ocr.process_with_annotations("image.jpg")
```

## 操作指南

### 交互式标注操作

#### 鼠标操作
- **左键拖拽**：绘制矩形区域
- **拖拽过程中**：实时显示当前区域类型和边界

#### 键盘快捷键
- **数字键 1-8**：切换区域类型
  - `1` - 单栏文本
  - `2` - 左栏文本
  - `3` - 右栏文本
  - `4` - 表格
  - `5` - 图像
  - `6` - 页眉
  - `7` - 页脚
  - `8` - 特殊区域

- **功能键**：
  - `u` - 撤销上一个区域
  - `c` - 清除所有区域
  - `s` - 保存标注
  - `h` - 显示帮助
  - `ESC` - 退出程序

#### 标注策略建议

1. **按阅读顺序标注**：从上到下，从左到右
2. **区域不要重叠**：避免同一文字被多个区域包含
3. **边界要准确**：确保文字完全在区域内
4. **选择合适的区域类型**：
   - 单栏文本：普通段落
   - 左栏/右栏：多栏布局的各列
   - 表格：结构化数据
   - 图像：包含文字的图片区域
   - 页眉/页脚：文档头尾信息
   - 特殊：其他特殊格式区域

### 复杂布局处理技巧

#### 多栏布局
```
[页眉区域]
[单栏文本区域1]
[左栏区域] [右栏区域]
[单栏文本区域2]
[页脚区域]
```

#### 表格处理
- 将整个表格作为一个区域标注
- 系统会自动尝试重建表格结构
- 复杂表格可能需要手动后处理

#### 图文混排
- 纯文字区域：使用对应的文本区域类型
- 包含文字的图片：使用图像区域类型
- 图片说明：可以单独标注为特殊区域

## 输出文件说明

### 标注文件 (.json)
```json
{
  "image_path": "pic/IMG_1320.heic",
  "image_width": 2268,
  "image_height": 4032,
  "regions": [
    {
      "id": "region_1",
      "x": 100,
      "y": 200,
      "width": 800,
      "height": 150,
      "region_type": "single_column",
      "label": "",
      "confidence": 1.0,
      "order": 1
    }
  ],
  "created_at": "2024-01-01T12:00:00",
  "modified_at": "2024-01-01T12:00:00"
}
```

### OCR结果文件 (.json)
```json
{
  "total_regions": 5,
  "processed_at": "2024-01-01T12:00:00",
  "results": [
    {
      "region_id": "region_1",
      "region_type": "single_column",
      "order": 1,
      "bbox": [100, 200, 800, 150],
      "text": "识别出的文字内容...",
      "confidence": 0.95,
      "raw_ocr_count": 3
    }
  ]
}
```

### 结构化文本文件 (.txt)
```
=== SINGLE_COLUMN (区域 1) ===
识别出的文字内容...

=== LEFT_COLUMN (区域 2) ===
左栏文字内容...

=== RIGHT_COLUMN (区域 3) ===
右栏文字内容...
```

## 高级功能

### 标注文件复用
- 相似布局的文档可以复用标注文件
- 复制.json文件并重命名即可
- 适用于批量处理相同格式的文档

### 批量处理
```python
import glob
from pathlib import Path

# 批量处理相同布局的图片
template_annotation = "template.json"
guided_ocr = AnnotationGuidedOCR()

for image_path in glob.glob("images/*.jpg"):
    # 复制模板标注文件
    annotation_path = Path(image_path).with_suffix('.json')
    shutil.copy(template_annotation, annotation_path)
    
    # 处理OCR
    results = guided_ocr.process_with_annotations(image_path)
```

### 自定义预处理
可以通过继承`AnnotationGuidedOCR`类来自定义预处理策略：

```python
class CustomOCR(AnnotationGuidedOCR):
    def _preprocess_text_image(self, image):
        # 自定义文本预处理
        return enhanced_image
    
    def _process_table_region(self, region_image):
        # 自定义表格处理
        return text, confidence, raw_results
```

## 故障排除

### 常见问题

#### 1. 标注窗口无法显示
```bash
# 检查OpenCV GUI支持
python3 -c "import cv2; print(cv2.getBuildInformation())"

# 重新安装OpenCV
pip3 uninstall opencv-python
pip3 install opencv-python
```

#### 2. HEIC格式无法打开
```bash
# 安装HEIC支持
pip3 install pillow-heif

# 在macOS上可能需要额外设置
brew install libheif
```

#### 3. OCR识别效果差
- 检查图像质量和分辨率
- 调整区域边界，确保文字完全包含
- 尝试不同的区域类型
- 检查图像预处理效果

#### 4. 内存不足
- 处理大图像时可能出现内存问题
- 可以在代码中添加图像缩放
- 或者预先缩放图像

### 性能优化

#### 图像预处理优化
```python
# 在标注前预先缩放大图像
def resize_large_image(image_path, max_size=2000):
    image = cv2.imread(image_path)
    height, width = image.shape[:2]
    
    if max(height, width) > max_size:
        scale = max_size / max(height, width)
        new_width = int(width * scale)
        new_height = int(height * scale)
        image = cv2.resize(image, (new_width, new_height))
        
    return image
```

#### 批量处理优化
- 使用多进程处理多个图像
- 预加载OCR模型避免重复初始化
- 合理设置批处理大小

## 最佳实践

### 标注质量
1. **一致性**：同类型区域使用相同的标注方式
2. **完整性**：确保所有重要文字都被标注
3. **准确性**：区域边界要精确，避免切断文字
4. **顺序性**：按照阅读顺序进行标注

### 工作流程
1. **预览图像**：先整体浏览，了解布局结构
2. **规划标注**：确定区域划分策略
3. **逐步标注**：按顺序标注各个区域
4. **检查验证**：完成后检查标注质量
5. **测试OCR**：运行OCR验证效果
6. **调整优化**：根据结果调整标注

### 文档管理
- 为每个项目创建专门的目录
- 使用有意义的文件命名
- 保留原始图像和所有中间文件
- 建立标注模板库供复用

## 扩展开发

### 添加新的区域类型
```python
# 在interactive_annotator.py中添加
self.region_colors['new_type'] = (R, G, B)

# 在annotation_guided_ocr.py中添加处理逻辑
def _process_new_type_region(self, region_image):
    # 自定义处理逻辑
    pass
```

### 集成到Web界面
可以将标注功能集成到Web界面中：
- 使用Canvas API实现网页版标注
- 通过WebSocket与后端OCR服务通信
- 提供在线协作标注功能

### API接口开发
```python
from fastapi import FastAPI, UploadFile

app = FastAPI()

@app.post("/annotate")
async def create_annotation(file: UploadFile):
    # 处理上传的图像文件
    # 返回标注界面或自动标注结果
    pass

@app.post("/ocr")
async def process_ocr(image_file: UploadFile, annotation_file: UploadFile):
    # 基于标注进行OCR处理
    # 返回结构化结果
    pass
```

## 总结

人工标注系统通过结合人工智能和人工干预，显著提高了复杂布局文档的OCR处理质量。系统的模块化设计使其易于扩展和定制，适用于各种文档处理场景。

通过合理使用标注工具和遵循最佳实践，可以实现高质量的文档数字化处理，为后续的文本分析和处理奠定坚实基础。 
# MongoDB 数据模型设计 - agent_test 数据库

## 数据库概述

使用MongoDB作为主要数据存储，数据库名称：`agent_test`

## 集合（Collections）设计

### 1. ocr_files (OCR文件列表)

存储上传的原始文件信息和处理状态。

```javascript
{
  _id: ObjectId,
  file_id: String,           // 唯一文件标识符
  original_filename: String, // 原始文件名
  file_path: String,         // 文件存储路径
  file_size: Number,         // 文件大小（字节）
  file_type: String,         // 文件类型 (jpg, png, pdf, heic, heif)
  mime_type: String,         // MIME类型
  file_hash: String,         // 文件MD5哈希值
  upload_time: Date,         // 上传时间
  status: String,            // 处理状态: pending, processing, completed, failed
  metadata: {
    width: Number,           // 图像宽度
    height: Number,          // 图像高度
    pages: Number,           // PDF页数（如果是PDF）
    format: String,          // 图像格式详细信息
    color_mode: String       // 颜色模式
  },
  processing_history: [{
    timestamp: Date,
    status: String,
    message: String,
    processing_time: Number  // 处理耗时（毫秒）
  }],
  created_at: Date,
  updated_at: Date
}
```

### 2. ocr_results (OCR识别结果表)

存储OCR识别的详细结果。

```javascript
{
  _id: ObjectId,
  result_id: String,         // 唯一结果标识符
  file_id: String,           // 关联的文件ID
  annotation_id: String,     // 关联的标注ID（可选）
  processing_type: String,   // 处理类型: basic, annotated, enhanced
  page_number: Number,       // 页码（从1开始）
  regions: [{
    region_id: String,       // 区域唯一标识符
    region_type: String,     // 区域类型: single_column, left_column, right_column, table, image, header, footer, special
    bbox: {
      x: Number,
      y: Number,
      width: Number,
      height: Number
    },
    text: String,            // 识别的文本
    confidence: Number,      // 置信度 (0-1)
    order: Number,           // 阅读顺序
    raw_ocr_results: [{
      text: String,
      confidence: Number,
      bbox: [Number, Number, Number, Number],
      char_boxes: [[Number, Number, Number, Number]]
    }],
    preprocessing_params: {  // 使用的预处理参数
      clahe_clip_limit: Number,
      adaptive_threshold_block_size: Number,
      adaptive_threshold_c: Number,
      morphology_kernel_size: [Number, Number],
      morphology_operation: String
    }
  }],
  processing_stats: {
    total_regions: Number,
    avg_confidence: Number,
    processing_time: Number,
    ocr_engine_version: String
  },
  created_at: Date,
  updated_at: Date
}
```

### 3. ocr_annotations (OCR标注文件表)

存储人工标注的区域信息。

```javascript
{
  _id: ObjectId,
  annotation_id: String,     // 唯一标注标识符
  file_id: String,           // 关联的文件ID
  annotation_name: String,   // 标注名称
  page_number: Number,       // 页码
  regions: [{
    region_id: String,       // 区域唯一标识符
    region_type: String,     // 区域类型
    bbox: {
      x: Number,
      y: Number,
      width: Number,
      height: Number
    },
    order: Number,           // 阅读顺序
    confidence: Number,      // 标注置信度
    notes: String,           // 标注备注
    created_by: String       // 标注者
  }],
  template_info: {
    is_template: Boolean,    // 是否为模板
    template_name: String,   // 模板名称
    template_description: String,
    applicable_file_types: [String]
  },
  created_at: Date,
  updated_at: Date,
  created_by: String         // 创建者
}
```

### 4. ocr_training_samples (OCR训练样本表)

存储学习系统的训练样本。

```javascript
{
  _id: ObjectId,
  sample_id: String,         // 唯一样本标识符
  file_id: String,           // 关联的文件ID
  result_id: String,         // 关联的OCR结果ID
  region_id: String,         // 关联的区域ID
  region_type: String,       // 区域类型
  region_bbox: {
    x: Number,
    y: Number,
    width: Number,
    height: Number
  },
  ground_truth_text: String, // 人工修正的正确文本
  ocr_predicted_text: String,// OCR识别的文本
  confidence: Number,        // 原始置信度
  preprocessing_params: {    // 使用的预处理参数
    clahe_clip_limit: Number,
    adaptive_threshold_block_size: Number,
    adaptive_threshold_c: Number,
    morphology_kernel_size: [Number, Number],
    morphology_operation: String
  },
  accuracy_metrics: {
    character_accuracy: Number,  // 字符级准确率
    word_accuracy: Number,       // 词级准确率
    edit_distance: Number        // 编辑距离
  },
  image_hash: String,        // 图像区域哈希值
  feedback_source: String,   // 反馈来源: manual, auto_correction
  created_at: Date,
  updated_at: Date
}
```

### 5. ocr_preprocessing_effectiveness (预处理参数效果表)

存储不同预处理参数的效果评估。

```javascript
{
  _id: ObjectId,
  effectiveness_id: String,  // 唯一效果记录标识符
  region_type: String,       // 区域类型
  preprocessing_params: {    // 预处理参数组合
    clahe_clip_limit: Number,
    adaptive_threshold_block_size: Number,
    adaptive_threshold_c: Number,
    morphology_kernel_size: [Number, Number],
    morphology_operation: String
  },
  performance_metrics: {
    accuracy_score: Number,   // 准确率分数
    confidence_score: Number, // 置信度分数
    combined_score: Number,   // 综合分数
    sample_count: Number,     // 样本数量
    avg_processing_time: Number // 平均处理时间
  },
  sample_ids: [String],      // 相关训练样本ID列表
  last_updated: Date,
  created_at: Date
}
```

### 6. ocr_error_patterns (错误模式表)

存储识别的错误模式和统计信息。

```javascript
{
  _id: ObjectId,
  pattern_id: String,        // 唯一错误模式标识符
  region_type: String,       // 区域类型
  error_type: String,        // 错误类型: substitution, insertion, deletion, transposition
  error_pattern: {
    original_text: String,   // 原始文本模式
    predicted_text: String,  // 预测文本模式
    pattern_regex: String,   // 正则表达式模式
    context: String          // 上下文信息
  },
  statistics: {
    frequency: Number,       // 出现频率
    accuracy_impact: Number, // 对准确率的影响
    confidence_impact: Number, // 对置信度的影响
    first_seen: Date,        // 首次发现时间
    last_seen: Date          // 最后发现时间
  },
  related_samples: [String], // 相关训练样本ID
  correction_suggestions: [String], // 修正建议
  created_at: Date,
  updated_at: Date
}
```

### 7. ocr_learning_reports (学习报告表)

存储学习系统生成的报告。

```javascript
{
  _id: ObjectId,
  report_id: String,         // 唯一报告标识符
  report_type: String,       // 报告类型: daily, weekly, monthly, on_demand
  time_range: {
    start_date: Date,
    end_date: Date
  },
  metrics: {
    total_samples: Number,
    accuracy_improvement: Number,
    confidence_improvement: Number,
    processing_time_improvement: Number
  },
  region_performance: [{
    region_type: String,
    sample_count: Number,
    avg_accuracy: Number,
    avg_confidence: Number,
    improvement_rate: Number
  }],
  error_analysis: [{
    error_type: String,
    frequency: Number,
    impact_score: Number,
    trend: String            // increasing, decreasing, stable
  }],
  recommendations: [{
    type: String,            // parameter_adjustment, training_data, error_correction
    description: String,
    priority: String,        // high, medium, low
    estimated_impact: Number
  }],
  generated_at: Date,
  generated_by: String       // system, user_request
}
```

## 索引设计

### 性能优化索引

```javascript
// ocr_files
db.ocr_files.createIndex({ "file_id": 1 }, { unique: true })
db.ocr_files.createIndex({ "file_hash": 1 })
db.ocr_files.createIndex({ "status": 1, "upload_time": -1 })
db.ocr_files.createIndex({ "upload_time": -1 })

// ocr_results
db.ocr_results.createIndex({ "result_id": 1 }, { unique: true })
db.ocr_results.createIndex({ "file_id": 1, "page_number": 1 })
db.ocr_results.createIndex({ "processing_type": 1, "created_at": -1 })
db.ocr_results.createIndex({ "regions.region_type": 1 })

// ocr_annotations
db.ocr_annotations.createIndex({ "annotation_id": 1 }, { unique: true })
db.ocr_annotations.createIndex({ "file_id": 1, "page_number": 1 })
db.ocr_annotations.createIndex({ "template_info.is_template": 1 })

// ocr_training_samples
db.ocr_training_samples.createIndex({ "sample_id": 1 }, { unique: true })
db.ocr_training_samples.createIndex({ "region_type": 1, "created_at": -1 })
db.ocr_training_samples.createIndex({ "file_id": 1, "region_id": 1 })
db.ocr_training_samples.createIndex({ "image_hash": 1 })

// ocr_preprocessing_effectiveness
db.ocr_preprocessing_effectiveness.createIndex({ "region_type": 1, "performance_metrics.combined_score": -1 })
db.ocr_preprocessing_effectiveness.createIndex({ "effectiveness_id": 1 }, { unique: true })

// ocr_error_patterns
db.ocr_error_patterns.createIndex({ "region_type": 1, "error_type": 1 })
db.ocr_error_patterns.createIndex({ "statistics.frequency": -1 })
db.ocr_error_patterns.createIndex({ "pattern_id": 1 }, { unique: true })

// ocr_learning_reports
db.ocr_learning_reports.createIndex({ "report_id": 1 }, { unique: true })
db.ocr_learning_reports.createIndex({ "report_type": 1, "generated_at": -1 })
db.ocr_learning_reports.createIndex({ "time_range.start_date": 1, "time_range.end_date": 1 })
```

## 数据关系

```
ocr_files (1) ←→ (N) ocr_results
ocr_files (1) ←→ (N) ocr_annotations
ocr_results (1) ←→ (N) ocr_training_samples
ocr_annotations (1) ←→ (N) ocr_results
ocr_training_samples (N) ←→ (1) ocr_preprocessing_effectiveness
ocr_training_samples (N) ←→ (N) ocr_error_patterns
```

## 数据一致性保证

1. **文件生命周期管理**：通过`status`字段跟踪文件处理状态
2. **引用完整性**：使用`file_id`、`result_id`等外键关联
3. **版本控制**：通过`created_at`和`updated_at`时间戳
4. **数据去重**：使用`file_hash`和`image_hash`避免重复数据
5. **事务支持**：关键操作使用MongoDB事务保证一致性

## 扩展性考虑

1. **分片策略**：可按`file_id`或时间范围分片
2. **归档策略**：定期归档旧的训练样本和报告
3. **缓存策略**：热点数据使用Redis缓存
4. **备份策略**：定期备份重要集合数据 
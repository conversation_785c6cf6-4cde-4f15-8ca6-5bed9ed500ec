# 更新日志 (简体中文)

本文件记录了 **MCP Feedback Enhanced** 的所有版本更新内容。

## [v2.4.3] - 2025-06-14 - 会话管理重构与音效通知

### 🌟 版本亮点
将会话管理从左侧边栏迁移到独立页签，解决浏览器兼容性问题。新增音效通知系统，支持自定义音效。

### ✨ 新功能
- 🔊 **音效通知系统**: 会话更新时播放音效提醒，支持内建音效和自定义音效上传
- 📚 **会话历史管理**: 本地保存会话记录，支持导出和清理功能
- 💾 **输入框高度记忆**: 自动保存和恢复文字输入框的高度设定
- 📋 **一键复制**: 项目路径和会话ID支持点击复制

### 🚀 改进功能
- 📋 **会话管理重构**: 从左侧边栏迁移到"会话管理"页签，解决小窗口下按钮无法点击的问题
- 🎨 **界面布局优化**: AI摘要区域自动扩展，提交按钮位置调整，移除多余描述文字
- 🌐 **多语言增强**: 新增tooltip和按钮的多语言支持

### 🐛 问题修复
- 修复当前会话详细信息按钮无反应问题
- 修复会话详情弹窗关闭延迟问题
- 修复音效通知语系初始化问题
- 修正自动提交处理逻辑

---

## [v2.4.2] - Web-Only 架构重构与智能功能增强

### 🌟 版本亮点
本版本进行了重大架构重构，**完全移除 PyQt6 GUI 依赖**，转为纯 Web UI 架构，大幅简化部署和维护。同时新增多项智能功能，包括提示词管理、自动提交、会话管理等，全面提升用户体验和工作效率。

### 🔄 重大架构变更
- 🏗️ **完全移除 PyQt6 GUI**: 彻底移除桌面应用程序依赖，简化安装和部署流程
- 🌐 **纯 Web UI 架构**: 统一使用 Web 界面，支持所有平台和环境
- 📦 **依赖大幅简化**: 移除 PyQt6、相关 GUI 库等重型依赖，安装包体积显著减小
- 🚀 **部署更简单**: 无需考虑 GUI 环境配置，适用于所有开发环境

### ✨ 全新功能
- 📝 **智能提示词管理系统**:
  - 常用提示词的 CRUD 操作（新增、编辑、删除、使用）
  - 使用频率统计和智能排序
  - 快速选择和一键应用功能
  - 支持自动提交标记和优先显示
- ⏰ **自动定时提交功能**:
  - 可设定 1-86400 秒的倒数计时器
  - 可视化倒数显示和状态指示
  - 与提示词管理系统深度整合
  - 支持暂停、恢复、取消操作
- 📊 **会话管理与追踪**:
  - 当前会话状态即时显示
  - 会话历史记录和统计分析
  - 今日会话数量和平均时长统计
  - 会话详情查看和管理功能
- 🔗 **连接监控系统**:
  - WebSocket 连接状态即时监控
  - 延迟测量和连接品质指示
  - 自动重连机制和错误处理
  - 详细的连接统计信息
- ⌨️ **快捷键增强**: 新增 Ctrl+I 快速聚焦输入框功能 (感谢 @penn201500)

### 🚀 功能改进
- 🎨 **UI/UX 全面优化**:
  - 新增左侧会话管理面板，支持收合/展开
  - 顶部连接状态栏，即时显示系统状态
  - 响应式设计，适配不同屏幕尺寸
  - 统一的设计语言和视觉风格
- 🌐 **多语言系统增强**:
  - 优化语言切换机制，支持即时切换
  - 新增大量翻译文本，提升本地化覆盖率
  - 改进语言选择器 UI，使用下拉菜单设计
  - 修复语言切换时的显示问题
- 🖼️ **图片设置整合**:
  - 将图片设置从工作区移至设置标签页
  - 统一的设置管理界面
  - 改进设置项目的组织和布局
- 📱 **界面布局优化**:
  - 调整版面配置，符合多语言显示需求
  - 优化按钮样式和间距
  - 改进表单元素的视觉设计
  - 增强可访问性和易用性

### 🐛 问题修复
- 🔧 **会话管理修复**:
  - 修复会话统计信息无法正确更新的问题
  - 修复会话数量计算错误
  - 改进会话状态追踪机制
- 🎯 **提示词功能修复**:
  - 修复常用提示词管理无法正确设置自动提交的问题
  - 改进提示词选择和应用逻辑
- 🌐 **语系切换修复**:
  - 修复语言切换时部分文字未更新的问题
  - 改进多语言文本的加载机制
- 🏗️ **架构稳定性修复**:
  - 修复会话管理初始化问题
  - 改进错误处理和资源清理
  - 优化模块加载顺序和依赖关系

### 🛠️ 技术改进
- 📦 **模块化架构**:
  - JavaScript 代码完全模块化重构
  - 采用 ES6+ 语法和现代化开发模式
  - 清晰的模块分离和职责划分
- 📊 **性能提升**:
  - 优化 WebSocket 通信效率
  - 改进前端资源加载速度
  - 减少内存使用和 CPU 负载

### 📚 文档更新
- 📖 **架构文档更新**: 更新系统架构说明，反映 Web-Only 设计
- 🔧 **安装指南简化**: 移除 GUI 相关安装步骤和依赖说明
- 🖼️ **截图更新**: 更新所有界面截图，展示新的 Web UI 设计
- 📋 **API 文档增强**: 新增提示词管理、自动提交等新功能的 API 说明

---

## [v2.3.0] - 系统稳定性与资源管理增强

### 🌟 亮点
本版本专注于提升系统稳定性和使用体验，特别解决了 Cursor SSH Remote 环境下无法启动浏览器的问题。

### ✨ 新功能
- 🌐 **SSH Remote 环境支持**: 解决 Cursor SSH Remote 无法启动浏览器的问题，提供清晰的使用指引
- 🛡️ **错误提示改善**: 当发生错误时，提供更友善的错误信息和解决建议
- 🧹 **自动清理功能**: 自动清理临时文件和过期会话，保持系统整洁
- 📊 **内存监控**: 监控内存使用情况，防止系统资源不足

### 🚀 改进功能
- 💾 **资源管理优化**: 更好地管理系统资源，提升运行效率
- 🔧 **错误处理增强**: 遇到问题时提供更清楚的说明和解决方案
- 🌐 **连接稳定性**: 改善 Web UI 的连接稳定性
- 🖼️ **图片上传优化**: 改善图片上传功能的稳定性
- 🎯 **自动聚焦输入框**: 反馈窗口开启时自动聚焦到输入框，提升用户体验 (感谢 @penn201500)

### 🐛 问题修复
- 🌐 **连接问题**: 修复 WebSocket 连接的相关问题
- 🔄 **会话管理**: 修复会话状态跟踪的问题
- 🖼️ **图片处理**: 修复图片上传时的事件处理问题

---

## [v2.2.5] - WSL 环境支持与跨平台增强

### ✨ 新功能
- 🐧 **WSL 环境检测**: 自动识别 WSL 环境，提供专门的支持逻辑
- 🌐 **智能浏览器启动**: WSL 环境下自动调用 Windows 浏览器，支持多种启动方式
- 🔧 **跨平台测试增强**: 测试功能整合 WSL 检测，提升测试覆盖率

### 🚀 改进功能
- 🎯 **环境检测优化**: 改进远程环境检测逻辑，WSL 不再被误判为远程环境
- 📊 **系统信息增强**: 系统信息工具新增 WSL 环境状态显示
- 🧪 **测试体验提升**: 测试模式下自动尝试启动浏览器，提供更好的测试体验

---

## [v2.2.4] - GUI 体验优化与问题修复

### 🐛 问题修复
- 🖼️ **图片重复粘贴修复**: 解决 GUI 界面中使用 Ctrl+V 复制粘贴图片时出现重复粘贴的问题
- 🌐 **语系切换修复**: 修复图片设定区域在语言切换时文字没有正确翻译的问题
- 📝 **字体可读性改善**: 调整图片设定区域的字体大小，提升文字可读性

---

## [v2.2.3] - 超时控制与图片设置增强

### ✨ 新功能
- ⏰ **用户超时控制**: 新增可自定义的超时设置功能，支持 30 秒至 2 小时的弹性设置
- ⏱️ **倒数计时器**: 界面顶部显示实时倒数计时器，提供可视化的时间提醒
- 🖼️ **图片大小限制**: 新增图片上传大小限制设置（无限制/1MB/3MB/5MB）
- 🔧 **Base64 兼容模式**: 新增 Base64 详细模式，提升部分 AI 模型的图片识别兼容性
- 🧹 **UV Cache 管理工具**: 新增 `cleanup_cache.py` 脚本，协助管理和清理 UV cache 空间

### 🚀 改进功能
- 📚 **文档结构优化**: 重新整理文档目录结构，将图片移至 `docs/{语言}/images/` 路径
- 📖 **Cache 管理指南**: 新增详细的 UV Cache 管理指南，包含自动化清理方案
- 🎯 **智能兼容性提示**: 当图片上传失败时自动显示 Base64 兼容模式建议

### 🐛 问题修复
- 🛡️ **超时处理优化**: 改进用户自定义超时与 MCP 系统超时的协调机制
- 🖥️ **界面自动关闭**: 修复超时后界面自动关闭和资源清理逻辑
- 📱 **响应式布局**: 优化超时控制组件在小屏幕设备上的显示效果

---

## [v2.2.2] - 超时自动清理修复

### 🐛 问题修复
- 🔄 **超时自动清理**: 修复 GUI/Web UI 在 MCP session timeout (默认 600 秒) 后没有自动关闭的问题
- 🛡️ **资源管理优化**: 改进超时处理机制，确保在超时时正确清理和关闭所有 UI 资源
- ⚡ **超时检测增强**: 加强超时检测逻辑，确保在各种情况下都能正确处理超时事件

---

## [v2.2.1] - 窗口优化与统一设置接口

### 🚀 改进功能
- 🖥️ **窗口大小限制解除**: 解除 GUI 主窗口最小大小限制，从 1000×800 降至 400×300
- 💾 **窗口状态实时保存**: 实现窗口大小与位置的即时保存机制，支持防抖延迟
- ⚙️ **统一设置接口优化**: 改进 GUI 设置版面的配置保存逻辑，避免设置冲突

### 🐛 问题修复
- 🔧 **窗口大小限制**: 解决 GUI 窗口无法调整至小尺寸的问题
- 🛡️ **设置冲突**: 修复设置保存时可能出现的配置冲突问题

---

## [v2.2.0] - 布局与设置界面优化

### ✨ 新功能
- 🎨 **水平布局模式**: GUI 与 Web UI 的合并模式新增摘要与反馈的左右布局选项

### 🚀 改进功能
- 🎨 **设置界面改进**: 优化了 GUI 与 Web UI 的设置页面，提升布局清晰度
- ⌨️ **快捷键完善**: 提交反馈快捷键现已完整支持数字键盘的 Enter 键

### 🐛 问题修复
- 🔧 **图片重复粘贴**: 解决了在 Web UI 文字输入区使用 Ctrl+V 粘贴图片时的重复问题

---

## [v2.1.1] - 窗口定位优化

### ✨ 新功能
- 🖥️ **智能窗口定位**: 新增「总是在主屏幕中心显示窗口」设置选项
- 🌐 **多屏幕支持**: 完美解决 T 字型屏幕排列等复杂多屏幕环境的窗口定位问题
- 💾 **位置记忆**: 自动保存和恢复窗口位置，智能检测窗口可见性

---

## [v2.1.0] - 全面重构版

### 🎨 重大重构
- 🏗️ **全面重构**: GUI 和 Web UI 采用模块化架构
- 📁 **集中管理**: 重新组织文件夹结构，提升维护性
- 🖥️ **界面优化**: 现代化设计和改进的用户体验

### ✨ 新功能
- 🍎 **macOS 界面优化**: 针对 macOS 用户体验进行专项改进
- ⚙️ **功能增强**: 新增设置选项和自动关闭页面功能
- ℹ️ **关于页面**: 新增关于页面，包含版本信息、项目链接和致谢内容

---

## [v2.0.14] - 快捷键与图片功能增强

### 🚀 改进功能
- ⌨️ **增强快捷键**: Ctrl+Enter 支持数字键盘
- 🖼️ **智能图片粘贴**: Ctrl+V 直接粘贴剪贴板图片

---

## [v2.0.9] - 多语言架构重构

### 🔄 重构
- 🌏 **多语言架构重构**: 支持动态载入
- 📁 **语言文件模块化**: 模块化组织语言文件

---

## [v2.0.3] - 编码问题修复

### 🐛 重要修复
- 🛡️ **完全修复中文字符编码问题**: 解决所有中文显示相关问题
- 🔧 **解决 JSON 解析错误**: 修复数据解析错误

---

## [v2.0.0] - Web UI 支持

### 🌟 重大功能
- ✅ **新增 Web UI 支持**: 支持远程环境使用
- ✅ **自动环境检测**: 自动选择合适的界面
- ✅ **WebSocket 即时通讯**: 实现即时双向通讯

---

## 图例说明

| 图标 | 意义 |
|------|------|
| 🌟 | 版本亮点 |
| ✨ | 新功能 |
| 🚀 | 改进功能 |
| 🐛 | 问题修复 |
| 🔄 | 重构变更 |
| 🎨 | 界面优化 |
| ⚙️ | 设置相关 |
| 🖥️ | 窗口相关 |
| 🌐 | 多语言/网络相关 |
| 📁 | 文件结构 |
| ⌨️ | 快捷键 |
| 🖼️ | 图片功能 |
| 📝 | 提示词管理 |
| ⏰ | 自动提交 |
| 📊 | 会话管理 |
| 🔗 | 连接监控 |
| 🏗️ | 架构变更 |
| 🛠️ | 技术改进 |
| 📚 | 文档更新 |

---

**完整项目信息：** [GitHub - mcp-feedback-enhanced](https://github.com/Minidoracat/mcp-feedback-enhanced)

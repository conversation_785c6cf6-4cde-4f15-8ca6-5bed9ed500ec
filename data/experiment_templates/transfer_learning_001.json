{"template_id": "transfer_learning_001", "name": "迁移学习训练", "description": "基于预训练模型进行迁移学习", "method_type": "transfer_learning", "training_config": {"method_type": "transfer_learning", "model_name": "ch_PP-OCRv4_det_pretrained", "dataset_path": "data/training/transfer", "output_path": "models/transfer_output", "epochs": 50, "batch_size": 32, "learning_rate": 0.0001, "optimizer": "<PERSON>", "loss_function": "CrossEntropyLoss", "early_stopping": true, "patience": 10, "validation_split": 0.2, "random_seed": 42, "gpu_enabled": true, "mixed_precision": false, "gradient_clipping": false, "weight_decay": 0.0001, "scheduler": null, "custom_params": {"freeze_backbone": true, "pretrained_model_path": "models/pretrained/ch_PP-OCRv4_det.pdparams"}}, "augmentation_config": {"enabled": true, "rotation_range": 10.0, "width_shift_range": 0.1, "height_shift_range": 0.1, "shear_range": 0.1, "zoom_range": 0.1, "brightness_range": [0.8, 1.2], "contrast_range": [0.8, 1.2], "noise_factor": 0.1, "blur_probability": 0.2, "custom_augmentations": []}, "ensemble_config": {"enabled": false, "method": "voting", "models": [], "weights": null, "meta_learner": null, "voting_strategy": "soft", "combination_rule": "average"}, "resource_config": {"max_memory_gb": 8.0, "max_cpu_cores": 4, "max_gpu_memory_gb": 6.0, "timeout_hours": 24, "checkpoint_interval": 10, "log_level": "INFO"}, "tags": ["transfer_learning", "pretrained", "fine_tuning"], "created_at": "2025-06-11T10:40:17.410130", "updated_at": "2025-06-11T10:40:17.410133", "version": "1.0.0", "author": "system", "is_active": true}
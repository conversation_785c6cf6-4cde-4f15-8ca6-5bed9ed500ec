{"template_id": "ensemble_learning_001", "name": "集成学习训练", "description": "使用多模型集成学习方法", "method_type": "ensemble_learning", "training_config": {"method_type": "ensemble_learning", "model_name": "ensemble_model", "dataset_path": "data/training/ensemble", "output_path": "models/ensemble_output", "epochs": 80, "batch_size": 16, "learning_rate": 0.0005, "optimizer": "<PERSON>", "loss_function": "CrossEntropyLoss", "early_stopping": true, "patience": 10, "validation_split": 0.2, "random_seed": 42, "gpu_enabled": true, "mixed_precision": false, "gradient_clipping": false, "weight_decay": 0.0001, "scheduler": null, "custom_params": {}}, "augmentation_config": {"enabled": true, "rotation_range": 10.0, "width_shift_range": 0.1, "height_shift_range": 0.1, "shear_range": 0.1, "zoom_range": 0.1, "brightness_range": [0.8, 1.2], "contrast_range": [0.8, 1.2], "noise_factor": 0.1, "blur_probability": 0.2, "custom_augmentations": []}, "ensemble_config": {"enabled": true, "method": "weighted", "models": ["ch_PP-OCRv4_det", "ch_PP-OCRv3_det", "en_PP-OCRv4_det"], "weights": [0.5, 0.3, 0.2], "meta_learner": null, "voting_strategy": "soft", "combination_rule": "average"}, "resource_config": {"max_memory_gb": 12.0, "max_cpu_cores": 6, "max_gpu_memory_gb": 6.0, "timeout_hours": 48, "checkpoint_interval": 10, "log_level": "INFO"}, "tags": ["ensemble", "multi_model", "voting", "weighted_fusion"], "created_at": "2025-06-11T10:40:17.411256", "updated_at": "2025-06-11T10:40:17.411258", "version": "1.0.0", "author": "system", "is_active": true}
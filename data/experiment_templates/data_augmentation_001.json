{"template_id": "data_augmentation_001", "name": "数据增强训练", "description": "使用多种数据增强技术进行训练", "method_type": "data_augmentation", "training_config": {"method_type": "data_augmentation", "model_name": "ch_PP-OCRv4_det", "dataset_path": "data/training/augmented", "output_path": "models/augmented_output", "epochs": 150, "batch_size": 24, "learning_rate": 0.001, "optimizer": "<PERSON>", "loss_function": "CrossEntropyLoss", "early_stopping": true, "patience": 10, "validation_split": 0.2, "random_seed": 42, "gpu_enabled": true, "mixed_precision": false, "gradient_clipping": false, "weight_decay": 0.0001, "scheduler": null, "custom_params": {}}, "augmentation_config": {"enabled": true, "rotation_range": 15.0, "width_shift_range": 0.15, "height_shift_range": 0.15, "shear_range": 0.15, "zoom_range": 0.15, "brightness_range": [0.7, 1.3], "contrast_range": [0.7, 1.3], "noise_factor": 0.15, "blur_probability": 0.3, "custom_augmentations": ["elastic_transform", "grid_distortion"]}, "ensemble_config": {"enabled": false, "method": "voting", "models": [], "weights": null, "meta_learner": null, "voting_strategy": "soft", "combination_rule": "average"}, "resource_config": {"max_memory_gb": 8.0, "max_cpu_cores": 4, "max_gpu_memory_gb": 6.0, "timeout_hours": 24, "checkpoint_interval": 10, "log_level": "INFO"}, "tags": ["data_augmentation", "robust_training", "generalization"], "created_at": "2025-06-11T10:40:17.410936", "updated_at": "2025-06-11T10:40:17.410939", "version": "1.0.0", "author": "system", "is_active": true}
# OCR 智能文档处理系统

这是一个基于 OCR 和 AI 的智能文档处理系统，能够识别和处理复杂排版的文档图片，包括混合排版（单栏/多栏）、多语言（中英文）、图表等内容。

## 主要功能

- OCR 图像识别
  - 支持多种图片格式（JPG、PNG、PDF等）
  - 混合排版识别（单栏/多栏）
  - 多语言混排识别（中英文）
  - 表格和图表识别

- AI 文本处理
  - 使用 Ollama 模型进行文本优化
  - 语义检查和纠错
  - 格式规范化

- 文档生成
  - 多种输出格式支持（TXT、Markdown、RTF、DOCX）
  - 自动生成目录和索引
  - 格式化和样式定制

## 技术栈

- OCR: PaddleOCR
- AI 模型: Ollama
- 后端: FastAPI
- 前端: React
- 数据库: SQLite
- 容器化: Docker

## 安装步骤

1. 克隆仓库
```bash
git clone [repository-url]
cd [repository-name]
```

2. 创建并激活虚拟环境
```bash
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
.\venv\Scripts\activate  # Windows
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，填入必要的配置
```

5. 启动开发服务器
```bash
uvicorn src.backend.main:app --reload
```

## 项目结构

```
.
├── src/
│   ├── backend/        # 后端代码
│   ├── frontend/       # 前端代码
│   ├── ocr/           # OCR 处理模块
│   └── models/        # AI 模型和处理
├── docs/              # 文档
├── tests/             # 测试文件
├── requirements.txt   # Python 依赖
└── README.md         # 项目说明
```

## 使用说明

[待补充]

## 贡献指南

[待补充]

## 许可证

[待补充]